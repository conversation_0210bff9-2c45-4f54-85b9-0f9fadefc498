<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="组织名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入组织名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="组织状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索
        </el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
        >重置
        </el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:dept:add']"
        >新增
        </el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="deptList"
      row-key="deptId"
      default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column
        prop="deptName"
        label="组织名称"
        width="260"
      ></el-table-column>
      <el-table-column
        prop="orderNum"
        label="排序"
        width="200"
      ></el-table-column>
      <el-table-column
        prop="status"
        label="状态"
        :formatter="statusFormat"
        width="100"
      ></el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-tickets"
            @click="handleTemplate(scope.row)"
            v-if="scope.row.parentId==1&&isAdmin"
          >短信模版
          </el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:dept:edit']"
          >修改
          </el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
            v-hasPermi="['system:dept:add']"
          >新增
          </el-button
          >
          <el-button
            v-if="scope.row.parentId != 0"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:dept:remove']"
          >删除
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改组织对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="820px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="system-form">
        <el-row>
          <el-col :span="24" v-if="form.parentId !== 0">
            <el-form-item label="上级组织" prop="parentId">
              <treeselect
                @select="blurDept"
                v-model="form.parentId"
                :options="deptOptions"
                :normalizer="normalizer"
                placeholder="选择上级组织"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织名称" prop="deptName">
              <el-input v-model.trim="form.deptName" placeholder="请输入组织名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number
                v-model="form.orderNum"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="leader">
              <el-input
                v-model.trim="form.leader"
                placeholder="请输入负责人"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入联系电话"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model.trim="form.email"
                placeholder="请输入邮箱"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in statusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                >{{ dict.dictLabel }}
                </el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否定制">
              <el-radio-group v-model="customizationFlag" @change="changeCustom">
                <el-radio :label="'1'">是</el-radio>
                <el-radio :label="'0'" :disabled="newDepId == 1">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <template v-if="customizationFlag == '1'">
            <el-col :span="24">
              <el-divider content-position="left">个性化配置</el-divider>
            </el-col>
            <el-col :span="12" style="height: 58px;">
              <el-form-item label="系统名称" prop="title">
                <el-input v-model.trim="form.title" placeholder="请输入系统名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="12" style="height: 58px;">
              <el-form-item label="系统标题" prop="systemName">
                <el-input v-model.trim="form.systemName" placeholder="请输入系统标题"/>
              </el-form-item>
            </el-col>
            <el-col :span="12" style="height: 58px;">
              <el-form-item label="系统标识码" prop="systemId">
                <el-input v-model.trim="form.systemId" @blur="checkId" placeholder="请输入系统标识码"/>
              </el-form-item>
            </el-col>
            <el-col :span="12" style="height: 58px;">
              <el-form-item label="系统链接" prop="systemLink">
                <el-input v-model.trim="form.systemLink" placeholder="请输入系统链接"/>
              </el-form-item>
            </el-col>
            <el-col :span="24" style="height:190px">
              <el-form-item label="标题LOGO" prop="titleLogo">
                <div class="form-logo logo">
                  <ImageUpload ref="logoNavImg" v-model="form.titleLogo" :values="form.titleLogo"/>
                  <el-button type="primary" @click="changeNavLogo">使用上级</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24" style="height:190px">
              <el-form-item label="LOGO" prop="logo">
                <div class="form-logo logo">
                  <ImageUpload ref="logoImg" v-model="form.logo" :values="form.logo"/>
                  <el-button type="primary" @click="changeLogo">使用上级</el-button>  
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="登录背景图" prop="loginBg1">
                <div class="form-logo">
                  <ImageUpload ref="loginBg" v-model="form.loginBg1" :file-size="20"/>
                  <el-button type="primary" @click="changeLoginBg">使用上级</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="登录左侧图案" prop="loginBg2">
                <div class="form-logo">
                  <ImageUpload ref="loginFront" v-model="form.loginBg2" :file-size="20"/>
                  <el-button type="primary" @click="changeLoginBgUrl">使用上级</el-button>
                </div>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <el-dialog title="选择短信模板" :visible.sync="templateDialog" width="30%" append-to-body>
      短信模板：
      <el-select size="small" v-model="templateId" placeholder="请选择短信模板">
        <el-option v-for="item in templateList" :label="item.templateName" :value="item.id" :key="item.id"/>
      </el-select>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="templateSubmit">确 定</el-button>
        <el-button @click="templateDialog=false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImageUpload from '@/components/ImageUpload/index.vue'
import {
  addDept,
  checkSystemId,
  delDept,
  getDept,
  listDept,
  listDeptExcludeChild,
  parentDeptInfo,
  saveDeptMessagetApi,
  updateDept
} from "@/api/system/dept";
import {templateListApi} from "@/api/system/warnTemplate";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Dept",
  components: {Treeselect, ImageUpload},
  computed: {
    isAdmin() {
      return this.$store.state.user.roles.includes('admin')
    }
  },
  data() {
    return {
      systemId: '',
      loginBgInheritFlag: '0',
      logoFlag: '0',
      loginBgFlag: '0',
      parentInfo: {},
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表格树数据
      deptList: [],
      // 组织树选项
      deptOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 查询参数
      queryParams: {
        deptName: undefined,
        status: undefined,
      },
      customizationFlag: '0',
      newDepId: '',
      // 表单参数
      form: {
      logo:'', titleLogo: '', loginBg1: '', loginBg2: '', systemName: '', title: '', systemLink: '', systemId: ''
      },
      // 表单校验
      rules: {
        parentId: [
          {required: true, message: "上级组织不能为空", trigger: "blur"},
        ],
        deptName: [
          {required: true, message: "组织名称不能为空", trigger: "blur"},
        ],
        orderNum: [
          {required: true, message: "显示排序不能为空", trigger: "blur"},
        ],
        title: [
          {required: true, message: "系统名称不能为空", trigger: "blur"},
        ],
        systemName: [
          {required: true, message: "系统标题不能为空", trigger: "blur"},
        ],
        systemId: [
          {required: true, message: "系统标识码不能为空", trigger: "blur"},
        ],
        titleLogo: [
          {required: true, message: "请上传标题LOGO", trigger: "blur"},
        ],
        logo: [
           { required: true, message: "请上传LOGO", trigger: "blur" },
        ],
        loginBg1: [
          {required: true, message: "请上传登录背景图", trigger: "blur"},
        ],
        loginBg2: [
          {required: true, message: "请上传登录左侧图案", trigger: "blur"},
        ],
        email: [
          {
            type: "email",
            message: "'请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],
        phone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
      templateDialog: false,
      templateList: [],
      templateId: '',
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_normal_disable").then((response) => {
      this.statusOptions = response.data;
    });
    this.getTemplateList()
  },
  methods: {
    checkId() {
      if (this.form.deptId && this.form.systemId == this.systemId) {
        return false
      }
      checkSystemId({systemId: this.form.systemId}).then((res) => {
        if (!res.data) {
          this.$message.warning('系统标识码已存在，请重新输入！')
          this.form.systemId = ''
        }
      })

    },
    blurDept(val) {
      this.newDepId = val.deptId
      if (this.newDepId == 1) {
        this.customizationFlag = '1'
        this.queryParentInfo(this.newDepId)
      } else {
        this.customizationFlag = '0'
      }
    },
    // 定制切换
    changeCustom(val) {
      if (this.title == "添加组织") {
        if (val == '1' && !this.form.parentId) {
          this.$message.warning('请先选择上级组织')
          this.customizationFlag = '0'
        } else {
          this.queryParentInfo(this.form.parentId)
        }
      }
    },
      // 图片赋值
    changeNavLogo(){
      let param = JSON.parse(JSON.stringify(this.parentInfo))
      this.form.titleLogo = param.titleLogo
      this.$refs.logoNavImg.setValue(this.form.titleLogo)
    },
    changeLogo(){
        let param = JSON.parse(JSON.stringify(this.parentInfo))
        this.form.logo = param.logo
        this.$refs.logoImg.setValue(this.form.logo)
    },
    changeLoginBg() {
      let param = JSON.parse(JSON.stringify(this.parentInfo))
      this.form.loginBg1 = param.loginBg1
      this.$refs.loginBg.setValue(this.form.loginBg1)
    },
    changeLoginBgUrl() {
      let param = JSON.parse(JSON.stringify(this.parentInfo))
      this.form.loginBg2 = param.loginBg2
      this.$refs.loginFront.setValue(this.form.loginBg2)
    },
    // 获取父级定制信息
    queryParentInfo(id) {
      parentDeptInfo({deptId: id}).then((res) => {
        this.parentInfo = res.data
      })
    },
    /** 查询组织列表 */
    getList() {
      this.loading = true;
      listDept(this.queryParams).then((response) => {
        this.deptList = this.handleTree(response.data, "deptId");
        this.loading = false;
      });
    },
    /** 转换组织数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    // 字典状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        deptId: undefined,
        parentId: undefined,
        deptName: undefined,
        orderNum: undefined,
        leader: undefined,
        phone: undefined,
        email: undefined,
        status: "0",
        systemId: undefined,
        systemName: undefined,
        systemLink: undefined,
        loginBg1: undefined,
        loginBg2: undefined,
        titleLogo: undefined,
        title: undefined,
        logo: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if (row != undefined) {
        this.form.parentId = row.deptId;
      }
      this.open = true;
      this.title = "添加组织";
      this.customizationFlag = '0'
      listDept().then((response) => {
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.systemId = row.systemId
      this.reset();
      if (this.form.parentId != 0) {
        this.queryParentInfo(row.parentId)
      } else {
        this.parentInfo = {}
      }
      getDept(row.deptId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改组织";
        this.customizationFlag = '1'
        this.$nextTick(() => {
          this.$refs.logoImg.setValue(this.form.logo)
          this.$refs.logoNavImg.setValue(this.form.titleLogo)
          this.$refs.loginBg.setValue(this.form.loginBg1)
          this.$refs.loginFront.setValue(this.form.loginBg2)
        })
      });
      listDeptExcludeChild(row.deptId).then((response) => {
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.deptId != undefined) {
            updateDept(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDept(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm(
        '是否确认删除名称为"' + row.deptName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delDept(row.deptId);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 查询模板列表 */
    getTemplateList() {
      let params = {
        pageSize: 999,
        status: '0'
      }
      templateListApi(params).then(response => {
        this.templateList = response.rows;
      });
    },
    //短信模版
    handleTemplate(row) {
      this.templateDialog = true
      this.deptId = row.deptId
      this.templateId = String(row.messageTemplateId || '')
    },
    //模版绑定提交
    templateSubmit() {
      let params = {
        deptId: this.deptId,
        templateId: this.templateId
      }
      saveDeptMessagetApi(params).then(res => {
        if (res.code == 200) {
          this.msgSuccess("选择成功")
          this.templateDialog = false
          this.getList();
        }
      })
    }
  },
};
</script>
<style scoped lang="scss">
.form-logo {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;

  .el-button {
    margin-left: 20px;
  }
}
</style>
