<template>
  <div :class="`data-container ${treeFlag?'':'collapse'}`">
    <div class="dataSidebar">
      <div class="tree-button" @click="treeFlag=!treeFlag">
        <img src="@/assets/images/retract.svg" alt="">
      </div>
      <dataSide class="dataSide" ref="taskTreeRef" @checkNode="checkTreeNode"></dataSide>
    </div>
    <div class="dataCont" v-show="checkedNode.id" ref="anchorRef" @scroll="analysishandleScroll">
      <div class="dataMonit">
        <div class="monit-data">
          <el-tabs v-model="activeName" :before-leave="beforeTabLeave">
            <el-tab-pane :label="item.name" :name="item.sort" v-for="item in tabList" :key="item.sort"
                         v-loading="tabLoading">
              <div/>
            </el-tab-pane>
            <!-- <el-tab-pane label="统计分析" name="2">
              <div />
            </el-tab-pane>
            <el-tab-pane label="预警设置" name="5">
              <div />
            </el-tab-pane>
            <el-tab-pane label="定向信源" name="4">
              <div />
            </el-tab-pane>
            <el-tab-pane label="方案设置" name="3">
              <div />
            </el-tab-pane> -->
          </el-tabs>
          <i class="el-icon-setting" @click="setSelf"></i>
        </div>
      </div>
      <!-- 自定义标题 -->
      <CustomTable ref="custom" selfTitle="自定义设置" :loading.sync="subLoading" :list.sync="list" :fold.sync="fold"
                   :foldHis.sync="foldHis"
                   :hisList.sync="hisList" :visible.sync="dialogVisible" @submits="submitField"></CustomTable>

      <div class="dataMain_top" v-show="activeName != '4'">
        <div class="dataMain_top_colorBlock"/>
        <div class="dataMain_top_title">{{ checkedNode.name ||'' }}</div>
        <div class="iconGroup">
          <img v-if="isInclude(checkedNode.planMain,'1')" src="@/assets/images/collection.svg" alt="">
          <img v-if="isInclude(checkedNode.planMain,'2')" src="@/assets/images/provinceIcon.svg" style="height: 22px;"
               alt="">
          <img v-if="isInclude(checkedNode.planMain,'3')" src="@/assets/images/countryIcon.svg" alt="">
        </div>
        <div class="collapseButton">
          <div class="retract" v-show="activeName=='2'"><i class="el-icon-document"></i>
            <el-button type="text" @click="getReport" :disabled="!canClick">生成报告</el-button>
          </div>
        </div>
        <div class="collapseButton">
          <div class="retract" v-show="showContent&&activeName=='1'" @click="showContent=!showContent"><i
            class="el-icon-arrow-up"></i><span>条件收起</span></div>
          <div class="retract" v-show="!showContent&&activeName=='1'" @click="showContent=!showContent"><i
            class="el-icon-arrow-down"></i><span>条件展开</span></div>
        </div>
      </div>
      <div class="dashedLine" v-show="showContent||activeName!='1'"></div>
      <div v-show="activeName == '1'" class="dataMain">
        <opinionData ref="opinionDataRef" :treeFlag="treeFlag" :show="activeName == '1'" :checkedNode="checkedNode"
                     :fold="opinionFold" @changeSearchForm="changeSearchForm"/>
      </div>
      <div v-show="typeName=='second'&&activeName == '2'" class="dataMain">
        <analysis :fixRight="40" ref="analysisRef" :type="typeName" :queryForm="queryForm" :checkedNode="checkedNode"
                  :isFilter="true"
                  @allChartimg="allChartimg" @getCanClick="getCanClick" @getAnalysisData="getAnalysisData"></analysis>
      </div>
      <div v-show="activeName == '3'" class="dataMain">
        <!-- <div class="dataMain_top">
          <div class="dataMain_top_colorBlock" />
          <div class="dataMain_top_title">{{ this.checkedNode.name ||'' }}</div>
        </div> -->
        <div class="dataMain_content" v-loading='addFormLoading'>
          <addProgram ref="addProgramRef"></addProgram>
          <div style="text-align: center;">
            <span class="dialog-footer">
              <el-button v-show="checkedNode.historyFlag=='0'" type="primary" :loading="btnLoading"
                         @click="submitForm">确定</el-button>
              <el-button @click="cancelForm">取消</el-button>
            </span>
          </div>
        </div>
      </div>
      <div v-show="activeName == '4'" class="dataMain">
        <directSource ref="directSource" :show="activeName == '4'" :checkedNode="checkedNode"></directSource>
        <directSource ref="directExcludeSource" :show="activeName == '4'" :checkedNode="checkedNode" :isExclude="true"
                      style="border-top: 20px solid #F4F7F9;"></directSource>
      </div>
      <div v-show="activeName == '5'" class="dataMain">
        <monitorSetting ref="monitorSettingRef" :checkedNode="checkedNode"></monitorSetting>
      </div>
      <div v-show="activeName == '6'" class="dataMain">
        <eventContext ref="eventContextRef" :checkedNode="checkedNode"></eventContext>
      </div>
      <div v-show="activeName == '7'" class="dataMain">
        <reportSetting ref="reportSettingRef" :checkedNode="checkedNode"></reportSetting>
      </div>
    </div>
    <div class="dataCont" v-show="!checkedNode.id">
      <el-empty style="height: 100%;background-color: #fff;" description="无方案，请先创建监测方案"></el-empty>
    </div>
    <!-- 报告 -->
    <el-dialog
      title="生成报告"
      :visible.sync="dialogVisibleReport"
      width="60%">
      <div slot="title">
        <span style="margin-right:25%">生成报告</span>
        <!-- <span>报告生成约需一分钟</span> -->
      </div>
      <analysisReport :analysisProps="analysisData" :visible.sync="dialogVisibleReport"/>
      <!-- <div style="text-align: center;">
        <el-button type="primary" size="mini" v-if="showBtn" @click="downReport">生成</el-button>
        <div v-else><el-button type="info" size="mini">生成</el-button>  当前事件正在生成文件，请稍后....</div>
      </div> -->
      <!-- <div>
        <div><span style="color:#247cFF;">统计分析报告 </span> <i class="el-icon-download"></i>下载</div>
      </div> -->
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleReport = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisibleReport = false">确 定</el-button>
      </span> -->
      <div style="text-align: center;">
        <el-button @click="dialogVisibleReport = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-backtop target=".dataCont" :right="10"></el-backtop>
  </div>
</template>


<script>
import dataSide from './components/dataSide/index.vue'
import addProgram from './components/addProgram/index.vue'
import directSource from '@/views/publicOpinionMonitor/components/directSource/index.vue'
import monitorSetting from '@/views/publicOpinionMonitor/components/monitorSetting/index.vue'
import reportSetting from '@/views/publicOpinionMonitor/components/reportSetting/index.vue'
import analysis from '@/views/fullSearch/analysis.vue'
import opinionData from '@/views/fullSearch/opinionData.vue'
import eventContext from '@/views/publicOpinionMonitor/components/eventContext/index.vue'

import analysisReport from '@/views/fullSearch/analysisReport.vue'

import CustomTable from '@/components/CustomTable/index.vue'
import {downPostBlobFile} from '@/utils/index'
import {
  addPlanApi,
  planDetailApi,
  updatePlanApi,
  getUserHabits,
  saveUserHabits
} from "@/api/publicOpinionMonitor/index.js";

export default {
  data() {
    return {
      baseAllImg: undefined,
      canClick: false,
      summary: '',
      showBtn: false,
      dialogVisibleReport: false,
      dialogVisible: false,
      subLoading: false,
      tabList: [],
      tabLoading: false,
      list: [],
      activeName: null,
      typeName: '0',
      btnLoading: false,//方案设置确认按钮loading
      checkedNode: {},
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        keyWord1: '',
        keyWord2: '',
        startTime: '',
        endTime: '',
        isOriginal: true,
        sort: 3,
        emotionFlag: '1',
        noSpam: 0,
        type: [],
        searchPosition: 0,
        videoHost: [],
        contentForm: [],
        forward: [],
        accountLevel: [],
        accountAreaCode: ''
      },
      addFormLoading: false,//方案设置loading
      treeFlag: true,
      fold: false,
      foldHis: false,
      opinionFold: false,
      hisList: [],
      showContent: false,
      analysisData: {},
      noInit:false,
    }
  },
  components: {
    dataSide,
    addProgram,
    directSource,
    analysis,
    opinionData,
    CustomTable,
    monitorSetting,
    eventContext,
    reportSetting,
    analysisReport
  },
  watch: {
    activeName(val) {
      if (this.checkedNode.id) {
        this.typeName = '0'
        if (val == '1') {
          if(!this.noInit){
            this.$refs['opinionDataRef'].init()
            this.noInit = false
          }else{
            this.noInit = false
          }
          this.$refs['opinionDataRef'].titleFixed = false
        } else if (val == '2') {
          this.$nextTick(() => {
            this.typeName = 'second'
            this.$refs.analysisRef.initializeOffsets()
            this.canClick = false
          })
        } else if (val == '3') {
          this.getPlanDetail()
        } else if (val == '4') {
          this.$refs['directSource'].init()
          this.$refs['directExcludeSource'].init()
        } else if (val == '5') {
          this.$refs['monitorSettingRef'].init()
        } else if (val == '6') {
          this.$refs['eventContextRef'].init()
        } else if (val == '7') {
          this.$refs['reportSettingRef'].init()
        }
        // 不在舆情数据tab页时，停止轮询
        if (val != '1') {
          this.$refs['opinionDataRef'].stopInterval()
        }
      }
    },
    showContent(val) {
      this.opinionFold = !val
    }
  },
  created() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.queryHabit('first')
      }, 200);
    })
  },
  mounted() {
    window.addEventListener('scroll', this.analysishandleScroll);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.analysishandleScroll);
  },
  methods: {
    // 生成报告
    getReport() {
      this.dialogVisibleReport = true
      this.showBtn = true
    },
    downReport() {
      this.showBtn = false
      let params = JSON.parse(JSON.stringify(this.queryForm))
      // params.accountLevel = params.accountLevel.join(',')
      params.contentForm = params.contentForm.join(',')
      params.forward = params.forward.join(',')
      params.type = params.type.join(',')
      params.videoHost = params.videoHost.join(',')
      setTimeout(() => {
        const resParams = {
          ...params,
          data: {...this.baseAllImg, summary: this.summary}
        }
        downPostBlobFile('/analyse/createReport', resParams, '统计分析报告', () => {
          this.$message.success('下载成功')
          this.dialogVisibleReport = false
        })
      }, 4000)
    },
    getCanClick(boll) {
      this.canClick = boll
    },
    allChartimg(val, summary) {
      this.baseAllImg = val
      this.summary = summary
    },
    // 滚动监听器
    analysishandleScroll() {
      if (this.typeName == 'second') {
        this.$nextTick(() => {
          const scrollTop = this.$refs.anchorRef ? this.$refs.anchorRef.scrollTop : window.scrollY;
          this.$refs.analysisRef.handleScroll(scrollTop)
        })
      }

    },
    queryHabit(type) {
      this.tabLoading = true
      getUserHabits().then((res) => {
        this.list = res.data.now.list
        this.hisList = res.data.history.list
        this.tabLoading = false
        if (this.checkedNode.historyFlag == '1') {
          this.tabList = JSON.parse(JSON.stringify(res.data.history.list))
          this.foldHis = res.data.history.fold
          this.opinionFold = JSON.parse(JSON.stringify(res.data.history.fold))
          this.showContent = !res.data.history.fold
        } else {
          this.tabList = JSON.parse(JSON.stringify(res.data.now.list))
          this.fold = res.data.now.fold
          this.opinionFold = JSON.parse(JSON.stringify(res.data.now.fold))
          this.showContent = !res.data.now.fold
        }
        if (type == 'first' && this.$route.query?.toHistoryFlag) {
          // 数据概览-热点事件-重点关注事件 跳转
          this.activeName = '2'
        } else {
          this.activeName = this.tabList[0]?.sort
        }
      }).catch(() => {
        this.tabLoading = false
      })
    },
    setSelf() {
      this.dialogVisible = true
    },
    // 设置自定义字段
    submitField() {
      saveUserHabits({
        now: {list: this.list, fold: this.fold},
        history: {list: this.hisList, fold: this.foldHis}
      }).then((res) => {
        this.subLoading = false
        this.dialogVisible = false
        this.$message.success('操作成功')
        this.queryHabit()
      }).catch(() => {
        this.subLoading = false
      })
    },
    //进入统计分析tab页前，对当前表单的时间段完整性进行校验
    beforeTabLeave(activeName, oldActiveName) {
      if (activeName == '2' && !this.$refs['opinionDataRef'].checkTime()) {
        this.$message.error('请在舆情数据条件中输入完整的时间范围')
        return false
      }
    },
    checkTreeNode(data) {
      this.typeName = '0'
      this.checkedNode = data
      document.title = this.checkedNode?.name || '舆情监测'
      this.returnTop()
      // this.activeName = this.tabList[0]?.sort
      //历史事件第一个tab为定向信源时，不显示，切换到第二个
      if (this.checkedNode.historyFlag == '1') {
        this.tabList = this.hisList
        this.opinionFold = this.foldHis
      } else {
        this.tabList = this.list
        this.opinionFold = this.fold
      }
      // this.activeName = this.tabList[0]?.sort
      if (this.$route.query?.toHistoryFlag) {
        // 数据概览-热点事件-重点关注事件 跳转
        this.activeName = '2'
      } else {
        this.activeName = this.tabList[0]?.sort
      }
      if (this.activeName=='1'){
        this.noInit = true
      }
      // if(this.activeName=='1'){//当前在舆情数据tab时，切换树节点，获取保存的条件、更新数据
      //   this.$nextTick(() => {
      //     this.$refs['opinionDataRef'].getFilter()
      //   })
      // }
      if (this.activeName == '2') {//当前在统计分析tab时，切换树节点更新数据
        this.$nextTick(() => {
          this.typeName = 'second'
        })
      }
      if (this.activeName == '3') {//当前在方案设置tab时，切换树节点更新数据
        this.getPlanDetail()
      }
    },
    // 取消按钮
    cancelForm() {
      this.activeName = this.tabList[0].sort||'1'
      this.$refs['addProgramRef'].reset()
    },
    // 提交按钮
    submitForm() {
      this.$refs['addProgramRef'].$refs['form'].validate((valid) => {
        if (valid) {
          let taskForm = JSON.parse(JSON.stringify(this.$refs['addProgramRef'].taskForm))
          let searchMode = JSON.parse(JSON.stringify(this.$refs['addProgramRef'].searchMode))

          //高级模式-监测关键词去除符号后的长度
          let highMonitorWordLength = taskForm?.highMonitorWord?.replace(/[+|\(|\)|]/g, '')?.length || 0

          //至少一个关键词
          if ((searchMode == '0' && !taskForm.kw1 && !taskForm.kw2 && !taskForm.kw3) || (searchMode == '1' && !highMonitorWordLength)) {
            this.$message({
              message: '请输入至少一个监测关键词',
              type: 'warning'
            })
            return
          }
          let params = taskForm
          console.log('taskForm', taskForm)
          params.area = taskForm.area?.join(',')
          params.highArea = taskForm.highArea?.join(',')
          params.searchMode = searchMode

          params.mainType = '1' //重点关注

          //提交时同步转化快速模式和高级模式
          if (searchMode == '0') {
            params.highArea = params.area
            params.highExcludeWord = params.excludeWord || ''

            // 确保kw1、kw2、kw3存在且不为空字符串再进行处理
            const kw1Processed = params.kw1 ? `(${params.kw1.replace(/\s/g, '|')})` : '';
            const kw2Processed = params.kw2 ? `(${params.kw2.replace(/\s/g, '|')})` : '';
            const kw3Processed = params.kw3 ? `(${params.kw3.replace(/\s/g, '|')})` : '';
            // 根据处理后的变量拼接最终的highMonitorWord，这样可以避免多余的'+('或')'
            params.highMonitorWord = `${kw1Processed ? kw1Processed + '+' : ''}${kw2Processed ? kw2Processed + '+' : ''}${kw3Processed ? kw3Processed + '+' : ''}`.replace(/\+$/, ''); // 移除开头可能存在的多余 '+' 符号
          } else {
            params.area = params.highArea
            params.excludeWord = params.highExcludeWord || ''
            params.kw1 = ''
            params.kw2 = ''
            params.kw3 = ''
          }
          console.log('params', params)

          this.btnLoading = true

          updatePlanApi(params).then((res) => {
            this.btnLoading = false
            this.$message({message: '修改成功', type: 'success'})
            this.activeName = this.tabList[0].sort||'1'
            this.$refs['taskTreeRef'].getTreeData('route')
          }).catch((err) => {
            this.btnLoading = false
            console.log('err', err)
          })
        } else {
          this.$message({
            message: '表单校验未通过',
            type: 'error'
          })
        }
      })

    },
    //获取方案详情
    getPlanDetail() {
      this.addFormLoading = true
      planDetailApi({planId: this.checkedNode.id}).then(res => {
        let formdata = res.data
        formdata.area = formdata.area ? formdata.area.split(',') : []
        formdata.highArea = formdata.highArea ? formdata.highArea.split(',') : []
        formdata.typeId = '-2'
        this.$refs['addProgramRef'].searchMode = String(formdata.searchMode)
        this.$refs['addProgramRef'].taskForm = formdata
        this.$refs['addProgramRef'].setTypeName()
        this.addFormLoading = false
      }).catch(err => {
        this.addFormLoading = false
      })
    },
    //同步舆情数据组件中的搜索条件
    changeSearchForm(form) {
      this.queryForm = form
    },
    isInclude(array, who) {
      if (!Array.isArray(array)) return false;

      for (let i = 0; i < array.length; i++) {
        if (array[i].mainType === who) {
          return true;
        }
      }

      return false;
    },
    returnTop() {
      document.querySelector('.dataCont').scrollTop = 0
    },
    getAnalysisData(data) {
      this.analysisData = data
    }
  }
}
</script>
<style scoped lang="scss">
@import "./index.scss";
</style>
