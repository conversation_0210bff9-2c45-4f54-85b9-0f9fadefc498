<template>
  <div class="expend">
    <div class="nav">
      <div class="nav-box">{{ navData.title }}</div>
      <div class="nav-box">{{ navData.name }}</div>
      <div class="nav-box">文章数{{ total }}条</div>
    </div>
    <div class="search-wrap">
      <div class="title"><span>筛选条件</span></div>
      <el-form :inline="true" :model="formInline" size="small" class="demo-form-inline">
        <el-form-item label="监测时间:">
          <el-date-picker type="datetime" v-model="navData.startTime" value-format="yyyy-MM-dd HH:mm:ss"
                          :picker-options="startOption" placeholder="开始日期" @change="changeTime('start')">
          </el-date-picker>
          -
          <el-date-picker type="datetime" v-model="navData.endTime" value-format="yyyy-MM-dd HH:mm:ss"
                          :picker-options="endOption" placeholder="结束日期" @change="changeTime('end')">
          </el-date-picker>
          <!-- <span style="color:#666">{{startTimeFormatted}} - {{endTimeFormatted}}</span> -->
        </el-form-item>
        <el-form-item label="信息属性:" label-width="100px">
          <el-select v-model="formInline.selectedValues"
                     collapse-tags
                     multiple
                     @remove-tag="removeTag"
                     @change="handleSelectChange">
            <el-option label="全部" value="all" @click.native="selectAll"></el-option>
            <el-option
              v-for="(item,index) in emotionData"
              :key="index"
              :label="item.name"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="信息排序:" label-width="100px">
          <el-select v-model="formInline.sort">
            <el-option
              v-for="(item,index) in msgData"
              :key="index"
              :label="item.name"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否去重:" label-width="100px">
          <el-select v-model="formInline.isOriginal">
            <el-option
              v-for="(item,index) in articleData"
              :key="index"
              :label="item.name"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="媒体类型:" label-width="100px" v-if="navData.title == '辖区总览'">
          <el-select v-model="formInline.type">
            <el-option
              v-for="(item,index) in mediaList"
              :key="index"
              :label="item.dictLabel"
              :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="事件名称:" label-width="100px" v-if="navData.title == '关注'">
          <el-select v-model="formInline.planId">
            <el-option
              v-for="(item,index) in caseData"
              :key="index"
              :label="item.planName"
              :value="item.planId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号/网站:" label-width="100px" v-if="navData.title == '活跃账号'&&accountName=='全部'">
          <el-select v-model="queryForm.author" @change="changeAccount">
            <el-option
              label="全部"
              value="全部"></el-option>
            <el-option
              v-for="(item,index) in accountData"
              :key="index"
              :label="item"
              :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="热词:" label-width="100px" v-if="navData.title == '热词云'&&accountName=='全部'">
          <el-select v-model="queryForm.hotWordCloud" @change="changeAccount">
            <el-option
              label="全部"
              value="全部"></el-option>
            <el-option
              v-for="(item,index) in accountData"
              :key="index"
              :label="item"
              :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="辖区:" label-width="100px" v-if="navData.title == '辖区总览'&&accountName=='全部'">
          <el-select v-model="queryForm.areaOverview" @change="changeAccount">
            <el-option
              label="全部"
              value="全部"></el-option>
            <el-option
              v-for="(item,index) in accountData"
              :key="index"
              :label="item"
              :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="重点账号:" label-width="100px"
                      v-if="navData.title == '重点账号动态'&&accountName=='全部'||navData.title == '重点账号'&&accountName=='全部'">
          <el-select v-model="pointName" @change="changeAccount">
            <el-option
              label="全部"
              value="全部"></el-option>
            <el-option
              v-for="(item,index) in accountData"
              :key="index"
              :label="item.name"
              :value="`${item.name}-${item.type}-${item.videoHost}`"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="search-btn">
        <el-button type="primary" @click="submitSearch('query')">查询</el-button>
      </div>
    </div>
    <div class="wrap-content">
      <div class="data-table" ref="data_table">
        <div :class="titleFixed?'dataTot is_data_fixed':'dataTot'">
          <!-- <div id="topAnchor" class="totalNum">
            <div v-if="totalLoading">
              刷新中
            </div>
            <div v-else>
              已有
              <span class="data-number">{{ showTotal || 0 }}</span>
              条数据更新
            </div>
            <i class="el-icon-refresh" @click="startInterval()"></i>
          </div> -->
          <div class="mt-4 dataSel">
                    <span v-show="total>0">
                        <el-select size="small" v-model="exportNum" placeholder="选择导出条数" style="width: 125px"
                                   clearable
                                   @change="exportNumChange">
                        <el-option label="选择当前页" value="0"/>
                        <el-option label="前500条" value="500"/>
                        <el-option label="前1000条" value="1000"/>
                        <el-option label="前5000条" value="5000"/>
                        </el-select>
                      <!-- <i v-if="!downloadLoading" class="el-icon-bottom export-download" @click="exportExcel()"></i> -->
                        <img src="@/assets/images/exportIcon.png" v-if="!downloadLoading" class="exportImg"
                             @click="exportExcel()" alt="">
                        <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;"
                           class="el-icon-loading"></i>
                    </span>
            <span></span>
            <div class="dataSel-left">
              <div class="jump-page">
                <i class="el-icon-arrow-left" @click="goLeft"></i>
                <el-input-number size="mini" @change="submitSearch()" v-model="queryForm.pageNum" :max="totalPage"
                                 :min="1" placeholder="请输入内容"></el-input-number>
                <span class="jump-line">/</span>
                <span>{{ totalPage }}</span>
                <i class="el-icon-arrow-right" @click="goRight"></i>
              </div>
              <el-input v-model="queryForm.keyWord2" size="small" clearable placeholder="在结果中搜索，支持单个词组"
                        class="input-with-select">
                <template #prepend>
                  <el-select size="small" v-model="queryForm.searchPosition" placeholder="Select" style="width: 90px">
                    <el-option label="按全文" :value="0"/>
                    <el-option label="按标题" :value="1"/>
                    <el-option label="按正文" :value="2"/>
                    <el-option label="按作者" :value="3"/>
                  </el-select>
                </template>
              </el-input>
              <el-button size="small" :loading="searchLoading" icon="el-icon-search" type="primary" class="search-btns"
                         @click="submitSearch('search')">
                搜索
              </el-button>
            </div>
          </div>
        </div>
        <div class="dataTable">
          <el-table ref="tableRef" v-loading="tableLoading" :data="tableData" border
                    style="width: 100%" :header-cell-style="{background:'#fcfcfd'}"
                    :class="titleFixed?'result-fiexd':''"
                    @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center"></el-table-column>
            <el-table-column prop="title" label="标题" align="left" header-align="center">
              <template #default="scope">
                <div :class="scope.row.isRead==1?'tableItemTitle cover-column':'tableItemTitle'">
                  <div class="tableTitle" @click="goDetail(scope.row)">
                    <img class="tableItemImg" :src="transImage(scope.row.type,scope.row.host||'')" alt="无图片"/>
                    <el-tooltip placement="top" effect="light" raw-content>
                      <div slot="content">
                        <div v-html="scope.row.title"></div>
                      </div>
                      <div class="tableTitleSpan">
                        <span>{{ (queryForm.pageNum - 1) * queryForm.pageSize + scope.$index + 1 }}. </span>
                        <span v-html="scope.row.title"></span>
                      </div>
                    </el-tooltip>
                    <el-select v-model="scope.row.emotionFlag"
                               :class="scope.row.emotionFlag==2?'emotionSelect table-nosense':scope.row.emotionFlag==1?'emotionSelect table-sense':'emotionSelect table-neutral'"
                               size="mini" placeholder="请选择" @change="(val)=>{changeSensitive(val,scope.row)}">
                      <el-option :key="2" label="非敏感" :value="2"></el-option>
                      <el-option :key="1" label="敏感" :value="1"></el-option>
                      <el-option :key="0" label="中性" :value="0"></el-option>
                    </el-select>
                    <p class="article-type" style="background-color: #339593" v-show="scope.row.isOriginal">原创</p>
                    <p class="article-type" v-show="!scope.row.isOriginal">转载</p>
                    <p class="article-type" style="background-color: #F7B8B3;color: #FA2C1C;"
                       v-show="scope.row.warned==1">流转中</p>
                    <p class="article-type" style="background-color: #B2E8F3;color: #00B4D8;"
                       v-show="scope.row.deal==1">已处置</p>
                    <p class="article-type" style="background-color: #F9D0AC;color: #F87500;"
                       v-show="scope.row.follow==1">已重点关注</p>
                    <p class="article-type" style="background-color: #D8D8D8;color: #999999;"
                       v-if="scope.row.urlAccessStatus==0">已删除</p>
                    <p class="article-type" style="background-color: #D5F8D1;color: #3EC82F;" v-else>可访问</p>
                    <p v-for="item in scope.row.contentMeta" class="article-type" style="background-color: #ECF5FF;color: #409EFF;" >
                      {{item}}
                    </p>
                  </div>
                  <div class="tableMain" v-html="scope.row.text" @click="goDetail(scope.row)"></div>
                  <div class="tableFoot">
                    <div class="footInfo">
                      <el-tooltip effect="light" content="评论数" placement="top">
                        <img src="@/assets/images/message.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <span>{{ scope.row.commentNum || 0 }}</span>
                    </div>
                    <div class="footInfo">
                      <el-tooltip effect="light" content="阅读数" placement="top">
                        <img src="@/assets/images/book.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <span>{{ scope.row.readNum || 0 }}</span>
                    </div>
                    <div class="footInfo">
                      <el-tooltip effect="light" content="点赞数" placement="top">
                        <img src="@/assets/images/good.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <span>{{ scope.row.likeNum || 0 }}</span>
                    </div>
                    <div class="footInfo">
                      <el-tooltip effect="light" content="转发数" placement="top">
                        <img src="@/assets/images/share.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <span>{{ scope.row.reprintNum || 0 }}</span>
                    </div>
                    <div class="footButtonGroup">
                      <div>
                        <div class="footButonItem" v-show="scope.row.hitWords">
                          <el-tooltip effect="light" content="涉及词" placement="top">
                            <img src="@/assets/images/keyword.png" alt="" class="footIcon"/>
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.hitWords" placement="top">
                            <span class="keyword">{{ scope.row.hitWords || '' }}</span>
                          </el-tooltip>
                        </div>
                        <div class="footButonItem" v-show="scope.row.hitCourtNames">
                          <el-tooltip effect="light" content="涉及法院" placement="top">
                            <img src="@/assets/images/court.png" alt="" class="footIcon"/>
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.hitCourtNames" placement="top">
                            <span class="keyword" style="color: #247CFF;">{{ scope.row.hitCourtNames || '' }}</span>
                          </el-tooltip>
                        </div>
                        <div class="footButonItem" v-show="scope.row.contentAreaCodeName">
                          <el-tooltip effect="light" content="精准地域" placement="top">
                            <img src="@/assets/images/areaDetail.png" alt="" class="footIcon"/>
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.contentAreaCodeName" placement="top">
                            <span class="keyword" style="color: #356391;">{{
                                scope.row.contentAreaCodeName || ''
                              }}</span>
                          </el-tooltip>
                        </div>
                      </div>

                      <div style="white-space: nowrap;">
                        <!-- <div  class="footButonItem">
                            <img src="@/assets/images/file.png" alt="" class="footIcon" />
                            <span>素材</span>
                        </div>
                        <div  class="footButonItem">
                            <img src="@/assets/images/heart.png" alt="" class="footIcon" />
                            <span>收藏</span>
                        </div>
                        <div  class="footButonItem">
                            <img src="@/assets/images/translate.png" alt="" class="footIcon" />
                            <span>翻译</span>
                        </div> -->
                        <div class="footButonItem" @click="copyAritical(scope.row)">
                          <img src="@/assets/images/copy.png" alt="" class="footIcon"/>
                          <span>复制</span>
                        </div>

                        <div class="footButonItem" @click="openSendMsg(scope.row)">
                          <img src="@/assets/images/send.png" alt="" class="footIcon"/>
                          <span>报送</span>
                        </div>
                        <!-- <el-dropdown>
                            <div  class="footButonItem">
                                <img src="@/assets/images/send.png" alt="" class="footIcon" />
                            <span>报送</span>
                            </div>
                            <el-dropdown-menu  slot="dropdown">
                                <el-dropdown-item @click.native="openSendMsg(scope.row)">短信报送</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown> -->
                        <!-- <el-dropdown>
                            <div  class="footButonItem">
                                <img src="@/assets/images/noise.png" alt="" class="footIcon" />
                                <span>噪音</span>
                            </div>
                            <el-dropdown-menu  slot="dropdown">
                                <el-dropdown-item @click.native="markNoise(scope.row)">{{scope.row.isSpam?'取消噪音':'噪音'}}</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown> -->
                        <div class="footButonItem" @click="goOrigin(scope.row.url)">
                          <img src="@/assets/images/goOrigin.png" alt="" class="footIcon"/>
                          <span>查看原文</span>
                        </div>
                        <div class="footButonItem" @click="copyText(scope.row.url,true)">
                          <img src="@/assets/images/copyLink.png" alt="" class="footIcon"/>
                          <span>拷贝地址</span>
                        </div>
                        <!-- <el-dropdown>
                            <div  class="footButonItem">
                                <img src="@/assets/images/filterInfo.png" alt="" class="footIcon" />
                                <span>过滤信息</span>
                            </div>
                            <el-dropdown-menu  slot="dropdown">
                                <el-dropdown-item @click.native="filterOne(scope.row)">过滤单条</el-dropdown-item>
                                <el-dropdown-item @click.native="filterWeb(scope.row)">过滤站点</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown> -->
                        <el-dropdown>
                          <div class="footButonItem">
                            <img src="@/assets/images/markIcon.png" alt="" class="footIcon"/>
                            <span>标记</span>
                          </div>
                          <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item @click.native="markDisposition(scope.row)">
                              {{ scope.row.deal == 1 ? '取消处置' : '处置' }}
                            </el-dropdown-item>
                            <el-dropdown-item @click.native="markKeyFocus(scope.row)">
                              {{ scope.row.follow == 1 ? '取消重点关注' : '重点关注' }}
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                    </div>
                  </div>
                </div>
                <img class="read-img" v-if="scope.row.isRead==1" src="@/assets/images/read.png" alt="">
                <img class="follow-img" v-if="scope.row.follow==1" src="@/assets/images/follow.png" alt="">
              </template>
            </el-table-column>
            <el-table-column prop="count" label="相似信息" align="center" width="100px">
            </el-table-column>
            <el-table-column prop="nickname" label="来源" align="center" width="100px">
              <template #default="scope">
                <div style="cursor: pointer;" @click="goHomepage(scope.row)">
                  <div v-show="scope.row.typeName!='短视频'">{{ scope.row.typeName }}</div>
                  <div>{{ scope.row.host }}</div>
                  <div v-show="scope.row.typeName=='短视频'">{{ scope.row.author }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="publishTime" align="center" width="100px">
              <template slot="header">
                时间
                <span class="sortIconGroup">
                                <i :class="`el-icon-caret-top ${(queryForm.sort && formInline.sort)=='4'?'active':''}`"
                                   @click="sortChange('4')"></i>
                                <i
                                  :class="`el-icon-caret-bottom ${(queryForm.sort && formInline.sort)=='3'?'active':''}`"
                                  @click="sortChange('3')"></i>
                            </span>
              </template>
              <template slot-scope="scope">
                <div>{{ scope.row.publishTime.substring(0, 10) }}</div>
                <div>{{ scope.row.publishTime.substring(11, 19) }}</div>
              </template>
            </el-table-column>
          </el-table>
          <div v-show="total>0"
               style="width:100%;min-height: 20px;display:flex;align-items:center;justify-content: space-between;">
            <div style="margin-top:10px;">
              <el-select v-model="exportNum" size="small" placeholder="选择导出条数" style="width: 125px" clearable
                         @change="exportNumChange">
                <el-option label="选择当前页" value="0"/>
                <el-option label="前500条" value="500"/>
                <el-option label="前1000条" value="1000"/>
                <el-option label="前5000条" value="5000"/>
              </el-select>
              <!-- <i v-if="!downloadLoading" class="el-icon-bottom export-download" @click="exportExcel()"></i> -->
              <img src="@/assets/images/exportIcon.png" v-if="!downloadLoading" class="exportImg" @click="exportExcel()"
                   alt="">
              <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;" class="el-icon-loading"></i>
            </div>
            <pagination :total="total" :page.sync="queryForm.pageNum" :limit.sync="queryForm.pageSize"
                        @pagination="pagination"/>

          </div>
        </div>
      </div>
    </div>
    <!-- 短信报送 -->
    <SendMsg :visible.sync="sendMsgDialog" :sendMsgRow="sendMsgRow" @visibleChange="visibleChange"></SendMsg>
  </div>
</template>

<script>
import {copyAritical, copyText, goHomepage, transImage} from "@/utils/index"
import SendMsg from '../fullSearch/components/sendMsg.vue';
import {getPlanType, homeDownCount, homeDownExport, searchHome} from '@/api/home/<USER>'
import {
  getUrlAccessStatusApi,
  hotWordList,
  searchRead,
  updateDeal,
  updateEmotion,
  updateFollow
} from "@/api/search/index";

export default {
  components: {SendMsg},
  data() {
    return {
      searchNum: 0,
      startOption: {
        disabledDate: this.startDisable
      },
      endOption: {
        disabledDate: this.endDisable
      },
      goHomepage,
      titleFixed: false,
      totalLoading: false,
      showTotal: 0,
      total: 0,
      downloadLoading: false,
      exportNum: null,
      sendMsgDialog: false,
      sendMsgRow: {},//短信报送对象的信息
      tableLoading: false,
      tableData: [],
      multipleSelection: {selectedRows: []},
      allTotal: 0,
      intervalId: null,
      queryLoading: false,
      searchLoading: false,
      totalLoading: false,
      tableLoading: false,
      totalPage: 0,
      wordsLoading: false,
      wordsData: [],
      transImage,
      pointName: '全部',
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        keyWord1: '',
        keyWord2: '',
        startTime: undefined,
        endTime: undefined,
        isOriginal: false,
        sort: 3,
        emotionFlag: '',
        noSpam: 0,
        type: '',
        searchPosition: 0,
        videoHost: undefined,
        contentForm: [],
        forward: [],
        accountLevel: [],
        accountAreaCode: '',
        timeIndex: undefined,
        homeMedia: undefined,
        author: undefined,
        useCommon: undefined,
        hotWordCloud: undefined,
        planId: undefined,
        areaOverview: undefined
      },
      formInline: {
        user: '',
        planId: '',
        type: null,
        sort: 3,
        isOriginal: false,
        selectedValues: ['all', 0, 1, 2],// 存放选中值的数组
      },
      mediaList: [],
      articleData: [
        {name: '是', value: true},
        {name: '否', value: false},
      ],
      caseData: [],
      msgData: [
        {name: '时间降序', value: 3},
        {name: '时间升序', value: 4},
      ],
      emotionData: [
        {name: '中性', value: 0},
        {name: '敏感', value: 1},
        {name: '非敏感', value: 2}
      ],
      planType: 1,
      isSelectAll: false,
      navData: {},
      accountName: '',
      startTimeFormatted: '',
      endTimeFormatted: '',
      accountData: [],
      pointParams: {
        type: '',
        videohost: '',
        author: ''
      }


    }
  },
  async created() {
    this.queryMediaList()
    this.navData = this.$route.query
    this.accountName = this.navData.name
    if (this.navData.name == '重点关注事件') {
      this.planType = 1
    } else if (this.navData.name == '全省热点事件') {
      this.planType = 2
    } else {
      this.planType = 3
    }

    if (this.navData.name == '中性') {
      this.formInline.selectedValues = [0]
    } else if (this.navData.name == '敏感') {
      this.formInline.selectedValues = [1]
    } else if (this.navData.name == '非敏感') {
      this.formInline.selectedValues = [2]
    } else {
      this.formInline.selectedValues = [1] // 默认敏感
    }

    if (this.navData.planId || this.navData.keyWord1) {
      if (this.navData.emotionFlag == '') {
        this.formInline.selectedValues = ['all', 0, 1, 2]
      } else {
        this.formInline.selectedValues = this.navData?.emotionFlag.split(',').map(Number);
      }
      this.formInline.sort = Number(this.navData.sort)
      this.formInline.isOriginal = this.navData.isOriginal == 'false' ? false : true
    }

    if (this.navData.title == '信息趋势') {
      if (this.navData.name == '全部') {
        delete this.queryForm.homeMedia
      } else {
        this.queryForm.homeMedia = this.navData.name
      }
    }
    if (this.navData.title == '活跃账号') {
      this.queryForm.author = this.navData.name
      if (this.navData.name == '全部') {
        this.accountData = JSON.parse(window.localStorage.getItem('accountData'))
      }
    }
    if (this.navData.title == '热词云') {
      this.queryForm.hotWordCloud = this.navData.name
      if (this.navData.name == '全部') {
        this.accountData = JSON.parse(window.localStorage.getItem('wordsData'))
      }
    }
    if (this.navData.title == '辖区总览') {
      this.queryForm.areaOverview = this.navData.name
      if (this.navData.name == '全部') {
        this.accountData = JSON.parse(window.localStorage.getItem('areaData'))
      }
    }
    if (this.navData.title == '重点账号动态' || this.navData.title == '重点账号') {
      this.queryForm.useArea = this.navData.useArea
      if (this.navData.name == '全部') {
        this.accountData = JSON.parse(window.localStorage.getItem('pointData')).filter((item, index, self) => {
          return index === self.findIndex(t => (
            t.name === item.name && t.type === item.type && t.videoHost === item.videoHost
          ));
        });
        let uniqueNames = [...new Set(this.accountData.map(item => item.name))];
        let uniqueTypes = [...new Set(this.accountData.map(item => item.type))];
        let uniqueVideoHosts = [...new Set(this.accountData.map(item => item.videoHost))];
        this.pointParams.author = uniqueNames.join(',')
        this.pointParams.type = uniqueTypes.join(',')
        this.pointParams.videoHost = uniqueVideoHosts.join(',')
        this.queryForm.author = uniqueNames.join(',')
        this.queryForm.type = uniqueTypes.join(',')
        this.queryForm.videoHost = uniqueVideoHosts.join(',')
      } else {
        this.queryForm.author = this.navData.name
        this.queryForm.type = this.navData.type
        this.queryForm.videoHost = this.navData.videoHost
      }
    }

    this.formatDatesFromQuery()
    // this.queryHabit()

    await this.queryWordList()
    await this.querySysList()
    // this.queryForm.keyWord1 = this.$route.query.title
    // await this.queryArea()
    // this.dateRanges({type:'fixed',date:0})
    await this.getPlan()
    await this.submitSearch()
  },
  mounted() {
    this.titleFixed = false;
    this.$nextTick(() => {
      document.addEventListener('scroll', this.handleScroll, true);
    })
    document.addEventListener("mouseup", (e) => {
      let treeDom = this.$refs.addInput
      if (treeDom) {
        if (!treeDom.contains(e.target)) {
          this.slideFlag = false;
        }
      }
    });
  },
  beforeDestroy() {
    // 当组件即将被销毁时停止间隔
    this.stopInterval();
    document.removeEventListener('mouseup', this.showSlide);
    document.removeEventListener('scroll', this.handleScroll, true);
  },
  methods: {
    // 获取媒体类型
    async queryMediaList() {
      let res = await this.getDicts('sys_media_type')
      this.mediaList = res.data
      this.mediaList.unshift({dictLabel: '全部', dictValue: null})
    },
    // 组件切换
    changeTime(text) {
      const startTime = new Date(this.navData.startTime).getTime();
      const endTime = new Date(this.navData.endtTime).getTime();
      if ((!this.navData.startTime && text === 'start') || (!this.navData.endtTime && text === 'end')) {
        return; // 如果相应的时间还未选择，则直接返回
      }
      if ((text === 'start' && startTime > endTime) || (text === 'end' && startTime > endTime)) {
        if (text === 'start') {
          this.navData.startTime = '';
        } else {
          this.navData.endtTime = '';
        }
        this.$message.error('请选择正确的时间！');
      }
    },
    // 开始日期规则
    startDisable(time) {
      return time.getTime() > new Date(this.navData.endtTime).getTime()
    },
    // 结束日期规则
    endDisable(time) {
      return time.getTime() < new Date(this.navData.startTime).getTime()
    },
    changeAccount(val) {
      if (this.navData.title == '活跃账号') {
        this.navData.name = this.queryForm.author
      } else if (this.navData.title == '热词云') {
        this.navData.name = this.queryForm.hotWordCloud
      } else if (this.navData.title == '辖区总览') {
        this.navData.name = this.queryForm.areaOverview
      } else if (this.navData.title == '重点账号动态' || this.navData.title == '重点账号') {
        if (val != '全部') {
          this.queryForm.author = this.pointName.split('-')[0]
          this.queryForm.type = this.pointName.split('-')[1]
          this.queryForm.videoHost = this.pointName.split('-')[2]
          this.navData.name = this.queryForm.author
        } else {
          this.navData.name = this.pointName
          this.queryForm.author = this.pointParams.author
          this.queryForm.type = this.pointParams.type
          this.queryForm.videoHost = this.pointParams.videoHost
        }
      }
    },
    // 获取事件
    async getPlan() {
      const res = await getPlanType(this.planType)
      this.caseData = res.data
      this.formInline.planId = this.caseData.length > 0 ? this.caseData[0].planId : undefined
    },
    async querySysList() {
      // // 获取排序方式
      // try{
      //     let res = await this.getDicts('sys_search_sort')
      //     this.msgData = res.data
      //     console.log('this.msgData :>> ', this.msgData);
      //     this.formInline.sort = this.sortList[0].dictValue
      // }catch(error){
      //     this.$message.error(error)
      // }
    },
    formatDatesFromQuery() {
      const startDate = new Date(this.navData.startTime);
      const endDate = new Date(this.navData.endTime);
      // 格式化日期为 "年-月-日"
      this.startTimeFormatted = this.formatDate(startDate);
      this.endTimeFormatted = this.formatDate(endDate);
    },
    formatDate(date) {
      // 格式化日期为 "年-月-日"
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以需要+1
      const day = String(date.getDate()).padStart(2, '0'); // 日期可能需要补零
      return `${year}年${month}月${day}日`;
    },
    selectAll() {
      // 用于点击全选时，判断是该全选还是清除选项
      if (this.formInline.selectedValues.length < this.emotionData.length) {
        this.formInline.selectedValues = this.emotionData.map(item => item.value)
        this.formInline.selectedValues.unshift('all')
      } else {
        this.formInline.selectedValues = []
      }
    },
    // 移除select中的标签
    removeTag(val) {
      if (val === 'all') this.formInline.selectedValues = []
    },
    handleSelectChange(val) {
      // val数组不包含'全部'项 且 其他项全部选中
      if (!val.includes('all') && val.length === this.emotionData.length) {
        this.formInline.selectedValues = val
        this.formInline.selectedValues.unshift('all')
      }
      // val数组包含'全部'项 且 其他项有未选中的
      if (val.includes('all') && val.length === this.emotionData.length) {
        this.formInline.selectedValues = val
        this.formInline.selectedValues.splice(0, 1)
      }
    },
    //滚动监听，头部固定
    handleScroll(val) {
      // if (this.tableData.length > 0) {
      //     this.$nextTick(() => {
      //         const tableFixed = this.$refs.data_table
      //         let offsetTop = tableFixed.getBoundingClientRect().top;
      //         this.titleFixed = offsetTop < 0;
      //         if (this.titleFixed) {
      //                 setTimeout(() => {
      //                     const dom = document.querySelector('.is_data_fixed')
      //                     const tableDom = document.querySelector('.el-table__header-wrapper')
      //                     if (dom) {
      //                         if (tableFixed.offsetWidth - dom.offsetWidth != 20||val) {
      //                             dom.style.width = `${tableFixed.offsetWidth - 20}px`
      //                             tableDom.style.width = `${tableFixed.offsetWidth - 40}px`
      //                         }
      //                     }
      //                 }, 200);

      //         }
      //      })
      // }else{
      //     this.titleFixed = false;
      // }


      if (this.tableData.length > 0) {
        const tabFixed = this.$refs.data_table
        let offsetTop = tabFixed.getBoundingClientRect().top;

        this.titleFixed = offsetTop < document.querySelector('.fixed-header').offsetHeight;

        const marginDom = document.querySelector('.dataTable')

        if (this.titleFixed) {
          // this.$nextTick(() => {
          const dom = document.querySelector('.is_data_fixed')
          const tableDom = document.querySelector('.el-table__header-wrapper')
          const dataTotDom = document.querySelector('.dataTot')
          setTimeout(() => {
            if (dom) {
              if (tabFixed.offsetWidth - dom.offsetWidth != 20 || val) {
                dom.style.width = `${tabFixed.offsetWidth - 40}px`
                tableDom.style.width = `${tabFixed.offsetWidth - 40}px`
              }
            }
          }, 10);
          if (marginDom && dataTotDom) {
            marginDom.style.marginTop = `${tableDom.offsetHeight + dataTotDom.offsetHeight}px`
          }
          // })
        } else {
          marginDom.style.marginTop = 0
        }
      } else {
        this.titleFixed = false
      }
    },
    // 获取实时数据刷新
    async queryTimeInfo() {
      let params = JSON.parse(JSON.stringify(this.queryForm))
      // params.accountLevel = params.accountLevel.join(',')
      params.contentForm = params.contentForm.join(',')
      params.forward = params.forward.join(',')
      //  params.type = this.formInline.type
      // params.videoHost = params.videoHost.join(',')
      params.startTime = this.navData.startTime
      params.endTime = this.navData.endTime
      params.sort = this.formInline.sort
      params.isOriginal = this.formInline.isOriginal
      params.useCommon = this.navData.useCommon
      if (this.navData.title == '活跃账号') {
        params.author = params.author == '全部' ? this.accountData.join(',') : params.author
      }
      if (this.navData.title == '热词云') {
        params.hotWordCloud = params.hotWordCloud == '全部' ? this.accountData.join(' ') : params.hotWordCloud
      }
      if (this.navData.title == '辖区总览') {
        params.type = this.formInline.type
        params.areaOverview = params.areaOverview == '全部' ? this.accountData.join(',') : params.areaOverview
      }
      if (this.navData.keyWord1 || this.navData.planId) {
        // 全文搜索 舆情
        params.planId = this.navData.planId
        params.sort = this.navData.sort
        params.isOriginal = this.navData.isOriginal
        params.type = this.navData.type
        params.accountLevel = this.navData.accountLevel
        params.contentForm = this.navData.contentForm
        params.forward = this.navData.forward
        params.noSpam = this.navData.noSpam
        params.searchPosition = this.navData.searchPosition
        params.videoHost = this.navData.videoHost

        params.host = this.navData.host
        params.author = this.navData.author
        params.accountGrade = this.navData.accountGrade
        params.useArea = this.navData.useArea
        params.emotionFlag = this.navData.emotionFlag
        params.hotWordCloud = this.navData.hotWordCloud
        params.contentAreaCode = this.navData.contentAreaCode

      }


      if (this.navData.title == '关注') {
        params.planId = this.formInline.planId
      } else {
        delete this.queryForm.planId
      }
      if (this.navData.title == '辖区总览') {
        params.type = this.formInline.type
      } else {
        params.type = this.navData.type
      }
      if (this.formInline.selectedValues.includes('all')) {
        params.emotionFlag = '';
      } else {
        params.emotionFlag = this.formInline.selectedValues.join(',');
      }

      this.totalLoading = true
      let res = await homeDownCount(params)
      this.allTotal = res.data
      if (this.allTotal > this.total) {
        this.showTotal = this.allTotal - this.total
      } else {
        this.showTotal = 0
      }
      this.totalLoading = false
    },
    startInterval() {
      console.log(this.intervalId, 'this.intervalId');
      // 清除之前的间隔（如果有的话）
      if (this.intervalId) {
        clearInterval(this.intervalId);
      }
      this.queryTimeInfo()
      // 每隔一分钟（60000毫秒）调用queryTimeInfo方法
      this.intervalId = setInterval(() => {
        this.queryTimeInfo()
      }, 60000)
    },
    stopInterval() {
      // 清除间隔
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },
    // 分页查询
    pagination(page) {
      this.queryForm.pageNum = page.page
      this.queryForm.pageSize = page.limit
      this.submitSearch()
      document.querySelector('.app-main').scrollTop = 430
    },
    // 列表导出
    exportExcel(id) {
      let params = JSON.parse(JSON.stringify(this.queryForm))
      // params.accountLevel = params.accountLevel.join(',')
      params.contentForm = params.contentForm.join(',')
      params.forward = params.forward.join(',')
      // params.type = this.formInline.type
      // params.videoHost = params.videoHost.join(',')
      params.startTime = this.navData.startTime
      params.endTime = this.navData.endTime
      params.sort = this.formInline.sort
      params.isOriginal = this.formInline.isOriginal
      params.useCommon = this.navData.useCommon
      if (this.navData.keyWord1 || this.navData.planId) {
        // 全文搜索 舆情
        params.planId = this.navData.planId
        params.sort = this.navData.sort
        params.isOriginal = this.navData.isOriginal
        params.type = this.navData.type
        params.accountLevel = this.navData.accountLevel
        params.contentForm = this.navData.contentForm
        params.forward = this.navData.forward
        params.noSpam = this.navData.noSpam
        params.searchPosition = this.navData.searchPosition
        params.videoHost = this.navData.videoHost

        params.host = this.navData.host
        params.author = this.navData.author
        params.accountGrade = this.navData.accountGrade
        params.useArea = this.navData.useArea
        params.emotionFlag = this.navData.emotionFlag
        params.hotWordCloud = this.navData.hotWordCloud
        params.contentAreaCode = this.navData.contentAreaCode

      }
      if (this.navData.title == '活跃账号') {
        params.author = params.author == '全部' ? this.accountData.join(',') : params.author
      }
      if (this.navData.title == '热词云') {
        params.hotWordCloud = params.hotWordCloud == '全部' ? this.accountData.join(' ') : params.hotWordCloud
        console.log(params.hotWordCloud, 'params.hotWordCloud');

      }

      if (this.navData.title == '关注') {
        params.planId = this.formInline.planId
      } else {
        delete this.queryForm.planId
      }
      if (this.navData.title == '辖区总览') {
        params.type = this.formInline.type
        params.areaOverview = params.areaOverview == '全部' ? this.accountData.join(',') : params.areaOverview
      }
      if (this.formInline.selectedValues.includes('all')) {
        params.emotionFlag = '';
      } else {
        params.emotionFlag = this.formInline.selectedValues.join(',');
      }

      const req = {...params}
      if (this.exportNum) { //选项导出
        if (this.exportNum !== '0') {
          req.pageNum = 1
          req.pageSize = parseInt(this.exportNum)
        }
      } else if (this.multipleSelection.selectedRows.length > 0) { //勾选导出
        const ids = this.multipleSelection.selectedRows.map(item => item.id)
        req.ids = ids
      } else {
        this.$message.warning('请选择导出条数或导出项')
        return
      }
      this.downloadLoading = true
      this.$confirm('是否确认导出所有类型数据项?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return homeDownExport(req);
      }).then(response => {
        this.downloadLoading = false
        this.$message.success('导出成功')
        this.download(response.msg);
      }).catch(() => {
        this.downloadLoading = false
      })
    },
    //导出条数变动后的table选中项改变
    exportNumChange(val) {
      this.$refs.tableRef.clearSelection()
      if (val) {
        this.$refs.tableRef.toggleAllSelection()
      }
    },
    sortChange(sort) {
      this.queryForm.sort = sort
      this.formInline.sort = Number(sort)
      this.submitSearch()
    },
    // 获取热词
    async queryWordList() {
      try {
        this.wordsLoading = true
        let res = await hotWordList({count: 15})
        this.wordsData = res.data
      } finally {
        this.wordsLoading = false
      }
    },
    //处置||移除
    async markDisposition(row) {
      let val = row.deal ? 0 : 1;
      let res = await updateDeal({md5: row.md5, deal: val, indexId: row?.id, createTime: row?.publishTime})
      if (res.code == 200) {
        this.$set(row, 'deal', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'deal', row.deal);
        this.$message.error(res.msg)
      }
    },
    //重点关注||移除
    async markKeyFocus(row) {
      let val = row.follow ? 0 : 1;
      let res = await updateFollow({md5: row.md5, follow: val, indexId: row?.id, createTime: row?.publishTime})
      if (res.code == 200) {
        this.$set(row, 'follow', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'follow', row.follow);
        this.$message.error(res.msg)
      }
    },
    // 复制文章
    copyAritical(row, tips) {
      copyAritical(row)
    },
    // 复制
    copyText(content, tips) {
      copyText(content, tips)
    },
    // 跳转详情页
    goOrigin(url) {
      window.open(url, '_blank')
    },
    //开启发送短信弹窗
    openSendMsg(row) {
      this.sendMsgDialog = true
      this.sendMsgRow = row
      this.sendMsgRow.keyWord1 = ''
      this.sendMsgRow.planId = ''
    },
    //同步sendMsgDialog值
    visibleChange(value) {
      this.sendMsgDialog = value
    },
    // 跳转详情页
    async goDetail(row) {
      // beforeTime.momentDay beforeTime.oneDay beforeTime.twoDay beforeTime.threeDay beforeTime.sevenDay
      const fullPath = this.$router.resolve({
        path: '/fullSearch/dataDetail',
        query: {id: row.id, planId: row.planId, keyWords: row.hitWords, time: row.publishTime, md5: row.md5}
      })
      window.open(fullPath.href, '_blank')
      if (row.isRead != 1) {
        this.updateIsRead(row.id)
        await searchRead({id: row.id})
      }
    },
    //更新阅读状态
    updateIsRead(id) {
      const foundItem = this.tableData.find(item => item.id === id);
      if (foundItem) {
        this.$set(foundItem, 'isRead', 1);
      }
    },
    // 切换敏感类型
    async changeSensitive(val, row) {
      let res = await updateEmotion({md5: row.md5, emotionFlag: val})
      this.$set(row, 'emotionFlag', val);
      if (res.code == 200) {
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'emotionFlag', row.originFlag);
        this.$message.error(res.msg)
      }
    },
    //table选中项改变
    handleSelectionChange(val) {
      this.multipleSelection.selectedRows = val
    },
    // 查询列表
    async submitSearch(type) {
      this.searchNum++
      this.slideFlag = false
      if (type == 'search') {
        this.queryForm.pageNum = 1
      }
      // if(!this.queryForm.keyWord1){
      //     this.$message.error('关键词不能为空！')
      //     return
      // }
      // type- search-搜索 query-空：查询 空-翻页、刷新
      // if (this.timeType != 'fixed') {
      //     if (!this.queryForm.startTime || !this.queryForm.endTime) {
      //         this.$message.error('请输入完整的时间范围')
      //         return
      //     }
      // }
      this.exportNum = ''//翻页时将导出页数选择器重置
      try {
        this.tableLoading = true
        if (type == 'search') { //点击翻页触发该方法
          this.searchLoading = true
        } else if (type == 'query') {
          this.queryLoading = true
        }
        let params = JSON.parse(JSON.stringify(this.queryForm))
        // params.accountLevel = params.accountLevel.join(',')
        params.contentForm = params.contentForm.join(',')
        params.forward = params.forward.join(',')
        if (this.navData.title == '活跃账号') {
          params.author = params.author == '全部' ? this.accountData.join(',') : params.author
        }
        if (this.navData.title == '热词云') {
          params.hotWordCloud = params.hotWordCloud == '全部' ? this.accountData.join(' ') : params.hotWordCloud
        }
        // params.type = params.type.join(',')
        // params.videoHost = params.videoHost.join(',')

        params.startTime = this.navData.startTime
        params.endTime = this.navData.endTime
        params.sort = this.formInline.sort
        params.isOriginal = this.formInline.isOriginal
        params.useCommon = this.navData.useCommon

        if (this.navData.keyWord1 || this.navData.planId) {
          // 全文搜索 舆情
          params.planId = this.navData.planId
          params.sort = this.navData.sort
          params.isOriginal = this.navData.isOriginal
          params.type = this.navData.type
          params.accountLevel = this.navData.accountLevel
          params.contentForm = this.navData.contentForm
          params.forward = this.navData.forward
          params.noSpam = this.navData.noSpam
          params.searchPosition = this.navData.searchPosition
          params.videoHost = this.navData.videoHost

          params.host = this.navData.host
          params.author = this.navData.author
          params.accountGrade = this.navData.accountGrade
          params.useArea = this.navData.useArea
          params.emotionFlag = this.navData.emotionFlag
          params.hotWordCloud = this.navData.hotWordCloud
          params.contentAreaCode = this.navData.contentAreaCode
          params.keyWord1 = this.navData.keyWord1

        }
        if (this.navData.title == '关注') {
          params.planId = this.formInline.planId
        } else {
          delete this.queryForm.planId
        }
        if (this.navData.title == '辖区总览') {
          params.type = this.formInline.type
          params.areaOverview = params.areaOverview == '全部' ? this.accountData.join(',') : params.areaOverview
        }
        if (this.formInline.selectedValues.includes('all')) {
          params.emotionFlag = '';
        } else {
          params.emotionFlag = this.formInline.selectedValues.join(',');
        }

        if (!params.startTime || !params.endTime) {
          this.$message.error('请输入正确的时间段')
          this.tableLoading = false
          this.searchLoading = false
          this.queryLoading = false
          return
        }
        console.log('params :>> ', params);
        const res = await searchHome(params)
        this.tableData = res.rows.map(item => {
          item.originFlag = item.emotionFlag;
          // 初始化 `urlAccessStatus` 属性
          this.$set(item, 'urlAccessStatus', null);
          return item;
        });
        this.tableData = res.rows
        this.tableData.map((item) => {
          item.originFlag = item.emotionFlag
        })
        this.total = Number(res.total)
        this.totalPage = Math.ceil(this.total / this.queryForm.pageSize)
        this.searchLoading = false
        this.queryLoading = false
        this.tableLoading = false
        this.tableData = await this.enrichArrayWithDetails(this.tableData)
        await this.checkUrlAlive(this.tableData, this.searchNum)
        this.startInterval()
      } catch (error) {
        console.log(error)
        this.searchLoading = false
        this.queryLoading = false
        this.tableLoading = false
      }
      // this.tableData=[{id:1},{id:2},{id:3},{id:4},{},{},{},{}]
    },
    //校验原文是否删除
    async checkUrlAlive(data, number) {
      const urls = data.map(item => {
        return item.url
      })
      const newArray = data
      try {
        let res = await getUrlAccessStatusApi(urls)
        res.data.map((item, index) => {
          newArray[index].urlAccessStatus = item
        })
      } catch (err) {
        this.$message.error(err)
      }
      if (this.searchNum == number) {
        this.tableData = newArray;
      }
    },
    async enrichArrayWithDetails(arrayA) {
      // 使用map创建一个Promise数组
      const promises = arrayA.map(async item => {
        if (this.queryForm.isOriginal != true && this.queryForm.sort != 7) {
          // const detail = await similarCount({md5:item.md5});
          return {...item, count: 1};
        } else {
          return {...item, count: item.similarCount};
        }
      });
      const enrichedArray = await Promise.all(promises);
      return enrichedArray;
    },
    goLeft() {
      if (this.totalPage >= this.queryForm.pageNum && this.queryForm.pageNum > 1) {
        this.queryForm.pageNum--
        this.submitSearch()
      }
    },
    goRight() {
      if (this.totalPage > this.queryForm.pageNum && this.totalPage > 0) {
        this.queryForm.pageNum++
        this.submitSearch()
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.expend {
  background: #F4F7FB;
  padding: 20px 40px;
  min-height: 100vh;

  .nav {
    background: #fff;
    display: flex;
    justify-content: space-between;

    .nav-box {
      width: 32.5%;
      height: 168px;
      text-align: center;
      line-height: 168px;
      font-size: 28px;
      color: #FFFFFF;
      background-position: center center;
      background-repeat: no-repeat;
      background-size: cover;
    }

    .nav-box:nth-child(1) {
      background-image: url('../../assets/images/box-blue.png');
    }

    .nav-box:nth-child(2) {
      background-image: url('../../assets/images/box-lakeblue.png');
    }

    .nav-box:nth-child(3) {
      background-image: url('../../assets/images/box-yellow.png');
    }
  }

  .search-wrap {
    // height: 210px;
    background: #FFFFFF;
    margin-top: 30px;
    margin-bottom: 20px;
    padding-bottom: 20px;

    .title {
      height: 60px;
      line-height: 60px;
      border-bottom: 1px solid #DCDEE0;

      span {
        border-left: 6px solid #247CFF;
        padding-left: 28px;
        font-weight: bold;
        font-size: 18px;
        color: #333333;
      }
    }

    .demo-form-inline {
      padding-top: 30px;
      padding-left: 36px;

      ::v-deep .el-form-item__label {
        font-size: 14px;
        color: #666666;
        font-weight: normal;
      }
    }

    .search-btn {
      text-align: center;
    }
  }

  .wrap-content {
    background: #F4F7FB;
  }
}
</style>
<style scoped lang="scss">
@import "./expendDetail.scss";
</style>
