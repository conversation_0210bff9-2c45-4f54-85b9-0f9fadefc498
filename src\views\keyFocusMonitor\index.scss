.data-container {
  width: 100%;
  padding: 20px 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  background: #f4f7f9;

  .el-form-item__content {
    line-height: 40px;
  }

  .tree-button {
    position: absolute;
    top: 50%;
    left: 304px;
    z-index: 99;
    width: 16px;
    // height: 50px;
    color: #fff;
    border-color: transparent #848ca6 transparent transparent;
    border-style: solid;
    border-width: 12px 12px 12px 0;
    transform: translateY(-50%);
    cursor: pointer;
    transition: border-right .2s;

    &:hover {
      border-right: 16px solid #848ca6;
    }

    img {
      width: 16px;
      margin: 5px 0 5px 2px;
    }
  }

  .dataSidebar {
    width: 300px;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    margin-right: 18px;
    height: calc(100vh - 120px);
    overflow-y: auto;
    transition: width .2s;
  }

  .dataCont {
    // width: calc(100% - 320px);
    flex: 1;
    height: calc(100vh - 120px);
    overflow-y: auto;

    // .dataMonitHead {
    //   //overflow: hidden;
    //   height: 50px;
    //   line-height: 50px;
    //   text-align: left;
    //   padding: 0 0 0 20px;
    //   box-sizing: border-box;
    //   background: #fff;
    //   width: 100%;
    // }

    .titleMonitHead {
      text-align: left;
      padding: 16px 0 20px 27px;
      box-sizing: border-box;
      background: #fff;
      width: 100%;
    }

    .bl5 {
      border-left: 5px solid #1890FF;
    }

    .dataMonit {
      overflow: hidden;
      width: 100%;
      // margin-bottom: 15px;
      // height: 50px;
    }

    .dataMain_top {
      padding: 12px 0;
      // border-bottom: 1px dashed #DCDEE0;
      background-color: #fff;
      position: relative;

      .collapseButton {
        position: absolute;
        z-index: 9;
        right: 20px;
        top: 20px;

        .retract {
          cursor: pointer;

          span {
            display: inline-block;
            margin-left: 6px;
          }
        }
      }

      .dataMain_top_colorBlock {
        width: 6px;
        height: 16px;
        background: #247CFF;
        display: inline-block;
        vertical-align: middle;
        margin-right: 20px;
      }

      .dataMain_top_title {
        font-size: 18px;
        color: #333333;
        line-height: 25px;
        display: inline-block;
        vertical-align: middle;
      }

      .iconGroup {
        display: inline-block;
        vertical-align: middle;
        margin-left: 20px;

        img {
          margin-right: 7px;
          height: 21px;
        }
      }
    }

    .dashedLine {
      width: 100%;
      border-bottom: 1px dashed #DCDEE0;
    }

    .dataMain {
      overflow-y: auto;
      width: 100%;
      // margin-bottom: 15px;
      // min-height: calc(100% - 59px);
      background-color: #fff;

      .dataMain_content {
        padding: 30px 0;
      }


      .dataMonitSear {
        width: 100%;
        //overflow: hidden;
        background: #fff;
        padding: 0 20px 10px;
        // padding-left: 35px;
        box-sizing: border-box;
        margin-bottom: 23px;

        li {
          // color: #666666;
        }

        .dataMTitle {
          width: 100%;
          //overflow: hidden;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          margin: 0 0 18px 0;
          padding: 20px 0 0 0;
          color: #333;
          line-height: 22px;
          font-size: 16px;
        }

        .timeSelUl {
          margin: 0;
          padding: 0;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          flex-wrap: wrap;
          font-size: 14px;
          color: #333;
          line-height: 20px;

          li {
            list-style: none;
            margin-right: 10px;
            padding: 4px 10px;
            cursor: pointer;
            border: solid 1px transparent; //透明边框防止点击后才出现的边框产生位移
            border-radius: 2px;
          }

          li.active {
            background-color: #1890FF;
            // border: solid 1px #409eff;
            // border-radius: 4px;
            color: #fff;
          }
        }

        .dataLine {
          padding: 20px 0 24px 0;
          text-align: center;
          border-top: 1px dashed #DCDEE0;
        }
      }

      .tabItem {
        .dataTot {
          width: 100%;
          text-align: center;
          margin: 0;
          font-size: 14px;
          color: #333;

          .totalNum {
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 40px;
            border-right: 20px solid #fff;
            border-left: 20px solid #fff;
            background-color: #fcfcfd;
          }

          span {
            color: #1890FF;
            margin: 0 3px;
          }

          i.el-icon-refresh {
            margin-left: 6px;
            font-size: 16px;
            color: #1890FF;
          }

          .dataSel {
            display: flex;
            justify-content: space-between;
            background-color: #fff;
            padding: 13px 20px 0 20px;

            .input-with-select {
              width: 400px;
            }
          }
        }

        .dataTable {
          width: 100%;
          //overflow: hidden;
          padding: 20px;
          box-sizing: border-box;
          background: #fff;

          .tableItemTitle {
            .tableTitle {
              em {
                color: #f46263;
              }

              .tableItemImg {
                width: 29px;
              }

              .tableItemImg,
              .tableTitleSpan {
                vertical-align: middle;
                margin-right: 10px;
              }

              .tableTitleSpan {
                display: inline-block;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                max-width: calc(100% - 180px);
                font-weight: 600;
                font-size: 16px;
                line-height: 22px;
                color: #333;
                cursor: pointer;
              }
            }

            .tableMain {
              margin: 6px 0 8px 0;
              position: relative;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              /* 控制显示的行数 */
              -webkit-box-orient: vertical;
              max-height: calc(2em * 2);
              /* 控制显示的高度 */
              overflow: hidden;
              font-size: 14px;
              line-height: 20px;
              color: #666;

              em {
                color: #f46263;
              }
            }

            .tableFoot {
              font-size: 12px;

              .footInfo {
                display: inline-block;
                margin-right: 26px;
              }

              .footButtonGroup {
                .footButonItem {
                  font-size: 12px;
                  display: inline-block;
                  margin-right: 20px;
                  cursor: pointer;
                  white-space: nowrap;
                }
              }

              img {
                width: 14px;
                margin-right: 5px;
              }

              img,
              span {
                display: inline-block;
                vertical-align: middle;
              }
            }
          }
        }
      }
    }
  }
}

.data-container.collapse {
  .dataSidebar {
    // position: absolute;
    width: 0px;
    padding: 0;
    margin: 0;
  }

  .tree-button {
    left: -16px;
    transform: translateY(-50%) rotateY(180deg);
    transform-origin: 100% 50%;
  }

  .dataSide {
    display: none;
  }
}


.menuButton {
  text-align: center;
}

.contextMenuTitle {
  margin-right: 20px;
  vertical-align: middle;
}

.menuIcon {
  font-size: 18px;
  vertical-align: middle;
}

.statics-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13px;
  color: #515a6e;
  font-weight: bold;

  em {
    font-style: normal;
    color: #3d9ffe;
  }
}

.downloadIcon {
  color: var(--el-color-primary);
}

.accountNameStyle {
  display: inline-block;
  vertical-align: middle;
}

.data-none {
  background: #fff;
}

// .top-tabs {
//   ::v-deep .el-tabs__header {
//     margin: 0 0 0px;

//     .el-tabs__nav-wrap::after {
//       background-color: transparent;
//     }

//     .el-tabs__item {
//       height: auto;
//       line-height: 30px;
//       color: #666666;
//       font-size: 16px;
//     }

//     .is-active {
//       color: #333333;
//       font-weight: 600;
//     }

//     .el-tabs__active-bar {
//       background-color: #247CFF;
//     }
//   }
// }

@keyframes ball-turn {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.export-field {
  margin-right: 15px;
  color: rgb(168, 171, 178);
  cursor: pointer;
}

.export-download {
  margin-left: 15px;
  font-size: 20px;
  vertical-align: middle;
  cursor: pointer;
}

::-webkit-scrollbar-track-piece {
  background-color: transparent;
}

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: hsla(220, 4%, 58%, .3);
}

.monit-data {
  position: relative;

  .el-icon-setting {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 20px;
    cursor: pointer;
    z-index: 200;
  }
}

::v-deep {
  .el-backtop {
    background-color: rgba(255, 255, 255, 0.6);
  }

  .el-tabs__item:focus.is-active.is-focus {
    box-shadow: none;
  }
}
