<template>
  <div class="reportSetting">

    <!-- 日报 -->
    <el-form ref="dayForm" class="formMain" :model="settingForm" label-width="140px"
             :rules="rules">
      <div class="formSetPart2">
        <div class="formH2">
          <div class="bluePoint"></div>
          <div>日报设置</div>
        </div>
        <el-form-item label="定时生成报告：" prop="dayStatus">
          <el-radio-group v-model="dayStatus">
            <el-radio :label="0" @change="changeStatus('1')">关闭</el-radio>
            <el-radio :label="1">开启</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="dayStatus === 1">
          <el-form-item label="日报生成时间：" prop="reportDate">
            <div style="display:flex;align-items:center;">
              <check-box v-model="settingForm.reportDate" :list="mediaList"></check-box>
              <el-time-select style="width: 150px;" placeholder="具体时间点" v-model="settingForm.reportTime"
                              :picker-options="{start: '00:00',step: '01:00',end: '23:00'}" :clearable="false">
              </el-time-select>
            </div>
          </el-form-item>
          <el-form-item label="报告模板：" prop="tempId">
            <el-select v-model="settingForm.tempId" placeholder="请选择报告模板" style="width:80%">
              <el-option v-for="item in tempLists" :key="item.tempId" :label="item.name"
                         :value="item.tempId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="推送人：" prop="users">
            <div class="receiveWay">
                            <span class="receiveTitle">
                                <span>
                                    <img src="@/assets/images/emailPush.svg" alt="">
                                    邮件推送
                                </span>
                            </span>
              <div class="receiveMain">
                                <span class="receiveMain-option">
                                    接收人：请选择需要接收消息的联系人
                                    <el-button type="text" style="color: #247CFF;"
                                               @click="resetContacts('1','day')">重新设置接收人</el-button>
                                </span>
                <el-table :data="emailContactsList" border style="width:80%">
                  <el-table-column prop="username" label="姓名" width="300"></el-table-column>
                  <el-table-column prop="email" label="邮箱地址"></el-table-column>
                  <el-table-column align="center" label="操作" width="200">
                    <template #default="scope">
                      <el-button slot="reference" type="text" style="color: #247CFF;"
                                 @click="delContactsList('emailContactsList',scope.row)">删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-form-item>
          <el-form-item>
            <div style="text-align: left;margin-bottom: 50px;">
                        <span class="dialog-footer">
                            <el-button type="primary" :loading="btnLoading"
                                       @click="daySubmitForm('dayForm')">保 存</el-button>
                        </span>
            </div>
          </el-form-item>

        </div>
      </div>

    </el-form>
    <!-- 周报 -->
    <el-form ref="weekForm" class="formMain" :model="weekSettingForm" label-width="140px"
             :rules="rules">
      <div class="formSetPart2">
        <div class="formH2">
          <div class="bluePoint"></div>
          <div>周报设置</div>
        </div>
        <el-form-item label="定时生成报告：" prop="weekStatus">
          <el-radio-group v-model="weekStatus">
            <el-radio :label="0" @change="changeStatus('2')">关闭</el-radio>
            <el-radio :label="1">开启</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="weekStatus">
          <el-form-item label="周报生成时间：" prop="reportDate">
            <div style="display:flex;align-items:center;">
              <span>每周：</span>
              <el-select v-model="weekSettingForm.reportDate" placeholder="请选择" style="margin-right:20px;">
                <el-option v-for="item in mediaList" :key="item.dictValue" :label="item.dictLabel"
                           :value="item.dictValue"></el-option>
              </el-select>
              <el-time-select style="width: 150px;" placeholder="具体时间点" v-model="weekSettingForm.reportTime"
                              :picker-options="{start: '00:00',step: '01:00',end: '23:00'}" :clearable="false">
              </el-time-select>
            </div>
          </el-form-item>
          <el-form-item label="报告模板：" prop="tempId">
            <el-select v-model="weekSettingForm.tempId" placeholder="请选择报告模板" style="width:80%">
              <el-option v-for="item in tempLists" :key="item.tempId" :label="item.name"
                         :value="item.tempId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="推送人：" prop="users">
            <div class="receiveWay">
                        <span class="receiveTitle">
                            <span>
                                <img src="@/assets/images/emailPush.svg" alt="">
                                邮件推送
                            </span>
                        </span>
              <div class="receiveMain">
                            <span class="receiveMain-option">
                                接收人：请选择需要接收消息的联系人
                                <el-button type="text" style="color: #247CFF;"
                                           @click="resetContacts('1','week')">重新设置接收人</el-button>
                            </span>
                <el-table :data="weekEmailContactsList" border style="width:80%">
                  <el-table-column prop="username" label="姓名" width="300"></el-table-column>
                  <el-table-column prop="email" label="邮箱地址"></el-table-column>
                  <el-table-column align="center" label="操作" width="200">
                    <template #default="scope">
                      <el-button slot="reference" type="text" style="color: #247CFF;"
                                 @click="delContactsList('weekEmailContactsList',scope.row)">删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-form-item>
          <el-form-item>
            <div style="text-align: left;margin-bottom: 50px;">
                    <span class="dialog-footer">
                        <el-button type="primary" :loading="btnLoading"
                                   @click="weekSubmitForm('weekForm')">保 存</el-button>
                    </span>
            </div>
          </el-form-item>
        </div>
      </div>
    </el-form>

    <!-- 月报设置 -->
    <el-form ref="monthForm" class="formMain" :model="monthSettingForm" label-width="140px"
             :rules="rules">
      <div class="formSetPart2">
        <div class="formH2">
          <div class="bluePoint"></div>
          <div>月报设置</div>
        </div>
        <el-form-item label="定时生成报告：" prop="monthStatus">
          <el-radio-group v-model="monthStatus">
            <el-radio :label="0" @change="changeStatus('3')">关闭</el-radio>
            <el-radio :label="1">开启</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="monthStatus">
          <el-form-item label="月报生成时间：" prop="reportDate">
            <div style="display:flex;align-items:center;">
              <span>每月：</span>
              <el-select v-model="monthSettingForm.reportDate" placeholder="请选择" style="margin-right:20px;">
                <el-option v-for="item in monthList" :key="item.dictValue" :label="item.dictLabel"
                           :value="item.dictValue"></el-option>
              </el-select>
              <el-time-select style="width: 150px;" placeholder="具体时间点" v-model="monthSettingForm.reportTime"
                              :picker-options="{start: '00:00',step: '01:00',end: '23:00'}" :clearable="false">
              </el-time-select>
            </div>
          </el-form-item>
          <el-form-item label="报告模板：" prop="tempId">
            <el-select v-model="monthSettingForm.tempId" placeholder="请选择报告模板" style="width:80%">
              <el-option v-for="item in tempLists" :key="item.tempId" :label="item.name"
                         :value="item.tempId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="推送人：" prop="users">
            <div class="receiveWay">
                        <span class="receiveTitle">
                            <span>
                                <img src="@/assets/images/emailPush.svg" alt="">
                                邮件推送
                            </span>
                        </span>
              <div class="receiveMain">
                            <span class="receiveMain-option">
                                接收人：请选择需要接收消息的联系人
                                <el-button type="text" style="color: #247CFF;"
                                           @click="resetContacts('1','month')">重新设置接收人</el-button>
                            </span>
                <el-table :data="monthEmailContactsList" border style="width:80%">
                  <el-table-column prop="username" label="姓名" width="300"></el-table-column>
                  <el-table-column prop="email" label="邮箱地址"></el-table-column>
                  <el-table-column align="center" label="操作" width="200">
                    <template #default="scope">
                      <el-button slot="reference" type="text" style="color: #247CFF;"
                                 @click="delContactsList('monthEmailContactsList',scope.row)">删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-form-item>
          <el-form-item>
            <div style="text-align: left;margin-bottom: 50px;">
                    <span class="dialog-footer">
                        <el-button type="primary" :loading="btnLoading"
                                   @click="monthSubmitForm('monthForm')">保 存</el-button>
                    </span>
            </div>
          </el-form-item>
        </div>
      </div>
    </el-form>

    <lexiconDialog :visible.sync="lexiconDialogVisible" @visibleChange="lexiconVisibleChange"
                   :lexiconType="lexiconType" @submit="submitWords"></lexiconDialog>


    <el-dialog :visible.sync="sendMsgDialog" title="选择推送用户" width="50%" @close="cancelMsgDialog">
      <el-form ref="sendMsgForm" @submit.prevent>

        <el-form-item label="接收人：" prop="contacts">
                    <span style="display: flex;justify-content: space-between;">
                        <div class="yellowTips">
                            <img src="@/assets/images/yellowWarn.svg" alt="">
                            <span>请选择需要接收消息的联系人</span>
                        </div>
                        <el-input style="margin: 0 40px;" v-model.trim="searchWord" placeholder="请输入联系人"
                                  clearable>
                            <el-button slot="append" icon="el-icon-search" @click="getContactsList"></el-button>
                        </el-input>
                        <el-button type="primary" plain icon="el-icon-plus" @click="addNewUser">添加</el-button>
                    </span>
        </el-form-item>
        <el-table ref="contactsTableRef" :data="contactsList" :row-key="(row) => row.id"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center"
                           :reserve-selection="true"></el-table-column>
          <el-table-column prop="username" label="姓名">
          </el-table-column>
          <el-table-column v-if="contactsListType=='1'" prop="email" label="邮箱">
          </el-table-column>
          <el-table-column v-if="contactsListType=='0'" prop="phone" label="手机号">
          </el-table-column>
          <el-table-column v-if="contactsListType=='2'" prop="wxUserName" label="微信号">
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <el-button v-show="contactsListType=='1'" type="text" style="color: #409eff;"
                         @click="checkEmail(scope.row)">邮箱验证
              </el-button>
              <el-button type="text" style="color: #409eff;" @click="updataRow(scope.row)">修改</el-button>
              <el-popconfirm title="确定删除该联系人吗？" @confirm="delRow(scope.row)" style="margin-left: 10px;">
                <el-button slot="reference" type="text" style="color: #f56c6c;">删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

      </el-form>
      <template #footer>
        <div style="text-align: center;">
          <el-button type="primary" @click="submitSendMsgForm">确定</el-button>
          <el-button @click="cancelMsgDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>


    <contactsEdit :visible.sync="contactsDialog" :contactsRow="contactsRow" :contactsOption="contactsOption"
                  :type="contactsListType" @visibleChange="contactsVisibleChange" @afterSubmit="contactsSubmit">
    </contactsEdit>
  </div>

</template>

<script>
import {copyText} from '@/utils/index';
import {getContactsApi, delContactsApi} from "@/api/search/index";
import {NolistTemplate} from '@/api/report/template.js'
import {warnChangeApi, warnGetApi, mailTestApi} from "@/api/publicOpinionMonitor/index.js";
import lexiconDialog from '../lexiconDialog/index.vue'
import {planReportApi, updatePlanReport, insertPlanReport} from '@/api/publicOpinionMonitor/report.js'

import contactsEdit from '@/views/publicOpinionMonitor/components/contactsEdit'

import CheckBox from "@/components/CheckBox";

export default {
  components: {
    lexiconDialog,

    CheckBox,
    contactsEdit,
  },
  props: {
    checkedNode: {
      type: Object,
      default: () => {
      },
    },
  },
  data() {
    return {

      monitorSettingStatus: true,
      mediaList: [
        {dictLabel: '周一', dictValue: '2'},
        {dictLabel: '周二', dictValue: '3'},
        {dictLabel: '周三', dictValue: '4'},
        {dictLabel: '周四', dictValue: '5'},
        {dictLabel: '周五', dictValue: '6'},
        {dictLabel: '周六', dictValue: '7'},
        {dictLabel: '周日', dictValue: '1'},
      ],
      monthList: [
        {dictLabel: '一号', dictValue: '1'}, {dictLabel: '二号', dictValue: '2'},
        {dictLabel: '三号', dictValue: '3'}, {dictLabel: '四号', dictValue: '4'},
        {dictLabel: '五号', dictValue: '5'}, {dictLabel: '六号', dictValue: '6'},
        {dictLabel: '七号', dictValue: '7'}, {dictLabel: '八号', dictValue: '8'},
        {dictLabel: '九号', dictValue: '9'}, {dictLabel: '十号', dictValue: '10'},
        {dictLabel: '十一号', dictValue: '11'}, {dictLabel: '十二号', dictValue: '12'},
        {dictLabel: '十三号', dictValue: '13'}, {dictLabel: '十四号', dictValue: '14'},
        {dictLabel: '十五号', dictValue: '15'}, {dictLabel: '十六号', dictValue: '16'},
        {dictLabel: '十七号', dictValue: '17'}, {dictLabel: '十八号', dictValue: '18'},
        {dictLabel: '十九号', dictValue: '19'}, {dictLabel: '二十号', dictValue: '20'},
        {dictLabel: '二十一号', dictValue: '21'}, {dictLabel: '二十二号', dictValue: '22'},
        {dictLabel: '二十三号', dictValue: '23'}, {dictLabel: '二十四号', dictValue: '24'},
        {dictLabel: '二十五号', dictValue: '25'}, {dictLabel: '二十六号', dictValue: '26'},
        {dictLabel: '二十七号', dictValue: '27'}, {dictLabel: '二十八号', dictValue: '28'},
        {dictLabel: '二十九号', dictValue: '29'}, {dictLabel: '三十号', dictValue: '30'},
        {dictLabel: '三十一号', dictValue: '31'},
      ],
      tempLists: [],
      emotionData: [{dictLabel: '中性', dictValue: '0'}, {dictLabel: '敏感', dictValue: '1'}, {
        dictLabel: '非敏感',
        dictValue: '2'
      }],
      searchPositionList: [{dictLabel: '标题', dictValue: '1'}, {dictLabel: '正文', dictValue: '2'}],

      sourceList: [],

      messageSetting: true,//短信报送是否置灰


      grading: {},
      dayStatus: 0,
      weekStatus: 0,
      monthStatus: 0,
      settingForm: {
        status: 0,
        reportType: 1,
        tempId: '',
        reportDate: [],
        reportTime: '',
        users: '',
        planId: '',

      },
      weekSettingForm: {},
      monthSettingForm: {},
      setType: '',


      weekEmailContactsList: [],
      monthEmailContactsList: [],
      emailContactsList: [],//邮件推送联系人列表
      phoneContactsList: [],//短信推送联系人列表
      wechatContactsList: [],//微信推送联系人列表

      contactsListType: '',//确定打开弹窗设置的是哪种接收方式   0: 短信 1: 邮件 2: 微信


      sendMsgDialog: false,
      searchWord: '',
      contactsList: [],
      selectedRows: [],


      contactsDialog: false,
      contactsRow: {},
      contactsOption: 'add',


      rules: {
        tempId: [{required: true, message: '请选择报告模板', trigger: 'blur'}],
        reportDate: [{required: true, message: '请至少选择一个时间周期', trigger: 'blur'}],
        reportTime: [{required: true, message: '请选择时间', trigger: 'blur'}],
        users: [{required: true, message: '请选择推送人', trigger: 'blur'}],
      },
      btnLoading: false,
      optionProps: {
        multiple: true,
        value: 'id',
        label: 'name',
        children: 'children',
        emitPath: false,
        checkStrictly: true
      },

      lexiconDialogVisible: false,
      lexiconType: '',
    }
  },

  watch: {
    settingForm: {
      immediate: true, // 确保在初始渲染时也能执行此函数
      deep: true,
      handler(newval, oldVal) {
        // 选择30分钟以下，短信设置置灰，不可设置；若已设置短信接收人，切换至30分钟以下，则预警不触发
        if (newval.warningType == 0 && newval.intervalTime < 30) {
          this.settingForm.message = 0
          this.messageSetting = false
        } else {
          this.messageSetting = true
        }
      },
    },
  },
  async mounted() {
    await this.querySysList()
    // this.getWarnSet()
  },
  methods: {

    init() {
      this.getWarnSet()
    },

    // 获取搜索字段列表
    async querySysList() {
      // 获取媒体类型
      // try {
      //     let res = await this.getDicts('sys_media_type')
      //     this.mediaList = res.data
      //     // let choseList = this.mediaList.map((item)=>item.dictValue)
      //     // this.settingForm.type = choseList
      // } catch (error) {
      //     this.$message.error(error)
      // }
      // 获取报告模板
      const res = await NolistTemplate()
      this.tempLists = res.data
      // 获取账号类型
      try {
        let res = await this.getDicts('account_level')
        this.sourceList = res.data
      } catch (error) {
        this.$message.error(error)
      }
    },

    //重新设置接收人

    async resetContacts(type, setType) {
      this.setType = setType
      this.sendMsgDialog = true
      //确定打开弹窗设置的是哪种接收方式
      this.contactsListType = type
      await this.getContactsList()
      // 0: 短信 1: 邮件 2: 微信
      if (this.contactsListType == '1') {
        this.toggleSelection(this.emailContactsList)
        this.toggleSelection(this.weekEmailContactsList)
        this.toggleSelection(this.monthEmailContactsList)
      } else if (this.contactsListType == '0') {
        this.toggleSelection(this.phoneContactsList)
      } else if (this.contactsListType == '2') {
        this.toggleSelection(this.wechatContactsList)
      }
    },

    //同步接收人表单的已选项
    toggleSelection(list) {
      this.$refs.contactsTableRef.clearSelection();
      if (!list || list?.length == 0) return
      this.contactsList.forEach(row => {
        list?.forEach(rows => {
          if (row.id === rows.id) {
            this.$refs.contactsTableRef.toggleRowSelection(row, true)
          }
        })
      })
    },

    //关闭接收人弹窗
    cancelMsgDialog() {
      this.sendMsgDialog = false
      this.searchWord = ''
      // this.updateVisible()
    },
    //获取联系人列表
    async getContactsList() {
      let param = {
        username: this.searchWord,
        type: this.contactsListType
      }
      await getContactsApi(param).then(res => {
        this.contactsList = res.data
      })
    },

    //table选中项改变
    handleSelectionChange(val) {
      this.selectedRows = val
    },


    // 提交表单
    submitSendMsgForm() {
      let rows = JSON.parse(JSON.stringify(this.selectedRows))
      // 0: 短信 1: 邮件 2: 微信
      if (this.contactsListType == '1') {
        if (this.setType == 'day') {
          this.emailContactsList = rows
          this.settingForm.users = this.emailContactsList
        } else if (this.setType == 'week') {
          this.weekEmailContactsList = rows
          this.weekSettingForm.users = this.weekEmailContactsList
        } else {
          this.monthEmailContactsList = rows
          this.monthSettingForm.users = this.monthEmailContactsList
        }


      } else if (this.contactsListType == '0') {
        this.phoneContactsList = rows
      } else if (this.contactsListType == '2') {
        this.wechatContactsList = rows
      }
      this.cancelMsgDialog()
    },

    //删除对应接收方式的联系人列表
    delContactsList(key, row) {
      this[key] = this[key].filter(item => item.id !== row.id);
    },

    contactsVisibleChange(val) {
      this.contactsDialog = val
    },


    addNewUser() {
      this.contactsDialog = true
      this.contactsOption = 'add'
    },

    async updataRow(row) {
      this.contactsDialog = true
      this.contactsOption = 'edit'
      this.contactsRow = row

    },

    //新增编辑联系人弹窗回调
    contactsSubmit(res) {
      this.getContactsList()
    },


    //邮箱验证
    checkEmail(row) {
      if (!row.email) {
        this.$message.error('邮箱不存在!')
        return
      }
      this.$confirm(`是否向邮箱${row.email}发送验证邮件？`, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          toEmail: row.email
        }
        mailTestApi(params).then(res => {
          if (res.code == 200) {
            this.$message.success('发送成功!')
          }
        })
      }).catch(() => {
      });
    },


    //删除联系人
    async delRow(row) {
      try {
        await delContactsApi(row.id)
        this.$message.success('删除成功')
        this.getContactsList()
      } catch (err) {

      }
    },


    // 回显数据字典 name-->id
    selectDictId(datas, value) {
      var actions = [];
      Object.keys(datas).some((key) => {
        if (datas[key].typeName == value) {
          actions.push(datas[key].typeId);
          return true;
        }
      })
      return actions.join('');
    },


    // 清空文本域
    clearTextarea(item) {
      this.settingForm[item] = ''
    },

    changeStatus(val) {
      let id;
      let status;
      if (val === '1') {   // 日报
        id = this.settingForm.id;
        status = this.dayStatus;
      } else if (val === '2') {   // 周报
        id = this.weekSettingForm.id;
        status = this.weekStatus;
      } else {  // 月报
        id = this.monthSettingForm.id;
        status = this.monthStatus;
      }
      const params = {
        status: status,
        planId: this.checkedNode.id,
        id: id
      };

      updatePlanReport(params).then(res => {
        if (res.code == 200) {
          this.$message.success('操作成功')
        }
      })
    },
    //获取报告设置
    getWarnSet() {
      // 日报
      let params = {
        planId: this.checkedNode.id,
        reportType: 1
      }
      planReportApi(params).then(res => {
        if (res.code == 200) {
          console.log('1 :>> ', res);
          if (res.rows.length > 0) {
            this.settingForm = res.rows[0]
            this.dayStatus = this.settingForm.status
            this.settingForm.reportDate = this.settingForm.reportDate.split(',')
            this.emailContactsList = this.settingForm.users
          } else {
            this.dayStatus = 0
          }
        }
      })

      // 周报
      let weekParams = {
        planId: this.checkedNode.id,
        reportType: 2
      }
      planReportApi(weekParams).then(res => {
        if (res.code == 200) {
          console.log('2 :>> ', res);
          if (res.rows.length > 0) {
            this.weekSettingForm = res.rows[0]
            this.weekStatus = this.weekSettingForm.status
            this.weekEmailContactsList = this.weekSettingForm.users
          } else {
            this.weekStatus = 0
          }
        }
      })

      // 月报
      let monthParams = {
        planId: this.checkedNode.id,
        reportType: 3
      }
      planReportApi(monthParams).then(res => {
        if (res.code == 200) {
          console.log('3 :>> ', res);
          if (res.rows.length > 0) {
            this.monthSettingForm = res.rows[0]
            this.monthStatus = this.monthSettingForm.status
            this.monthEmailContactsList = this.monthSettingForm.users
          } else {
            this.monthStatus = 0
          }
        }
      })


    },
    // 提交日报设置
    daySubmitForm(ref) {
      this.$refs[ref].validate((valid) => {
        if (valid) {
          //   日报1 周报2 月报3
          console.log('this.settingForm :>> ', this.settingForm);
          this.settingForm.reportType = 1
          let form = JSON.parse(JSON.stringify(this.settingForm))
          form.reportDate = this.settingForm.reportDate.join(',')
          form.users = this.emailContactsList
          form.status = this.dayStatus
          let params = {
            ...form,
            planId: this.checkedNode.id,
          }
          console.log('params', params)
          if (this.settingForm.reportTime && form.users.length > 0) {
            updatePlanReport(params).then(res => {
              if (res.code == 200) {
                this.$message.success('保存成功')
              }
            })

          } else {
            if (!this.settingForm.reportTime) {
              this.$message.error('请选择具体时间点');
            }
            if (form.users.length === 0) {
              this.$message.error('请选择推送人');
            }
          }

        }
      })
    },
    // 提交周报设置
    weekSubmitForm(ref) {
      this.$refs[ref].validate((valid) => {
        if (valid) {
          this.weekSettingForm.reportType = 2
          let form = JSON.parse(JSON.stringify(this.weekSettingForm))
          form.status = this.weekStatus
          form.users = this.weekEmailContactsList
          let params = {
            ...form,
            planId: this.checkedNode.id,
          }
          console.log('params', params)
          if (this.weekSettingForm.reportTime && form.users.length > 0) {
            updatePlanReport(params).then(res => {
              if (res.code == 200) {
                this.$message.success('保存成功')
              }
            })

          } else {
            if (!this.weekSettingForm.reportTime) {
              this.$message.error('请选择具体时间点');
            }
            if (form.users.length === 0) {
              this.$message.error('请选择推送人');
            }
          }
        }
      })
    },
    // 提交月报设置
    monthSubmitForm(ref) {
      this.$refs[ref].validate((valid) => {
        if (valid) {
          this.monthSettingForm.reportType = 3
          let form = JSON.parse(JSON.stringify(this.monthSettingForm))
          form.status = this.monthStatus
          form.users = this.monthEmailContactsList
          let params = {
            ...form,
            planId: this.checkedNode.id,
          }
          console.log('params', params)
          if (this.monthSettingForm.reportTime && form.users.length > 0) {
            updatePlanReport(params).then(res => {
              if (res.code == 200) {
                this.$message.success('保存成功')
              }
            })

          } else {
            if (!this.monthSettingForm.reportTime) {
              this.$message.error('请选择具体时间点');
            }
            if (form.users.length === 0) {
              this.$message.error('请选择推送人');
            }
          }
        }
      })
    },


    // 重置表单
    reset() {
      this.settingForm = {
        // 表单数据
        typeId: '',
        kw1: '',
        excludeWord: '',
      }
      this.$nextTick(() => {
        this.$refs['form'].resetFields()
      })
    },


    // 复制
    copyText(content) {
      copyText(content, false)
    },
    lexiconVisibleChange(val) {
      this.lexiconDialogVisible = val
    },

    openLexiconDialog(key) {
      this.lexiconDialogVisible = true
      // if (key == 'kw1') {
      //     this.lexiconType = 'kw1'
      // }
      this.lexiconType = key
    },

    submitWords(type, wordsList) {
      // console.log(type, wordsList);
      if (wordsList.length == 0 || !type) return

      //无该字段时，赋初始值为空字符串
      if (!this.settingForm[type]) {
        this.settingForm[type] = ''
      }

      if (type == 'monitorWord') {
        //有值且最后一位不为+时，添加+作为间隔
        if (this.settingForm[type] && this.settingForm[type]?.length > 0 && this.settingForm[type]?.slice(-1) !== '+') {
          this.settingForm[type] += '+'
        }
        this.settingForm[type] += `(${wordsList.join('|')})`
      } else {
        //有值且最后一位不为空格时，添加空格作为间隔
        if (this.settingForm[type] && this.settingForm[type]?.length > 0 && this.settingForm[type]?.slice(-1) !== ' ') {
          this.settingForm[type] += ' '
        }
        this.settingForm[type] += wordsList.join(' ')
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.tipIconGrey {
  position: absolute;
  left: -90px;
  top: 12px;
  cursor: pointer;
  color: #fff;
  font-size: 16px;
}

.el-textarea__icon {
  color: #247CFF;
  text-align: end;
  cursor: pointer;
  width: fit-content;
  float: right;
  margin-left: 10px;

  img {
    height: 15px;
    vertical-align: text-bottom;
  }
}


.reportSetting {
  padding: 30px 20px;
  font-family: PingFangSC, PingFang SC;

  .settingSwitch {
    padding: 30px 60px;

    .settingLable {
      display: inline-block;
      vertical-align: middle;
      font-size: 16px;
    }

    .switch {
      vertical-align: middle;
      margin-left: 15px;
      margin-right: 20px;
    }

    .settingTip {
      display: inline-block;
      vertical-align: middle;
      font-size: 12px;
      color: #999999;
    }
  }

  .formMain {
    ::v-deep .el-form-item__label {
      font-weight: 400;
      line-height: 40px;
    }

    ::v-deep .el-textarea__inner {
      font-family: Arial;
    }

    .bluePoint {
      width: 8px;
      height: 8px;
      background: #247CFF;
      border-radius: 50%;
      margin-right: 9px;
    }

    .formSetPart2 {
      // margin-left: 50px;
    }

    .formH2 {
      font-size: 18px;
      line-height: 25px;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;

      div {
        display: inline-block;
        vertical-align: middle;
      }
    }


    .sourceLever {
      width: 60%;
      padding-left: 78px;

      .sourceLeverRow {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;

        .sourceLever-title {
          // font-size: 14px;
          // color: #333333;
          width: 20%;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
        }

        .sourceLever-item {
          flex: 1;
          display: flex;
          justify-content: space-between;

          & > * {
            width: 70px;
            text-align: center;
          }

          ::v-deep.el-radio__label {
            padding-left: 0;
          }
        }

      }

      .topRow {
        .sourceLever-title {
          font-weight: 600;
          font-size: 14px;
          color: #333333;
        }

        .sourceLever-item {
          font-weight: 600;
          font-size: 14px;
          color: #333333;
        }
      }

    }

    .receiveWay {
      .receiveTitle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px solid #EEEEEE;
        width: 80%;

        img {
          vertical-align: middle;
        }
      }

      .receiveMain {
        width: 100%;
        margin-bottom: 20px;

        .receiveMain-option {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 10px 0;
          width: 80%;
        }

        ::v-deep.el-table .el-table__header-wrapper th,
        .el-table .el-table__fixed-header-wrapper th {
          padding-top: 0;
          padding-bottom: 0;
          background-color: #F8FAFF;
        }
      }
    }

  }

}

.explain-words {
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  line-height: 24px;
}

.yellowTips {
  white-space: nowrap;
  line-height: 32px;
  background: #FFFBE6;
  border-radius: 4px;
  border: 1px solid #FAAD14;
  padding: 0px 10px;

  img {
    vertical-align: middle;
    height: 17px;
    margin-right: 5px;
  }

  span {
    vertical-align: middle;
  }
}

</style>
