<template>
  <div class="contain" v-loading="loading">
    <div class="content">
      <div class="left">
        <el-input
          type="textarea"
          :rows="26"
          maxlength="1000"
          show-word-limit
          :placeholder="exampleText"
          v-model="textarea"
        >
        </el-input>
        <el-button type="primary" style="margin-top: 50px" @click="getResolve"
        >识别
        </el-button
        >
      </div>
      <div class="line"></div>
      <div class="right">
        <el-form ref="form" :model="form" :rules="rules" label-width="122px">
          <el-form-item
            label="文章链接："
            prop="articleLink"
            :rules="[
              { required: true, validator: validURL, trigger: 'blur' },
              {
                min: 0,
                max: 600,
                message: '长度在600个字符以内',
                trigger: 'blur',
              },
            ]"
          >
            <div style="display: flex">
              <el-input
                v-model.trim="form.articleLink"
                placeholder="请输入格式为http、https开头的链接，600字符以内"
                style="width: 70%"
                @input="inputLink"
              ></el-input>
              <el-button
                type="warning"
                style="margin-left: 10px"
                :disabled="linkDisabled"
                @click="getLink"
                :loading="linkLoading"
              >链接解析
              </el-button
              >
            </div>
          </el-form-item>
          <el-form-item label="文章标题：" prop="articleTitle">
            <el-input
              v-model.trim="form.articleTitle"
              placeholder="请输入100字符以内的文章标题"
              style="width: 70%"
            ></el-input>
          </el-form-item>
          <el-form-item label="原文时间：" prop="originalTime">
            <el-date-picker
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期"
              v-model="form.originalTime"
              style="width: 70%"
              :picker-options="timeOption"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="信息类别：" prop="infoType">
            <SelectAdd
              style="width: 70%"
              :activeWidth="'70%'"
              ref="selectAdd"
              :actIndustryIds="groupOption"
              v-model="form.infoType"
              @saveActive="saveActive"
              @chooseActive="chooseActive"
            />
          </el-form-item>
          <el-form-item label="媒体类型：" prop="mediaType">
            <el-select
              v-model="form.mediaType"
              placeholder="请选择媒体类型"
              style="width: 70%"
            >
              <el-option
                v-for="item in mediaList"
                :label="item.dictLabel"
                :value="item.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="涉事地域：" prop="articleArea">
            <treeselect
              style="width: 70%"
              ref="treeSelect"
              noOptionsText="没有数据"
              noResultsText="暂无结果"
              v-model="form.articleArea"
              :options="deptOptions"
              :normalizer="normalizer"
              placeholder="请选择地域"
            />
          </el-form-item>
          <el-form-item label="倾向性：" prop="tendency">
            <el-select
              v-model="form.tendency"
              placeholder="请选择倾向性"
              style="width: 70%"
            >
              <el-option
                v-for="item in tendencyList"
                :label="item.dictLabel"
                :value="item.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="信息描述：">
            <el-input
              type="textarea"
              style="width: 70%"
              :autosize="{ minRows: 4, maxRows: 6}"
              maxlength="2000"
              show-word-limit
              v-model.trim="form.eventDesc"
              placeholder="请输入2000字符以内的信息描述"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="处置建议：">
            <el-input
              type="textarea"
              style="width: 70%"
              :autosize="{ minRows: 4, maxRows: 6}"
              maxlength="2000"
              show-word-limit
              v-model.trim="form.suggestions"
              placeholder="请输入2000字符以内的处置建议"
            >
            </el-input>
          </el-form-item>
          <!-- 管理者才有 -->
          <el-form-item
            label="处置截止时间："
            prop="deadlineNum"
            v-if="$route.query.flag"
          >
            <el-select
              v-model="form.deadlineNum"
              placeholder="请选择"
              style="width: 70%"
              :disabled="$route.path == '/send/updateSend'"
            >
              <el-option
                v-for="item in deadTimeList"
                :label="item.dictLabel"
                :value="item.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- 报送接收人 -->
          <el-form-item
            label="接收人："
            prop="managerUser"
            v-if="
              $route.path == '/submit/updateSubmit' ||
              $route.path == '/submit/addSubmit'
            "
          >
            <treeselect
              style="width: 70%"
              disableBranchNodes
              :disabled="$route.path == '/submit/updateSubmit'"
              noOptionsText="没有数据"
              noResultsText="暂无结果"
              v-model="form.managerUser"
              :options="managerList"
              :normalizer="managerUser"
              placeholder="请选择接收人"
            />
          </el-form-item>
          <!-- 审核接收人 -->
          <el-form-item
            label="接收人："
            prop="disposers"
            v-if="
              $route.path == '/send/updateSend' ||
              $route.path == '/send/addSend'
            "
          >
            <treeselect
              style="width: 70%"
              value-consists-of="LEAF_PRIORITY"
              :disabled="$route.path == '/send/updateSend'"
              :multiple="true"
              noOptionsText="没有数据"
              noResultsText="暂无结果"
              v-model="form.disposers"
              :options="processorList"
              :normalizer="managerUser"
              placeholder="请选择接收人"
            />
          </el-form-item>
          <el-form-item label="附件：">
            <div style="width: 370px">
              <FileUpload
                ref="files"
                @pageLoad="pageLoad"
                :fileLimit="10"
                :fileSize="50"
                acceptType=".png,.jpeg,.jpg,.pdf,.doc,.docx,.xlsx,.xls"
                v-model="form.files"
              />
            </div>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              :disabled="disabled"
              :loading="btnLoading"
              @click="onSubmit"
            >确定
            </el-button
            >
            <el-button @click="cancelForm">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {valideURL} from "@/utils/validate.js";
import FileUpload from "@/components/UploadFiles/index";
import AddressParse, {AREA, Utils} from "address-parse";
import {decryptByAES, encryptByAES} from "@/utils/jsencrypt";
import {getUrlByFileIds} from "@/api/fileList/fileIndex";
import {infoOne} from "@/api/search/index";
import {
  sureAddSubmitApi,
  getInfoDetailsApi,
  getAreaTreeApi,
  updateSubmitApi,
  getInfoTypeList,
  AddTypeApi,
  getManager,
  getProcessor,
  getSolrDataByUrl,
} from "@/api/infoSubmit/index";

export default {
  name: "addSubmit",
  components: {
    FileUpload,
    Treeselect,
  },
  data() {
    return {
      exampleText: `粘贴信息，自动拆分文章链接、标题、时间等，限制1000字符，信息格式如下：
文章链接:https://www.toutiao.com/i7316139521547043366
文章标题:电池产能“结构性过剩”，钠电的未来在哪里?
信息类别:新型显示产业
原文时间:2023-12-24 12:00:00
媒体类型:微博
涉事地域:合肥
倾向性:敏感`,
      timeOption: {
        disabledDate: this.endDisable,
      },
      loading: false,
      btnLoading: false,
      linkLoading: false,
      validURL: valideURL,
      textarea: "",
      original: "",
      mediaList: [],
      tendencyList: [],
      deadTimeList: [
        {dictValue: "暂无", dictLabel: "暂无"},
        {dictValue: "24H", dictLabel: "24H"},
        {dictValue: "48H", dictLabel: "48H"},
        {dictValue: "12H", dictLabel: "12H"},
      ],
      deptOptions: [],
      groupOption: [],
      managerList: [],
      processorList: [],
      form: {
        articleLink: "",
        articleTitle: "",
        originalTime: "",
        infoType: "",
        mediaType: "",
        articleArea: null,
        articleAreaName: "",
        tendency: "",
        managerUser: "",
        disposers: [],
        deadlineNum: "",
        eventDesc: "",
        suggestions: "",
        files: [],
      },
      rules: {
        articleTitle: [
          {required: true, message: "请输入文章标题", trigger: "blur"},
          {min: 0, max: 100, message: "长度在100个字符以内", trigger: "blur"},
        ],
        originalTime: [
          {required: true, message: "选择日期", trigger: "blur"},
        ],
        infoType: [
          {required: true, message: "请选择信息类别", trigger: "blur"},
        ],
        mediaType: [
          {required: true, message: "请选择媒体类型", trigger: "blur"},
        ],
        articleArea: [
          {required: true, message: "请选择涉事地域", trigger: "blur"},
        ],
        tendency: [
          {required: true, message: "请选择倾向性", trigger: "blur"},
        ],
        managerUser: [
          {required: true, message: "请选择接收人", trigger: "blur"},
        ],
        disposers: [
          {required: true, message: "请选择接收人", trigger: "blur"},
        ],
        deadlineNum: [{required: true, message: "请选择", trigger: "blur"}],
      },
      disabled: false,
      linkDisabled: true,
    };
  },
  created() {
    this.getDict();
    this.resetForm();
    this.textarea = "";
    // 快捷报送
    if (this.$route.query.sendId) {
      this.loading = true;
      infoOne({id: this.$route.query.sendId, time: this.$route.query.time}).then((res) => {
        this.form = res.data.detail;
        this.$set(this.form, 'articleLink', this.form.url);
        this.$set(this.form, 'articleTitle', this.form.title);
        this.$set(this.form, 'originalTime', this.form.time);
        this.$set(this.form, 'mediaType', String(this.form.type));
        this.form.articleArea = this.form.textAddress[0]
        this.$set(this.form, 'tendency', String(this.form.emotional));
        this.form.files = [];
        let fileIds = res.data.detail.files;
        fileIds = fileIds.map((item) => item.id);
        if (fileIds.length != 0) {
          this.queryFilesById(fileIds);
        }
      })
    }
    if (this.$route.query.id) {
      this.loading = true;
      getInfoDetailsApi(this.$route.query.id).then((res) => {
        if (this.$takeAES) {
          const resDecry = JSON.parse(decryptByAES(res.data));
          this.form = resDecry.info;
          this.form.files = [];
          let fileIds = resDecry.files;
          fileIds = fileIds.map((item) => item.id);
          if (fileIds.length != 0) {
            this.queryFilesById(fileIds);
          }
        } else {
          this.form = res.data.info;
          this.form.files = [];
          let fileIds = res.data.files;
          fileIds = fileIds.map((item) => item.id);
          if (fileIds.length != 0) {
            this.queryFilesById(fileIds);
          }
        }
        this.linkDisabled = !this.form.articleLink;  // 链接解析是否可以点击
        const queryType = {
          typeName: "",
          pageQuery: false,
        };
        // 加密
        if (this.$takeAES) {
          const formKeys = encryptByAES(JSON.stringify(queryType));
          getInfoTypeList({encryptJson: formKeys}).then((res) => {
            this.groupOption = JSON.parse(decryptByAES(res.encryptRows));
            this.loading = false;
            this.groupOption.map((item) => {
              if (item.id == this.form.infoType) {
                this.form.infoType = item.typeName;
                this.$set(item, "tag", true);
              } else {
                this.$set(item, "tag", false);
              }
            });
          });
        } else {
          getInfoTypeList(queryType).then((res) => {
            this.groupOption = res.rows;
            this.loading = false;
            this.groupOption.map((item) => {
              if (item.id == this.form.infoType) {
                this.form.infoType = item.typeName;
                this.$set(item, "tag", true);
              } else {
                this.$set(item, "tag", false);
              }
            });
          });
        }
      });
    }
  },
  mounted() {
    let container = document.getElementsByClassName('topbar-container')
    if (container.length > 0) {
      let links = container[0].querySelectorAll('a');
      let haveSend = ''
      let haveSubmit = ''
      // 遍历这些a标签
      links.forEach((link) => {
        // 检查href属性是否包含"infoSend"
        if (link.href.includes('infoSend')) {
          let liEle = link.querySelector('li')
          haveSend = liEle
        }
        if (link.href.includes('infoSubmit')) {
          haveSubmit = link.querySelector('li')
        }
      });
      if (this.$route.path == '/send/addSend' || this.$route.path == '/send/updateSend') {
        setTimeout(() => {
          haveSend.classList.add('is-active');
        }, 0);
      }
      if (this.$route.path == '/submit/addSubmit' || this.$route.path == '/submit/updateSubmit') {
        setTimeout(() => {
          haveSubmit.classList.add('is-active');
        }, 0);
      }
    }
  },
  methods: {
    // 结束日期规则
    endDisable(time) {
      return time.getTime() > new Date().getTime();
    },
    // 上传文件状态
    pageLoad(load) {
      this.disabled = load;
    },
    // 获取字典
    getDict() {
      // 媒体类型
      this.getDicts("sys_media_type").then((response) => {
        this.mediaList = response.data;
      });
      // 倾向性
      this.getDicts("tendency_type").then((response) => {
        this.tendencyList = response.data;
      });
      this.loading = true;
      // 获取地域
      getAreaTreeApi().then((response) => {
        this.loading = false;
        this.deptOptions = response.data;
      });
      // 信息类别
      const queryType = {
        typeName: "",
        pageQuery: false,
      };
      // 加密
      if (this.$takeAES) {
        const formKeys = encryptByAES(JSON.stringify(queryType));
        getInfoTypeList({encryptJson: formKeys}).then((res) => {
          this.groupOption = JSON.parse(decryptByAES(res.encryptRows));
        });
      } else {
        getInfoTypeList(queryType).then((res) => {
          this.groupOption = res.rows;
        });
      }
      if (this.$route.query.flag) {
        // 审核-接收人员
        getProcessor().then((res) => {
          this.processorList = this.$takeAES ? JSON.parse(decryptByAES(res.data)) : res.data;
        });
      } else {
        // 报送-接收人员
        getManager().then((res) => {
          this.managerList = this.$takeAES ? JSON.parse(decryptByAES(res.data)) : res.data;
        });
      }
    },
    // 接收人转换
    managerUser(node) {
      if (node.children == null || node.children == "null") {
        delete node.children;
      }
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      };
    },
    // 添加分类
    saveActive(addActive) {
      if (addActive) {
        let params;
        if (this.$takeAES) {
          const formKeys = encryptByAES(JSON.stringify({typeName: addActive}));
          params = {encryptJson: formKeys};
        } else {
          params = {typeName: addActive};
        }
        AddTypeApi(params).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg);
            this.$refs.selectAdd.resetAddActive();
            const queryType = {
              typeName: "",
              pageQuery: false,
            };
            // 加密
            if (this.$takeAES) {
              const formKeys = encryptByAES(JSON.stringify(queryType));
              getInfoTypeList({encryptJson: formKeys}).then((res) => {
                this.groupOption = JSON.parse(decryptByAES(res.encryptRows));
                this.groupOption.forEach(item => {
                  this.$set(item, "tag", item.typeName === this.form.infoType);
                });
              });
            } else {
              getInfoTypeList(queryType).then((res) => {
                this.groupOption = res.rows;
                this.groupOption.forEach(item => {
                  this.$set(item, "tag", item.typeName === this.form.infoType);
                });
              });
            }
          }
        });
      } else {
        this.msgInfo("请输入");
      }
    },
    // 选中分类
    chooseActive(item) {
      this.$forceUpdate();
      this.groupOption.forEach((itema) => {
        itema.tag = itema === item;
      });
    },
    // 文件解密
    queryFilesById(fileId) {
      getUrlByFileIds(fileId).then((res) => {
        let decryptFile = this.$takeAES ? JSON.parse(decryptByAES(res.encryptRows)) : res.rows;
        decryptFile.map((item) => {
          item.name = item.originalName;
        });
        this.form.files = decryptFile;
        this.$refs.files.setFileList(decryptFile);
      });
    },
    // 地域转换
    normalizer(node) {
      if (node.children == null || node.children == "null") {
        delete node.children;
      }
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    // 提交
    onSubmit() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          this.btnLoading = true;
          this.form.files = this.form.files.map((item) => {
            return {fileId: item.id};
          });
          let paraMsg = this.groupOption.find(
            (item) => item.typeName === this.form.infoType
          );
          this.form.articleAreaName = this.$refs.treeSelect
            .getNode(this.$refs.treeSelect.value)
            .nestedSearchLabel.replace(/\s/g, "");
          let paramsForm = {...this.form};
          paramsForm.infoType = String(paraMsg.id);
          let params = undefined;
          // 加密
          if (this.$takeAES) {
            const formKeys = encryptByAES(JSON.stringify(paramsForm));
            params = {encryptJson: formKeys};
          } else {
            params = paramsForm;
          }
          if (
            this.$route.path == "/submit/addSubmit" ||
            this.$route.path == "/send/addSend"
          ) {
            sureAddSubmitApi(params).then(() => {
              this.btnLoading = false;
              this.msgSuccess("新增成功");
              if (this.$route.path == "/submit/addSubmit") {
                this.$router.push("/infoSubmit");
              } else if (this.$route.path == "/send/addSend") {
                this.$router.push("/infoSend");
              }
              this.textarea = "";
              this.resetForm();
              this.$refs.form.resetFields();
              this.groupOption.map((itema) => {
                itema.tag = false;
              });
            });
          } else {
            updateSubmitApi(params).then((res) => {
              this.btnLoading = false;
              this.msgSuccess("修改成功");
              if (this.$route.path == "/submit/updateSubmit") {
                this.$router.push("/infoSubmit");
              } else if (this.$route.path == "/send/updateSend") {
                this.$router.push("/infoSend");
              }
            });
          }
        }
      });
    },
    // 取消
    cancelForm() {
      this.$refs.form.clearValidate();
      if (
        this.$route.path == "/submit/updateSubmit" ||
        this.$route.path == "/submit/addSubmit"
      ) {
        this.$router.push("/infoSubmit");
      } else if (
        this.$route.path == "/send/updateSend" ||
        this.$route.path == "/send/addSend"
      ) {
        this.$router.push("/infoSend");
      }
    },
    // 识别
    getResolve() {
      this.textarea = this.textarea.replace(/：/g, ":");
      const array = this.textarea.split(/\r?\n|\r/);
      for (let i = 0; i < array.length; i++) {
        const line = array[i];
        if (line.startsWith("文章标题:")) {
          // this.form.articleTitle = line.substring("文章标题:".length).trim();
          let articleTitle = line.substring("文章标题:".length).trim()
          if(articleTitle.length > 20){
            this.form.articleTitle = articleTitle.substring(0,20) + "..." || null
            this.form.eventDesc = articleTitle.substring(0,2000) || null
          }else{
            this.form.articleTitle = articleTitle;
          }
        } else if (line.startsWith("文章链接:")) {
          this.form.articleLink = line.substring("文章链接:".length).trim();
        } else if (line.startsWith("信息类别:")) {
          this.form.infoType = line.substring("信息类别:".length).trim();
        } else if (line.startsWith("原文时间:")) {
          this.form.originalTime = line.substring("原文时间:".length).trim();
        } else if (line.startsWith("媒体类型:")) {
          this.form.mediaType = line.substring("媒体类型:".length).trim();
        } else if (line.startsWith("涉事地域:")) {
          this.form.articleArea = line.substring("涉事地域:".length).trim() || null
        } else if (line.startsWith("倾向性:")) {
          this.form.tendency = line.substring("倾向性:".length).trim();
        }
      }
      if (this.form.articleLink) {
        this.linkDisabled = false;
      } else {
        this.linkDisabled = true;
      }
      if (this.form.originalTime) {
        if (new Date(this.form.originalTime).getTime() > new Date().getTime()) {
          this.form.originalTime = "";
        }
      }
      if (this.form.infoType) {
        const infoValue = this.groupOption.filter(
          (item) => item.typeName === this.form.infoType
        );
        if (infoValue.length == 0) {
          this.form.infoType = null;
        } else {
          this.form.infoType = infoValue[0].typeName;
          this.groupOption.map((item) => {
            if (this.form.infoType == item.typeName) {
              item.tag = true;
            } else {
              item.tag = false;
            }
          });
        }
      }
      if (this.form.mediaType) {
        this.form.mediaType = this.mediaList.find(
          (item) => item.dictLabel === this.form.mediaType
        )?.dictValue;
      }
      if (this.form.tendency) {
        this.form.tendency = this.tendencyList.find(
          (item) => item.dictLabel === this.form.tendency
        )?.dictValue;
      }
      if (this.form.articleArea) {
        // 将地域中文转成code编码
        const areaValue = AddressParse.parse(this.form.articleArea);
        this.form.articleArea = areaValue[0].code || null;
      }
      const copyForm = {...this.form}
      delete copyForm.files
      const isEmpty = Object.values(copyForm).every(value => !value);
      if (isEmpty) {
        this.$message.error("请先按照格式填写识别内容");
      }
    },
    inputLink() {
      if (this.form.articleLink) {
        this.linkDisabled = false;
      } else {
        this.linkDisabled = true;
      }
    },
    // 链接解析
    getLink() {
      this.linkLoading = true;
      getSolrDataByUrl(this.form.articleLink).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            // this.form.articleTitle = res.data.title;

            let articleTitle = res.data.title
            if(articleTitle.length > 20){
              this.form.articleTitle = articleTitle.substring(0,20) + "..." || null
              this.form.eventDesc = articleTitle.substring(0,2000) || null
            }else{
              this.form.articleTitle = articleTitle;
            }

            this.form.originalTime = res.data.time;
            this.form.mediaType = res.data.type;
            this.linkLoading = false;
          } else {
            this.linkLoading = false;
            this.msgError(res.msg);
          }
        } else {
          this.linkLoading = false;
        }
      }).catch(() => {
        this.linkLoading = false;
      });
    },
    // 重置form
    resetForm() {
      this.form = {
        articleLink: undefined,
        articleTitle: undefined,
        originalTime: undefined,
        infoType: undefined,
        mediaType: undefined,
        articleArea: null,
        articleAreaName: undefined,
        tendency: undefined,
        deadlineNum: undefined,
        eventDesc: undefined,
        managerUser: undefined,
        disposers: undefined,
        suggestions: undefined,
        files: [],
      };
      this.$nextTick(() => {
        this.$refs.files.setFileList([]);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.contain {
  background: #f7f8fa;
  min-height: calc(100vh - 124px);
  padding: 1px;

  .content {
    box-sizing: border-box;
    min-height: calc(100vh - 124px);
    background: #ffffff;
    padding: 24px 20px 24px 68px;
    margin: 16px 20px 20px 20px;
    display: flex;

    .left {
      width: 45%;
      margin-top: 26px;
      text-align: center;
    }

    .right {
      width: 55%;
      margin-top: 26px;
    }

    .line {
      border: 1px dashed #dcdfe6;
      margin-left: 35px;
      margin-right: 50px;
    }
  }
}
</style>
