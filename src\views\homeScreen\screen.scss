body {
    font-size: 16px;
}

.trend-wrap {
    width: 100%;
    overflow: hidden;

    .trend-main {
        width: 100%;
        overflow: hidden;
        border-bottom: dotted 1px #2f333f;
        height: 70px;
        padding: 6px 0px;
        box-sizing: border-box;
        cursor: pointer;

        .text-title {
            width: 100%;
            overflow: hidden;
            height: 30px;
            line-height: 30px;
            color: #fff;
            text-overflow: ellipsis;
            white-space: nowrap;

            a {
                color: #fff;
                font-size: 0.16rem;
            }
        }
    }

    .text-name {
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        height: 28px;
        line-height: 28px;
        font-size: 0.14rem;

        .source {
            color: #4dadef;
        }

        .text-time {
            color: #fff;
        }

        img {
            width: 14px;
            height: 14px;
            display: inline-block;
            vertical-align: text-top;
            margin-right: 6px;
        }
    }

    .el-tooltip__popper {
        width: 250px;
    }
}

.noneData {
    color: #fff;
    font-size: 0.14rem;
    text-align: center;
    margin-top: 18%;

    width: 100%;

    img {
        width: 0.6rem;
        height: 0.8rem;
    }
}



.dataAcq {
    width: 100%;
    height: 100%;
    background: #040715;
    overflow: hidden;
}

.dataAcqBody {
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.dataHead {
    height: 8vh;
    line-height: 8vh;
    width: 100%;
    overflow: hidden;
    text-align: center;
    background: url("../../assets/images/headBg.png") no-repeat center center;
    background-size: 100% 100%;
}

.dataHead img {
    vertical-align: middle;
}

.jumpPage {
    width: 100%;
    overflow: hidden;
}

.jumpUL {
    // display: inline-block;
    /*float: left;*/
    display: flex;
    flex-direction: row;
    justify-content: start;
    height: 4vh;
    line-height: 4vh;
}

.jumpUL li {
    margin-right: 10px;
    color: #a4a5a7;
    font-size: 0.16rem;
    padding: 0 10px;
    cursor: pointer;
}

.jumpUL li.cur {
    color: #fff;
    background: url("../../assets/images/fontBg.png") no-repeat center center;
    background-size: 120% 130%;
}

.dataCot {
    width: 100%;
    overflow: hidden;
    padding: 0px 34px;
    box-sizing: border-box;
}

.jumpDl {
    height: 4vh;
    line-height: 4vh;
    display: flex;
    flex-direction: row;
    justify-content: right;
    float: right;
    color: #ffff;
    font-size: 0.2rem;
}

.jumpDl dt img {
    vertical-align: middle;
    cursor: pointer;
}

.jumpDl dt {
    margin-right: 10px;
    cursor: pointer;
}

#dataTime {
    cursor: unset;
}

.dataContent {
    width: 100%;
    overflow: hidden;
    margin-top: 0.3rem;
}

.hometablist {
    display: flex;
    flex-direction: row;
    position: absolute;
    top: 0.65rem;
    right: 0.25rem;
    color: #fff;
    font-size: 0.16rem;
    z-index: 110000;

    span {
        cursor: pointer;
    }

    .tab-prev {
        margin-right: 0.2rem;
    }

    .date {
        margin-left: 0.3rem;
        margin-right: 0.2rem;
    }
}

.dataOneFloor {
    width: 100%;
    margin-bottom: 1.5vh;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    position: relative;
}

.addDataOneFloor {
    position: absolute;
    left: 0;
    z-index: 10;
}

.realTime {
    -webkit-text-fill-color: red;
}

.dataCollH2 {
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 2vh;
    line-height: 2vh;
    margin: 1.5vh 0;
}

.dataCollH2 span {
    display: inline-block;
    font-size: 0.15rem;
    background-image: -webkit-linear-gradient(bottom, #3b82f8, #5bc0e1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: normal;
}

.dataCollH2 img {
    display: inline-block;
    vertical-align: middle;
    width: 100%;
}

.spanImg {
    width: 80%;
}

.dataCollectCot {
    width: 25%;
    position: absolute;
    right: 0;
}

.dataCollection {
    width: 100%;
    height: 51vh;
    overflow: hidden;
    background: url("../../assets/images/collectionBg.png") no-repeat center center;
    background-size: 100% 100%;
    padding: 10px 38px;
    box-sizing: border-box;

}

.dataCollection ul {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    /* overflow: hidden; */
}

.dataCollection ul li {
    /*height: 49px;
    line-height: 70px;*/
    color: #fff;
    font-size: 0.2rem;
    height: 48px;
    line-height: 48px;
}

.dataCollection ul li svg {
    display: inline-block;
    float: left;
    vertical-align: middle;
    fill: #fff;
    width: 18px;
    height: 18px;
    margin-right: 10px;
    margin-top: 22px;
}

.dataCollection ul li svg g polyline {
    stroke: #fff;
}

.dataCollection ul li p {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: dotted 1px #2f333f;
}

.dataCollection ul li p em {
    display: inline-block;
    color: #2bb4d7;
    background: url("../../assets/images/fontBg.png") no-repeat center center;
    background-size: 110% 110%;
    padding: 0 10px;
    font-size: 25px;
}

.bigMap {
    width: 44%;
    overflow: hidden;
    position: relative;
}

.addBigMap {
    width: 100%;
}

.dataMap {
    width: 100%;
    overflow: hidden;
    height: 56vh;
}

.addDataMap {
    position: relative;
}

.dataMapHead {
    width: 100%;
    overflow: hidden;
}

.addDataMapHead {
    width: 46%;
    overflow: hidden;
    margin-left: 27%;
    position: absolute;
    z-index: 10;
}

.dataMapHead dl {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    height: 90px;
    overflow: hidden;
}

.dataMapHead dl dt {
    width: 62px;
    height: 74px;
    margin: 0 4px;
    font-size: 0.44rem;
    color: #4aa3f1;
    text-align: center;
    line-height: 74px;
    background: url("../../assets/images/dataBorder.png") no-repeat center center;
    background-size: 100% 100%;
}

.dataMapHead dl dd {
    text-align: center;
    width: 32px;
    font-size: 0.3rem;
    color: #4aa3f1;
    line-height: 115px;
}

.dataNewTime {
    width: 100%;
    height: 51vh;
    overflow: hidden;
    background: url("../../assets/images/collectionBg.png") no-repeat center center;
    background-size: 100% 100%;
    padding: 10px 38px;
    box-sizing: border-box;
}

.dataNewTime ul {
    width: 100%;
    overflow: hidden;
}

.dataNewTime ul li {
    width: 100%;
    overflow: hidden;
    border-bottom: dotted 1px #2f333f;
    height: 70px;
    padding: 6px 0px;
    box-sizing: border-box;
    cursor: pointer;
}

.dataNewTime p {
    width: 100%;
    overflow: hidden;
    height: 30px;
    line-height: 30px;
    color: #fff;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dataNewTime dl {
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 28px;
    line-height: 28px;
    font-size: 0.14rem;
}

.dataNewTime dl dt {
    color: #4dadef;
}

.dataNewTime dl dd img {
    width: 14px;
    height: 14px;
    display: inline-block;
    vertical-align: text-top;
    margin-right: 6px;
}

.dataNewTime dl dd {
    color: #fff;
}

.dataWordMap {
    width: 100%;
    overflow: hidden;
    height: 55.9vh;
}

.dataOneDayTrend {
    width: 74%;
}

.dataOneDataChart {
    width: 100%;
    height: 27vh;
    overflow: hidden;
    background: url("../../assets/images/collectionBg.png") no-repeat center center;
    background-size: 100% 100%;
    padding: 10px 38px;
    box-sizing: border-box;
}

.dataOneDataChart1 {
    width: 100%;
    height: 23vh;
    overflow: hidden;
    background: url("../../assets/images/collectionBg.png") no-repeat center center;
    background-size: 100% 100%;
    padding: 10px 38px;
    box-sizing: border-box;
}

.dataFrom {
    width: 25%;
}

.dataTotalOfPub {
    width: 100%;
    height: 16vh;
    padding: 0 1vh;
    box-sizing: border-box;
    position: relative;
}

.adddataTotalOfPub {
    padding-top: 4vh;
}

.dataOneFloorLeft {
    width: 25%;
}

.dataCollectCotTop {
    width: 100%;
}

.dataEmotionEchart {
    width: 100%;
    height: 30vh;
    overflow: hidden;
}

.dataRank {
    width: 170px;
    height: auto;
    overflow: hidden;
    padding: 6px 10px;
    box-sizing: border-box;
    position: absolute;
    right: 10px;
    bottom: 0;
    border: solid 1px rgb(0, 121, 255, 0.3);
}

.dataRank h2 {
    width: 100%;
    overflow: hidden;
    color: #fff;
    height: 28px;
    line-height: 28px;
    font-weight: normal;
}

.dataRank h2 img {
    width: 20px;
    vertical-align: middle;
}

.dataRank ul {
    width: 100%;
    overflow: hidden;
    max-height: 50vh;
}

.dataRank ul li {
    width: 100%;
    overflow: hidden;
    height: 26px;
    line-height: 26px;
    color: #fff;
    font-size: 12px;
    background: rgb(7, 90, 188, 0.3);
    margin: 3px 0px;
    cursor: pointer;
    padding: 0 6px;
    box-sizing: border-box;
}

.dataRank ul li em {
    display: inline-block;
    float: left;
    margin-right: 8px;
}

.dataRank ul li span {
    display: inline-block;
    float: right;
}