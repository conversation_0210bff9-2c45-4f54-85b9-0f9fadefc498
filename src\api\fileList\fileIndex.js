import request from '@/utils/request'
import fileRequest from '@/utils/fileRequest'

// 上传文件
export function getFileUpload(data) {
  return request({
    url: '/common/uploadFile',
    method: 'post',
    data
  })
}

// 上传附件
export function getUploadMinio(data) {
  return request({
    url: '/file/uploadMinio',
    method: 'post',
    data
  })
}

// 文件传输上传
export function uploadMinioSecret(data) {
  return request({
    url: '/file/uploadMinioSecret',
    method: 'post',
    data
  })
}

// 预览附件
export function getUrlByFileIds(data) {
  return request({
    url: `/file/getUrlByFileIds/${data}`,
    method: 'post',

  })
}

// 下载附件
export function downloadFile(data) {
  return request({
    url: `/file/downloadFile/${data}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 下载压缩文件
export function downloadEncryptFile(data) {
  return request({
    url: `/file/encryptFile/${data}`,
    method: 'get',
    responseType: 'blob'
  })
}

export function encryptFileDownload(data) {
  return fileRequest({
    url: `/file/encryptFile`,
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 获取文件列表数据
export function getFileDataApi(data) {
  return request({
    url: '/file/list',
    method: 'post',
    data
  })
}

//删除文件-废弃
export function delFileApi1(id) {
  return request({
    url: '/upload/file/' + id,
    method: 'delete'
  })
}

// 删除文件
export function delFileApi(id) {
  return request({
    url: '/file/delete/' + id,
    method: 'delete'
  })
}

// 获取人员数据
export function getuserInfoApi() {
  return request({
    url: '/system/dept/treeselectForPeople',
    method: 'get',
  })
}

// 确定新增
export function sureAddFileListApi(data) {
  return request({
    url: '/common/addUser',
    method: 'post',
    data: data
  })
}

// 获取某个文件的可下载人员
export function getFileDownPeopleApi(data) {
  return request({
    url: '/downloadFileRelation/list',
    method: 'get',
    params: data
  })
}

// 删除某个文件的可下载用户
export function delPeopleApi(id) {
  return request({
    url: '/downloadFileRelation/' + id,
    method: 'delete'
  })
}

// 新增某个文件可下载用户
export function addPeopleApi(data) {
  return request({
    url: '/downloadFileRelation',
    method: 'post',
    data: data
  })
}

// 获取人员信息(手机号与用户类别)
export function getPeopleDataApi() {
  return request({
    url: '/getInfo',
    method: 'get',
  })
}

// 获取短信验证码
export function getCodeNumberApi(data) {
  return request({
    url: '/common/downloadMsgCode',
    method: 'get',
    params: data
  })
}

// 用户下载文件(无加密狗)
export function addNoPassPeopleApi(data) {
  let url = process.env.VUE_APP_BASE_API + "/common/downloadFile?fileId=" + data.fileId + '&phoneNum=' + data.phoneNum + '&msgCode=' + data.msgCode;
  return url;
}

// 用户下载文件(有加密狗)
export function addNoPassPeopleApiTwo(data) {
  let url = process.env.VUE_APP_BASE_API + "/common/downloadFile?fileId=" + data.fileId + '&downloadKey=' + data.downloadKey;
  return url;
}

// 用户下载文件(有加密狗) 新狗
export function addNoPassPeopleApiNew(data) {
  let url = process.env.VUE_APP_BASE_API + "/common/downloadFile?fileId=" + data.fileId + '&newKey=' + data.newKey;
  return url;
}

// 新-下载文件前获取加密串
export function keyFiles(data) {
  return request({
    url: "/common/downloadFileNew",
    method: 'get',
    params: data
  })
}

// 文件下载加密狗口令
export function getRawKeyApi() {
  return request({
    url: '/common/downloadKey',
    method: 'get',
  })
}

//
export function addDept(data) {
  return request({
    url: '/dept/addDept',
    method: 'post',
    data: data
  })
}

// 获取树
export function getdeptTreeApi() {
  return request({
    url: '/system/dept/deptTree',
    method: 'get',
  })
}
