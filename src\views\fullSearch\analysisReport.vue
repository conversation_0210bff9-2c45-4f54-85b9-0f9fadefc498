<template>
  <div class="analysisingReport">
    <div class="head">
      <el-input :class="isEdit?'head-title editReport':'head-title'" :disabled="!isEdit" type="textarea"
                v-model="analysisData.title" :autosize="true" :resize="'none'"></el-input>
      <!-- <div class="star-insert">第（ {{detailList.issue}} ）期</div>
      <div>
          {{detailList.head}}
          <span style="display:inline-block;width:10px;"></span>
          {{ detailList.createTime }}
      </div> -->
      <!-- <div class="line"></div> -->
      <div style="text-align: right;">
        <el-button type="text" @click="isEdit=!isEdit">{{ isEdit?'预览':'编辑' }}</el-button>
        <el-button type="text" @click="downReport" :loading="downLoading">导出word</el-button>
      </div>
    </div>
    <div class="content">
      <div class="div-box">
        <h1>事件简介</h1>
        <el-input :class="isEdit?'text editReport':'text'" :disabled="!isEdit" type="textarea"
                  v-model="analysisData.statisticsText" :autosize="true" :resize="'none'"></el-input>
      </div>
      <div class="div-box">
        <h1>事件摘要</h1>
        <el-input :class="isEdit?'text editReport':'text'" :disabled="!isEdit" type="textarea"
                  v-model="analysisData.analyseSummary" :autosize="true" :resize="'none'"></el-input>
        <div class="text">
          <div class="mediaContent">
            参与此事件的重要媒体：<a v-for="(item,index) in analysisData.mediaContentList"
                                    :key="index">{{item.mediaName}} {{ item.mediaNum }}&nbsp;&nbsp;</a>
          </div>
          <div class="siteContent">
            站点报道量排行：<a v-for="(item,index) in analysisData.allMediaData" :key="index">{{item.name}} {{
            item.value }}&nbsp;&nbsp;</a>
          </div>
        </div>
      </div>

      <div class="div-box">
        <h1>相关热文</h1>
        <div class="text" style="padding: 20px 5%;">
          <el-table :data="analysisData.tableData" border style="width: 100%">
            <el-table-column prop="typeName" label="类型" align="center"></el-table-column>
            <el-table-column prop="host" label="来源" align="center"></el-table-column>
            <el-table-column prop="author" label="作者" align="center"></el-table-column>
            <el-table-column prop="publishTime" label="时间" align="center"></el-table-column>
            <el-table-column prop="title" label="标题" align="center">
              <template slot-scope="scope">
                <div style="">{{
                  replaceHtml(scope.row.title||'') }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div class="div-box" v-show="false">
        <h1>事件脉络</h1>
        <div class="text">
          <div v-show="analysisData.activities.length>0" class="eventContextList">
            <div v-for="(activity, index) in analysisData.activities" :key="index"
                 class="eventContextListItem">
              <div class="eventContext_context">
                <div class="context_title" @click="goOrigin(activity)">
                  {{index+1}}. {{activity.title||''}}
                </div>
                <div class="context_info">
                  <span>{{ activity.publishTime }}</span>
                  <span>媒体：{{activity.host}}</span>
                  <span>
                                        相似文章数量：<span style="cursor: pointer;">{{activity.similarCount||0}}</span>
                                    </span>
                </div>
              </div>
            </div>
          </div>
          <div v-if="analysisData.activities.length==0" class="noneData">
            <img src="@/assets/images/none.png" alt="">
            <div>暂无数据</div>
          </div>
        </div>
      </div>
      <div class="div-box" v-show="analysisData.hotKw">
        <h1>相关热搜</h1>
        <div class="text" style="padding: 20px 5%;">
          <el-table class="hotSearchTable" :data="analysisData.hotSearchTableData" border style="width: 100%">
            <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <a :href="scope.row.url" style="color: #000;" target="_blank">{{scope.row.title}}</a>
              </template>
            </el-table-column>
            <el-table-column prop="sort" label="当前排名" align="center"></el-table-column>
            <el-table-column prop="indexNumStr" label="热度" align="center"></el-table-column>
            <el-table-column prop="type" label="平台" align="center"></el-table-column>
          </el-table>
        </div>
      </div>

      <div class="div-box">
        <h1>信息来源走势图</h1>
        <div class="text">
          <lineChart v-if="visible" style="width:100%;height:400px" :data="analysisData.infoLineData"
                     :legendData="analysisData.legendData" chartText="" :isShow="true" :toolName="'信息来源走势'"
                     :showLoading="false" @chartRef="chartToImg" :isToImg="'line1'" :isDown="true"
                     :isDataView="isChartEdit"/>
        </div>
      </div>
      <div class="div-box">
        <h1>媒体级别分布图</h1>
        <el-input :class="isEdit?'text editReport':'text'" :disabled="!isEdit" type="textarea"
                  v-model="analysisData.mediaLevelText" :autosize="true" :resize="'none'"></el-input>
        <div class="text">
          <barChartMedia v-if="visible" v-show="analysisData.rankNull" style="width:100%;height:400px"
                         :data="analysisData.mediaRankData" :showLoading="false" @chartRef="chartToImg"
                         :isToImg="'barMedia'" :isDown="true" :isDataView="isChartEdit"/>
          <div v-if="analysisData.rankNull ==0" class="noneData">
            <img src="@/assets/images/none.png" alt="">
            <div>暂无数据</div>
          </div>
        </div>
        <el-input :class="isEdit?'text editReport':'text'" :disabled="!isEdit" type="textarea"
                  v-model="analysisData.mediaCountText" :autosize="true" :resize="'none'"></el-input>
        <div class="text" style="padding: 20px 5%;">
          <el-table :data="analysisData.centralData" style="width: 100%" border>
            <el-table-column type="index" label="排名" width="80" align="center">
              <template slot-scope="scope">
                <div>{{ scope.$index + 1 }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="mediaName" label="媒体网站" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="mediClass">{{scope.row.mediaName||'-'}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="mediaNum" label="信息量" width="160" align="center">
              <template slot-scope="scope">
                <div class="mediClass">{{scope.row.mediaNum||'-'}}</div>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="siteName" label="媒体账号" width="100" align="center">
              <template slot-scope="scope">
                <div class="mediClass">{{scope.row.siteName||'-'}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="siteNum" label="信息量" width="100" align="center">
              <template slot-scope="scope">
                <div class="mediClass">{{scope.row.siteNum||'-'}}</div>
              </template>
            </el-table-column> -->
          </el-table>
        </div>
      </div>

      <div class="div-box">
        <h1>敏感走势图</h1>
        <div class="text">
          <lineChart v-if="visible" style="width:100%;height:400px" :data="analysisData.sensitiveLineData"
                     chartText="" :isShow="false" :toolName="'敏感走势'" :showLoading="false" @chartRef="chartToImg"
                     :isToImg="'line2'" :isDown="true" :isDataView="isChartEdit"/>
        </div>
      </div>
      <div class="div-box">
        <h1>敏感占比图</h1>
        <div class="text">
          <pieChart v-if="visible" style="width:100%;height:400px" :data="analysisData.emotionData"
                    :toolName="'敏感占比'" :showLoading="false" @chartRef="chartToImg" :isToImg="'pie1'" :isDown="true"
                    :isDataView="isChartEdit"/>
        </div>
      </div>
      <div class="div-box">
        <h1>敏感信息 TOP10</h1>
        <div class="text" style="padding: 20px 5%;">
          <el-table :data="analysisData.emotionTopData" style="width: 100%" border>
            <el-table-column type="index" label="排名" width="80" align="center">
              <template slot-scope="scope">
                <div>{{ scope.$index + 1 }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="mediClass">{{ scope.row.title}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="publishTime" label="发布时间" width="160" align="center">
            </el-table-column>
            <el-table-column prop="similarCount" label="相似文章" width="100" align="center">
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="div-box">
        <h1>信息来源占比</h1>
        <div class="text">
          <pieChart v-if="visible" style="width:100%;height:400px" :show-loading="false" :toolName="'信息来源占比'"
                    :data="analysisData.sourceData" :radius="['30%', '50%']" :color="colorList"
                    @chartRef="chartToImg" :isToImg="'pie2'" :isDown="true" :isDataView="isChartEdit"/>
        </div>
      </div>
      <div class="div-box">
        <h1>来源占比信息</h1>
        <div class="text" style="padding: 20px 5%;">
          <el-table :data="analysisData.sourceData.data" style="width: 100%" border>
            <el-table-column prop="name" label="来源" align="center"></el-table-column>
            <el-table-column prop="value" label="信息量" align="center"></el-table-column>
            <el-table-column prop="percent" label="占比" align="center"></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="div-box">
        <h1>关键词云</h1>
        <div class="text">
          <cloudChart v-if="visible" v-show="analysisData.cloudData.length" style="width:100%;height:400px"
                      :showLoading="false" ref="cloud" :data="analysisData.cloudData" @chartRef="chartToImg"
                      :isToImg="'cloud'" :isDown="true" :isDataView="isChartEdit">
          </cloudChart>
          <div v-if="analysisData.cloudData.length == 0" class="noneData">
            <img src="@/assets/images/none.png" alt="">
            <div>暂无数据</div>
          </div>
        </div>
      </div>
      <div class="div-box">
        <h1>热门词频</h1>
        <div class="text">
          <el-table :data="analysisData.sensitiveTableData" style="width: 100%" border>
            <el-table-column type="index" label="排名" width="80" align="center">
              <template slot-scope="scope">
                <div>{{ scope.$index + 1 }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="热词" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="value" label="提及量" align="center"></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="div-box">
        <h1>媒体活跃度</h1>
        <div class="text">
          <barChart v-if="visible" v-show="analysisData.mediaNull" style="width:100%;height:400px"
                    :data="analysisData.mediaData" chartText="" :showLoading="false" @chartRef="chartToImg"
                    :isToImg="'bar'" :isDown="true" :isDataView="isChartEdit"/>
          <div v-if="analysisData.mediaNull == 0" class="noneData">
            <img src="@/assets/images/none.png" alt="">
            <div>暂无数据</div>
          </div>
        </div>
      </div>
      <div class="div-box">
        <h1>地域分布图</h1>
        <div class="text">
          <ChinaMapChart ref="ChinaMapChart" v-if="visible" v-show="analysisData.ddNull"
                         @getAreaData="getAreaData" :data="analysisData.areaMapData" :params="analysisData.queryParams"
                         :involves="analysisData.involves" :areaInfo="analysisData.newParams"
                         style="width:100%;height:600px" @chartRef="chartToImg" :isToImg="'map'" :isDown="true"
                         :isDataView="isChartEdit"/>
          <div v-if="analysisData.ddNull == 0" class="noneData">
            <img src="@/assets/images/none.png" alt="">
            <div>暂无数据</div>
          </div>
        </div>
      </div>
      <div class="div-box">
        <h1>地域分布TOP10</h1>
        <div class="text">
          <barChartArea v-if="visible" v-show="analysisData.mapNull" style="width:100%;height:400px"
                        :data="analysisData.areaBarData" :showLoading="false" @chartRef="chartToImg"
                        :isToImg="'barArea'" :isDown="true" :isDataView="isChartEdit"/>
          <div v-if="analysisData.mapNull ==0" class="noneData">
            <img src="@/assets/images/none.png" alt="">
            <div>暂无数据</div>
          </div>
        </div>
      </div>
      
      <div class="div-box">
        <h1>传播路径分析</h1>
        <div class="text">
          <treeChart v-if="visible" style="width:100%;height:400px" :chartData="analysisData.propagePathData"
                     @chartRef="chartToImg" :isToImg="'tree'" :isDown="true" :isDataView="false"></treeChart>
        </div>
      </div>

      <div class="div-box">
        <h1>媒体观点</h1>
        <div class="text">
          <barView v-if="visible" style="width:100%;height:400px" :showLoading="false"
                   :data="analysisData.mediaOpinion" @chartRef="chartToImg" :isToImg="'bar2'" :isDown="true"
                   :isDataView="isChartEdit"></barView>
        </div>
        <el-input :class="isEdit?'text editReport':'text'" :disabled="!isEdit" type="textarea"
                  v-model="analysisData.mediaOpinionText" :autosize="true" :resize="'none'"></el-input>
      </div>
      <div class="div-box">
        <h1>网民观点</h1>
        <div class="text">
          <el-table :data="analysisData.netizenOpinion" style="width: 100%" border>
            <el-table-column type="index" label="排名" width="80" align="center">
              <template slot-scope="scope">
                <div>{{ scope.$index + 1 }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="author" label="作者" align="center"></el-table-column>
            <el-table-column prop="title" label="标题" align="center"></el-table-column>
            <el-table-column width="180" prop="publishTime" label="发布时间" align="center"></el-table-column>
          </el-table>
        </div>
      </div>

    </div>

  </div>
</template>
<script>
import moment from 'moment'
import html2canvas from 'html2canvas';
import {transImage} from '@/utils/index';
import barView from './components/barView.vue'
import lineChart from './components/lineChart.vue'
import pieChart from './components/pieChart.vue'
import cloudChart from './components/cloudChart.vue'
import barChart from './components/barChart.vue'
import barChartMedia from './components/barChartMedia.vue'
import barChartArea from './components/barChartArea.vue'
import hotTopic from './components/hotTopic.vue'
import ChinaMapChart from "@/views/dataScreen/components/ChinaMapChart.vue";
import treeChart from './components/treeChart.vue'
import {replaceHtml} from '@/utils/index';
import {downPostBlobFile} from '@/utils/index'

export default {
  components: {
    barView,
    lineChart,
    pieChart,
    cloudChart,
    barChart,
    barChartArea,
    barChartMedia,
    ChinaMapChart,
    hotTopic,
    treeChart
  },
  data() {
    return {
      replaceHtml,
      colorList: ['#518DEB', '#FF7800', '#E770B0', '#08C47A', '#664AE7', '#EC6764', '#98C20A'],
      isEdit: false,//编辑模式
      isChartEdit: true,//echarts图表是否能编辑（动态切换不好做，需留存修改后的数据，不能直接重置）
      analysisData: this.analysisProps,
      downLoading: false,
    }
  },
  props: {
    analysisProps: {
      type: Object,
      default: () => ({}),
    },
    visible: {
      type: Boolean,
      default: false
    },
  },
  watch: {
    analysisProps: {
      deep: true,
      handler(newVal, oldVal) {
        console.log('analysisProps :>> ', newVal);
        this.analysisData = JSON.parse(JSON.stringify(this.analysisProps))
      }
    },
    analysisData: {
      deep: true,
      handler(newVal, oldVal) {
        console.log('analysisDataWatch :>> ', newVal);
      }
    },
    visible: {
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal) {
          this.analysisData = JSON.parse(JSON.stringify(this.analysisProps))//重置数据
          this.analysisData.analyseSummary = this.analysisProps.analyseSummary.replace(/<br\/>/g, '\n')
          this.$nextTick(() => {
            this.$refs['ChinaMapChart'].initMap(this.analysisData.newParams)//重绘地图
          })
        }
      }
    },
  },
  async mounted() {
    console.log('analysisData mounted:>> ', this.analysisData);
  },

  methods: {
    getImage() {
      setTimeout(() => {
        html2canvas(document.querySelector("#myDom1")).then(canvas => {
          var dataUrl = canvas.toDataURL("image/png", 2)
          console.log('dataUrl :>> ', dataUrl);
        });
      }, 20000);
    },


    chartToImg(val, base) {
      // 确保 imgEchart 已被初始化
      if (!this.imgEchart) {
        this.imgEchart = {};
      }
      const imgMappings = {
        'line1': 'lineImg1',
        'barMedia': 'barMediaImg',
        'line2': 'lineImg2',
        'pie1': 'pieImg1',
        'pie2': 'pieImg2',
        'cloud': 'cloudImg',
        'bar': 'barImg',
        'map': 'mapImg',
        'barArea': 'barAreaImg',
        'bar2': 'barImg2',
        'tree': 'spreadPathImg',
      };
      //   const
      const propName = imgMappings[val]
      this.imgEchart[propName] = base;
      // console.log('allChartimg :>> ', propName);
      // this.$emit('allChartimg', this.imgEchart)
    },


    //地域分布图
    async getAreaData(value, long, mapData) {
      if (value) {
        this.analysisData.areaBarData = value
        this.analysisData.mapData = mapData
      }
      this.analysisData.mapNull = long
    },
    // 查看原文
    goOrigin(item) {
      window.open(item.url, '_blank')
    },

    downReport() {
      if (!this.imgEchart.mapImg) {
        return this.$message.warning('请等待地图图表生成')
      }
      this.downLoading = true
      let data = JSON.parse(JSON.stringify(this.analysisData))

      const resParams = {
        data: {
          title: data.title,//报告标题
          desc: data.statisticsText,//第一段文字描述
          summary: data.analyseSummary,//事件摘要文字
          rankDatas: data.mediaContentList,
          mediaActive: data.allMediaData,
          firstRelease: data.tableData,//firstRelease首发接口返回的列表
          eventContext: data.activities,//eventContext事件脉络接口返回的列表
          hotWords: data.hotSearchTableData,//hotWords相关热搜接口返回的列表
          lineImg1: "信息来源走势图",
          mediaTotalNum: data.mediaLevelText,//媒体级别分布图下方的描述文字  内容示例：媒体总数量：184家 媒体总发文数量：1535条
          barMediaImg: "媒体级别分布图",
          leveCount: data.mediaCountText,//媒体级别分布图右边表格上方描述文字  内容示例：媒体数量：20家
          mediaCentral: data.centralData,//mediaCentral接口返回数据列表
          lineImg2: "敏感走势图",
          pieImg1: "敏感占比图",
          emotionTop: data.emotionTopData,//emotionTop接口返回数据列表
          pieImg2: "信息来源占比",
          mediaTypes: data.sourceData.data,//mediaTypes来源占比信息接口返回数据列表
          cloudImg: "关键词云",
          wordArray: data.sensitiveTableData,//wordArray热门词频接口返回数据列表
          barImg: "媒体活跃度",
          mapImg: "",
          barAreaImg: "地域分布TOP10",
          barImg2: "媒体观点",
          mediaOpinion: data.mediaOpinionText, //mediaOpinion媒体观点接口拼接出的文本信息
          netizenOpinion: data.netizenOpinion,//netizenOpinion网民观点接口返回的数据列表

          ...this.imgEchart,
        }
      }

      console.log('resParams :>> ', resParams, data);
      // this.downLoading = false

      downPostBlobFile('/analyse/updateReport', resParams, '统计分析报告.docx', () => {
        this.$message.success('下载成功')
        this.downLoading = false
      })
    },
  }
}
</script>
<style scoped lang="scss">
.analysisingReport {
  overflow-y: auto;

  .head {
    text-align: center;

    .head-title {
      font-weight: bold;
      font-size: 24px;
      padding: 10px 5%;
      // color: #FF0D0D;
      ::v-deep .el-textarea__inner {
        text-align: center;
        border: none;
        background-color: transparent;
        color: #333;
        padding: 0;
        font-family: sourcehan;
        cursor: auto;
      }

    }

    .editReport {
      background-color: #F2F3F7;
    }

    .star-insert {
      margin: 10px;
      font-size: 14px;
      line-height: 20px;
    }

    .line {
      height: 1px;
      border: 2px solid #FF0D0D;
      margin-top: 26px;
      margin-bottom: 35px;
    }
  }

  .content {
    .div-box {
      .text {
        font-size: 16px;
        line-height: 24px;
        padding: 20px 5%;

        ::v-deep .el-textarea__inner {
          border: none;
          background-color: transparent;
          color: #333;
          padding: 0;
          font-family: sourcehan;
          cursor: auto;
        }
      }

      .editReport {
        background-color: #F2F3F7;
      }

      .image {
        // width: 100%;
        // // height: 100%;
        // height: 200px;
      }

      .noneData {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
          width: 120px;
          margin-bottom: 10px;
        }
      }
    }
  }


  .eventContextList {
    width: 100%;

    .eventContextListItem {
      display: flex;
      justify-content: flex-start;
      padding: 10px 0;

      .eventContext_context {
        .context_title {
          display: flex;
          align-items: center;
          color: blue;
          cursor: pointer;
        }

        .context_info {
          margin-top: 10px;
          color: #000;

          > span {
            margin-right: 20px;
            white-space: nowrap;
            color: #000;
          }
        }
      }
    }
  }
}


::v-deep .el-dialog__footer {
  text-align: center;
}

::v-deep .el-dialog__header {
  background: #247CFF;
  padding-bottom: 20px;

  .el-dialog__title {
    color: #fff;
  }

  .el-dialog__close {
    color: #fff;
  }
}

.viewClass {
  ::v-deep .el-dialog__header {
    background: #fff;
    padding-bottom: 20px;

    // .el-dialog__title {
    //     color: #fff;
    //   }
    .el-dialog__close {
      color: #333;
    }
  }

  ::v-deep .el-dialog {
    width: 60%;
    height: 90%;
    overflow: hidden;

    .el-dialog__body {
      height: 90%;
      overflow-y: auto;
    }

    .el-dialog__footer {
      padding: 10px 0px;
      position: absolute;
      bottom: 0px;
      display: flex;
      justify-content: center;
      width: 100%;
      background: rgba(74, 77, 81, .8);
    }
  }

}

</style>
