<template>
    <div class="addSimulate">
        <div class="pageTitle">
            <div class="pageTitle_colorBlock" />
            <div class="pageTitle_title">{{ pageTitle }}</div>
        </div>
        <div class="dashedLine"></div>
        <div class="main-content">
            <el-form ref="form" :model="taskForm" label-width="160px" :rules="rules">

                <el-form-item label="任务标题：" prop="taskTitle">
                    <el-input v-model.trim="taskForm.taskTitle" maxlength="100" show-word-limit placeholder="请输入标题"
                        style="width: 100%" />
                </el-form-item>
                <el-form-item label="任务内容：" prop="taskContent">
                    <el-input type="textarea" v-model.trim="taskForm.taskContent" maxlength="1000" show-word-limit
                        :autosize="{ minRows: 4, maxRows: 6}" placeholder="请输入任务内容，不超过1000个字符" style="width: 100%;" />
                </el-form-item>
                <el-form-item label="演练事件：" prop="drillEvent">
                    <el-input v-model.trim="taskForm.drillEvent" maxlength="100" show-word-limit placeholder="请输入事件"
                        style="width: 100%" />
                </el-form-item>
                <el-form-item label="演练时间：" prop="estimateDrillTime">
                    <el-date-picker v-model="taskForm.estimateDrillTime" type="date" :picker-options="pickOptions"
                        value-format="yyyy-MM-dd" placeholder="请选择演练时间" style="width: 100%">
                    </el-date-picker>
                </el-form-item>
                <div class="group-top">
                    <img src="@/assets/images/simulatedVS/blueRectangle.png" alt="">
                    <div>蓝方</div>
                </div>
                <!-- 蓝方队长 -->
                <el-form-item label="队长：" prop="blueCaptain">
                    <el-select v-model="taskForm.blueCaptain" placeholder="请选择队长" style="width: 100%">
                        <el-option v-for="item in blueCaptainOptions" :key="item.value" :label="item.label"
                            :value="item.value" :disabled="item.disabled">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- 蓝方队员 -->
                <el-form-item label="队员：" prop="blueMember">
                    <el-select v-model="taskForm.blueMember" multiple placeholder="请选择队员" style="width: 100%">
                        <el-option v-for="item in blueMemberOptions" :key="item.value" :label="item.label"
                            :value="item.value" :disabled="item.disabled">
                        </el-option>
                    </el-select>
                </el-form-item>


                <div class="group-top">
                    <img src="@/assets/images/simulatedVS/redRectangle.png" alt="">
                    <div>红方</div>
                </div>
                <!-- 红方队长 -->
                <el-form-item label="队长：" prop="redCaptain">
                    <el-select v-model="taskForm.redCaptain" placeholder="请选择队长" style="width: 100%">
                        <el-option v-for="item in redCaptainOptions" :key="item.value" :label="item.label"
                            :value="item.value" :disabled="item.disabled">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- 红方队员 -->
                <el-form-item label="队员：" prop="redMember">
                    <el-select v-model="taskForm.redMember" multiple placeholder="请选择队员" style="width: 100%">
                        <el-option v-for="item in redMemberOptions" :key="item.value" :label="item.label"
                            :value="item.value" :disabled="item.disabled">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div class="btn-group">

                <el-button v-if="pageTitle=='新增任务演练'||taskForm.status==1" type="primary" @click="onSubmit">确定</el-button>
                <el-button @click="onCancel">取消</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import { drillTaskSaveApi, drillTaskUserApi, drillTaskQueryOneApi } from "@/api/simulatedVS/index.js";
export default {
    data() {
        return {
            taskForm: {
                taskTitle: '',
                taskContent: '',
                drillEvent: '',
                estimateDrillTime: '',
                blueCaptain: '',
                blueMember: [],
                redCaptain: '',
                redMember: [],
            },
            rules: {
                taskTitle: [{ required: true, message: '请输入任务标题', trigger: 'blur' }],
                taskContent: [
                    { required: true, message: '请输入任务内容', trigger: 'blur' },
                    { max: 1000, message: "任务内容请控制在1000个字以内", trigger: "change" }
                ],
                drillEvent: [{ required: true, message: '请输入演练事件', trigger: 'blur' }],
                estimateDrillTime: [{ required: true, message: '请输入演练时间', trigger: 'blur' }],
                blueCaptain: [{ required: true, message: '请选择蓝方队长', trigger: 'blur' }],
                blueMember: [{ required: true, message: '请选择蓝方队员', trigger: 'blur' }],
                redCaptain: [{ required: true, message: '请选择红方队长', trigger: 'blur' }],
                redMember: [{ required: true, message: '请选择红方队员', trigger: 'blur' }],

            },
            pickOptions: {
                disabledDate: (time) => {
                    return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
                }
            },

            peopleList: [],
            pageTitle: '新增任务演练'
        }
    },
    computed: {
        // 蓝队长选项（排除蓝队员、红队长、红队员）
        blueCaptainOptions() {
            return this.peopleList.map(user => ({
                ...user,
                disabled: [
                    ...this.taskForm.blueMember,
                    this.taskForm.redCaptain,
                    ...this.taskForm.redMember
                ].includes(user.value)
            }))
        },
        // 蓝队员选项（排除蓝队长、红队长、红队员）
        blueMemberOptions() {
            return this.peopleList.map(user => ({
                ...user,
                disabled: [
                    this.taskForm.blueCaptain,
                    this.taskForm.redCaptain,
                    ...this.taskForm.redMember
                ].includes(user.value)
            }))
        },
        // // 蓝队员选项,下拉中不显示已被选择的用户
        // blueMemberOptions() {
        //     const excludedIds = [
        //         this.taskForm.blueCaptain,
        //         this.taskForm.redCaptain,
        //         ...this.taskForm.redMember
        //     ].filter(id => id !== '' && id != null)
        //     return this.peopleList.filter(user => !excludedIds.includes(user.value))
        // },
        // 红队长选项（排除红队员、蓝队长、蓝队员）
        redCaptainOptions() {
            return this.peopleList.map(user => ({
                ...user,
                disabled: [
                    ...this.taskForm.redMember,
                    this.taskForm.blueCaptain,
                    ...this.taskForm.blueMember
                ].includes(user.value)
            }))
        },
        // 红队员选项（排除红队长、蓝队长、蓝队员）
        redMemberOptions() {
            return this.peopleList.map(user => ({
                ...user,
                disabled: [
                    this.taskForm.redCaptain,
                    this.taskForm.blueCaptain,
                    ...this.taskForm.blueMember
                ].includes(user.value)
            }))
        }
    },
    mounted() {
        this.getDist()
        if (this.$route.query?.drillTaskId) {
            this.pageTitle = '编辑任务演练'
            this.resetForm()
            let query = {
                drillTaskId: this.$route.query.drillTaskId
            }
            drillTaskQueryOneApi(query).then(res => {
                if (res.code == '200') {
                    this.taskForm = res.data
                }
            })
        } else {
            this.pageTitle = '新增任务演练'
            this.resetForm()
        }
    },
    methods: {
        getDist() {
            drillTaskUserApi().then(res => {
                if (res.code == '200') {
                    this.peopleList = res.data.map(item => {
                        return {
                            value: item.userId,
                            label: item.nickName
                        }
                    })
                }
            })
        },
        validateTeams() {
            const { blueCaptain, blueMember = [], redCaptain, redMember = [] } = this.taskForm
            const errors = []

            if (blueCaptain && blueCaptain === redCaptain) {
                errors.push('红蓝队长不能为同一人')
            }
            if (blueMember.includes(blueCaptain)) {
                errors.push('蓝队队长不能兼任队员')
            }
            if (redMember.includes(redCaptain)) {
                errors.push('红队队长不能兼任队员')
            }
            if (blueMember.some(item => redMember.includes(item))) {
                errors.push('存在重复的双方队员')
            }
            if (blueCaptain && redMember.includes(blueCaptain)) {
                errors.push('蓝队队长不能担任红方队员')
            }
            if (redCaptain && blueMember.includes(redCaptain)) {
                errors.push('红队队长不能担任蓝方队员')
            }

            return errors
        },
        onSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {

                    const errors = this.validateTeams()
                    if (errors.length > 0) {
                        this.$message.error(errors.join('，'))
                        return
                    }

                    const params = { ...this.taskForm }
                    console.log('params', params)
                    drillTaskSaveApi(params).then((res) => {
                        this.$message.success('保存成功')
                        this.onCancel()
                    }).catch((err) => {
                        this.$message.error('保存失败')
                    })
                }
            })
        },
        onCancel() {
            // this.$router.go(-1)
            this.$router.push({ path: '/simulatedVS/index' })
        },
        resetForm() {
            this.$refs.form.resetFields()
            this.taskForm = {
                taskTitle: '',
                taskContent: '',
                drillEvent: '',
                estimateDrillTime: '',
                blueCaptain: '',
                blueMember: [],
                redCaptain: '',
                redMember: [],
            }
        },
    }
}
</script>
<style scoped lang="scss">
.pageTitle {
    padding: 12px 0;
    // border-bottom: 1px dashed #DCDEE0;
    background-color: #fff;
    position: relative;

    .pageTitle_colorBlock {
        width: 6px;
        height: 16px;
        background: #247CFF;
        display: inline-block;
        vertical-align: middle;
        margin-right: 20px;
    }

    .pageTitle_title {
        font-size: 18px;
        color: #333333;
        line-height: 25px;
        display: inline-block;
        vertical-align: middle;
        font-weight: 600;
    }

}

.dashedLine {
    width: 100%;
    border-bottom: 1px dashed #DCDEE0;
}

.addSimulate {
    margin: 20px;
    background-color: #fff;

    .main-content {
        padding: 20px 10%;

        .group-top {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 18px;
            margin-left: 80px;
            margin-bottom: 10px;

            img {
                height: 12px;
                margin-right: 5px;
            }
        }

        .btn-group {
            width: 100%;
            text-align: center;
        }
    }
}
</style>