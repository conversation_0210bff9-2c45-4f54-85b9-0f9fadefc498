<template>
  <div class="wechatAlert">
    <div v-if="code == 200">
      <img src="../assets/images/surelogin.png" alt=""/>
      <p>{{ showMsg }}</p>
      <el-button class="knowBtn" @click="knowFun">我知道了</el-button>
    </div>
    <div v-else-if="code == ''">
      <p>绑定中,请稍后</p>
    </div>
    <div v-else>
      <img src="../assets/images/errorlogin.png" alt=""/>
      <p>{{ showMsg }}</p>
      <el-button class="knowBtn" @click="knowFun">我知道了</el-button>
    </div>
  </div>
</template>

<script>
import {bindWarningApi} from "@/api/wechat";

export default {
  data() {
    return {
      showMsg: "",
      code: "",
    };
  },
  created() {
    this.bindWarning();
  },
  methods: {
    // 预警绑定
    async bindWarning() {
      let params = {
        code: this.$route.query.code,
        state: this.$route.query.state,
      };
      let res = await bindWarningApi(params);
      this.code = res.code;
      this.showMsg = res.msg;
    },
    // 点击我知道了
    knowFun() {
      try {
        WeixinJSBridge.call("closeWindow");
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.wechatAlert {
  width: 100%;
  overflow: hidden;
  text-align: center;

  div {
    margin-top: 100px;

    .knowBtn {
      margin-top: 300px;
      width: 200px;
      height: 60px;
      line-height: 6px;
      background: #f2f2f2;
      color: #67ab72;
    }
  }
}
</style>
