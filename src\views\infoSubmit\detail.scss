.detail-mainter {
  // height: calc(100vh - 84px);
  background-color: #f7f7f7;
  min-height: calc(100vh - 84px);
  padding: 16px 0;
  box-sizing: border-box;
  word-wrap: break-word;
  word-break: break-all;

  .detail-wrap {
    min-height: calc(100vh - 124px);
    // height: 100%;
    display: flex;
    padding: 25px 20px 0 20px;
    margin: 0 20px;
    overflow-y: scroll;
    background: #fff;
    box-sizing: border-box;
  }
}

.detail-line {
  margin: 73px 0 20px 0;
  width: 1px;
  border: 1px dashed #999;
}

.detail-title {
  display: flex;
  align-items: center;
  padding-bottom: 23px;
  border-bottom: 1px solid #eee;
  font-size: 20px;
  color: #333;
  font-weight: 600;

  img {
    width: 25px;
    margin-right: 8px;
  }

  span {
    margin-right: 30px;
  }

  p {
    margin: 0;
    padding: 3px 4px 2px 4px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
    color: #fff;

    &.wait-status {
      background-color: #EC0D1E;
    }

    &.deal-status {
      background-color: #2E54EC;
    }

    &.finish-status {
      background-color: #3BC12F;
    }

    &.trans-status {
      background-color: #ECB00D;
    }

    &.expire-status {
      background-color: #999;
    }
  }
}

.detail-left {
  // margin-top: 16px;
  width: 70%;
  margin-right: 20px;
}

.detail-right {
  flex: 1;
  margin-left: 20px;

  .detail-title {
    img {
      width: 20px;
    }
  }
}

.detail-main {
  .head {
    text-align: center;
    margin-top: 18px;
    font-size: 28px;
    font-weight: 600;
    color: #3657D8;

    span {
      cursor: pointer;
    }
  }

  .head-sub {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #999;

    .sub-time {
      margin-right: 23px;
      margin-left: 20px;
    }
  }

  .sub-curp {
    cursor: pointer;
  }

  .sub-main {
    display: flex;
    align-items: center;
    margin-right: 10px;

    img {
      width: 10px;
      margin-right: 3px;
    }
  }

  .detail-msg {
    display: flex;
    align-items: flex-start;
    width: 100%;
    margin-bottom: 30px;
    font-size: 14px;
    color: #333;

    .msg-title {
      width: 70px;
      text-align: right;
      font-weight: 500;
    }

    .msg-content {
      flex: 1;
      word-wrap: break-word;
      word-break: break-all;
    }

    .msg-pic {
      display: flex;
      flex-wrap: wrap;

      img {
        width: 160px;
        height: 120px;
        margin: 0 13px 13px 0;
      }
    }
  }
}

.msg-word {
  .imgicon {
    display: none;
  }
}

.word-list {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  img {
    margin-right: 10px;
  }

  .imgicon-pdf {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/pdf.png") !important;
  }

  .imgicon-docx {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/docx.png") !important;
  }

  .imgicon-zip {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/zip.png") !important;
  }

  .imgicon-pptx {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/ppt.png") !important;
  }

  .imgicon-xlsx {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/xls.png") !important;
  }

  .imgicon-default {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/text.png") !important;
  }
}

.word-lists {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  img {
    margin-right: 10px;
  }

  .imgicon-pdf {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/pdf.png") !important;
  }

  .imgicon-docx {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/docx.png") !important;
  }

  .imgicon-zip {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/zip.png") !important;
  }

  .imgicon-pptx {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/ppt.png") !important;
  }

  .imgicon-xlsx {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/xls.png") !important;
  }

  .imgicon-default {
    display: inline-block;
    width: 20px;
    margin-bottom: -3px;
    height: 20px;
    background-size: 100% 100%;
    margin-right: 10px;
    background-image: url("../../components/UploadFiles/text.png") !important;
  }
}

.detail-right {
  .detail-flow {
    margin-top: 30px;
    max-height: calc(100vh - 230px);
  }

  .flow-timeline {
    padding-top: 10px;
    max-height: calc(100vh - 280px);
    overflow-y: scroll;

  }

  .el-timeline-item__timestamp {
    color: #333;
  }

  .flow-detail {
    padding: 0;
    font-size: 14px;
    word-wrap: break-word;
    word-break: break-all;

    h4 {
      margin: 0;
      color: #333;
    }

    .flow-create {
      margin: 0;
      margin-top: 4px;
      color: #999;
    }

    .flow-comment {
      display: block;
      margin-top: 4px;
      color: #333;
    }

    .flow-result {
      display: block;
      margin-top: 4px;
      color: #2E54EC;
    }
  }
}

.file-list {
  display: flex;
  flex-wrap: wrap;
}

.file-main {
  position: relative;
}

.file-main:hover .file-img {
  display: inline-block;
  font-size: 20px;
}

.file-main:hover .file-mask {
  display: block;
  height: 120px;
}

.file-main .file-mask {
  display: none;
  position: absolute;
  height: 120px;
  top: 0;
  left: 0;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  right: 20px;
  bottom: 0;
  text-align: center;
  color: #fff;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.5);
}

.file-main .file-mask i {
  cursor: pointer;
  margin-top: 50px;
}

.file-main img {
  margin-right: 20px;
  display: inline-block;
  width: 160px;
  height: 120px;
  margin-bottom: 10px;
}

.big-img {
  /* width: 680px; */
  width: 100%;
  height: 510px;
}

.title-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;

  .detail-title {
    border: none;

  }

  .countdown {
    margin-right: 10px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
    font-weight: 500;

    img {
      margin-right: 9px;
      width: 19px;
    }
  }
}

.operate-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}
