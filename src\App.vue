<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
import {baseRoute} from '@/utils/index.js'

export default {
  name: 'App',
  data() {
    return {}
  },
  created() {
    this.getSysLogo()
  },
  mounted() {
    this.pageResize()
    window.addEventListener('resize', this.pageResize())
  },
  beforeDestroy() {
    // 在组件销毁前清除计时器和移除事件监听器（尽管这里没有添加任何事件监听器）
    window.removeEventListener("resize", this.pageResize);
  },
  methods: {
    getSysLogo() {
      const url = location.pathname
      const index = baseRoute(url)

      if (url.includes('/outReport') || url.includes('/singleLogin') || url.includes('/phoneDetail') || url.includes('/wechatAlert') || url.includes('/warnList') || url.includes('/vsSingUp')) {
        // 加缓存context
        window.localStorage.setItem('context', index)
      }
      // 缓存context和地址不一致-退出登录
      if (window.localStorage.getItem('context') != index) {
        this.$store.dispatch('LogOut').then(() => {
          location.href = `${index}/login`;
        })
      } else {
        this.$store.dispatch("getSysLogo", index).then((res) => {
          document.title = this.$store.state.user.sysName || '博约舆情监测系统';
          var favicon = document.querySelector('#dynamic-favicon');
          favicon.href = this.$store.state.user.logo||'/favicon.png';
        });
      }
      // 加缓存context
      window.localStorage.setItem('context', index)
    },
    //页面字体自适应方法
    pageResize() {
      this.$nextTick(() => {
        // 页面开始加载时修改font-size
        const html = document.getElementsByTagName('html')[0]
        const oWidth =
          document.body.clientWidth || document.documentElement.clientWidth
        // 这里的1920是指设计图的大小,自己根据实际情况改变
        html.style.fontSize = (oWidth / 1920) * 100 + 'px'
      })
    }
  }
}
</script>

<style>
/* 弹幕全局样式 */
.vue-danmaku {
  width: 100% !important;
  height: 100% !important;
  pointer-events: none !important;
  z-index: 100 !important; /* 与容器保持一致 */
}

.vue-danmaku-item {
  pointer-events: none !important;
}

.danmu-item {
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
  white-space: nowrap !important;
  line-height: 1.5 !important;
  padding: 2px 4px !important;
  border-radius: 2px !important;
  color: rgba(255,255,255,0.8) !important;
  font-size: 18px !important;
  font-family: SourceHanSansCN, PingFangSC, sans-serif !important;
  font-weight: 600 !important;
  transition: opacity 0.3s !important;
}
</style>
