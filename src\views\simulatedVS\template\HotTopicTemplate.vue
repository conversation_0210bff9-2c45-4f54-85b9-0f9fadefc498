<template>
  <div class="template2">
    <img src="@/assets/images/simulatedVS/hotTalkIcon.png" alt="">
    <div class="main-content">
      <div style="color: #333333;margin-bottom: 0.5em;">#热议话题你来定#</div>
      {{ content||'' }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'HotTopicTemplate',
  props: {
    content: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
.template2 {
  font-size: 12em;
  background-color: #FFFFFF;
  display: flex;
  padding: 1em;

  img {
    width: 5em;
    height: 5em;
    aspect-ratio: 1;
    flex-shrink: 0;
    margin-right: 1em;
  }

  .main-content {
    color: #000000;
    width: calc(100% - 6em);
    word-break: break-word;
    white-space: pre-wrap;
  }
}
</style>
