<template>
  <div>
    <!-- 处置 -->
    <el-dialog title="处置" width="550px" :visible.sync="dialogDealVisible" :before-close="cancelDialog"
               :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form :model="form" ref="dynamicValidateForm" label-width="120px" :rules="rules">
        <el-form-item label="评论：" prop="comment">
          <el-input class="comment" contenteditable=true type="textarea" placeholder="请输入200字符以内的评论"
                    v-model.trim="form.comment"></el-input>
        </el-form-item>
        <el-form-item label="附件：" prop="files">
          <FileUpload ref="files" @pageLoad="pageLoad" :fileLimit="10" :fileSize="50"
                      acceptType=".png,.jpeg,.jpg,.pdf,.doc,.docx,.xlsx,.xls" v-model="form.files"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="disabled" :loading="loading" @click="submitForm('dynamicValidateForm')">确
          定
        </el-button>
        <el-button @click="cancelDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import FileUpload from "@/components/UploadFiles/index"
import {decryptByAES, encryptByAES} from '@/utils/jsencrypt'
import {getManager, getProcessor, approvalSubmit} from '@/api/infoSubmit/index'

export default {
  name: 'dealDialog',
  components: {FileUpload},
  props: {
    dialogDealVisible: {
      type: Boolean,
      default: false,
    },
    infoId: {
      type: String,
      default: '',
    },
    processResult: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      rules: {
        comment: [
          {min: 0, max: 200, message: '长度在200个字符以内', trigger: 'blur'}
        ],
      },
      form: {
        files: [],
        comment: ''
      },
      loading: false,
      disabled: false
    }
  },
  methods: {
    pageLoad(load) {
      this.disabled = load
    },
    cancelDialog() {
      this.$emit('cancelDeal')
    },
    resetParams() {
      this.form = {
        files: [],
        comment: ''
      }
      this.$nextTick(() => {
        this.$refs['dynamicValidateForm'].resetFields()
        this.$refs.files.setFileList([])
      })
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          try {
            let params = JSON.parse(JSON.stringify(this.form))
            let fileIds = []
            params.files.map((item) => fileIds.push({fileId: item.id}))
            params.files = fileIds
            this.loading = true
            let allParams = {}
            if (this.$takeAES) {
              allParams = {
                encryptJson: encryptByAES(JSON.stringify({
                  ...params,
                  processResult: this.processResult,
                  infoId: this.infoId
                }))
              }
            } else {
              allParams = {...params, processResult: this.processResult, infoId: this.infoId}
            }
            let res = await approvalSubmit(allParams)
            this.$message.success(res.msg)
            this.cancelDialog()
            this.$emit('success')
          } finally {
            this.loading = false
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.comment {
  width: 360px;
}

.dialog-footer {
  text-align: center;
}
</style>
