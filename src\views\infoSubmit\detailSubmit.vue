<template>
  <div class="detail-mainter">
    <div class="detail-wrap" v-loading="loading">
      <div class="detail-left">
        <div class="title-wrap">
          <div class="detail-title">
            <img src="@/assets/images/message.png" alt="">
            <span>信息详情</span>
            <template v-if="roles.includes('fenxishi')">
              <p class="wait-status" v-if="infoParams.fxsState==0">待审核</p>
              <p class="wait-status" v-if="infoParams.fxsState==1">待修改</p>
              <p class="deal-status" v-if="infoParams.fxsState==2">未采纳</p>
              <p class="finish-status" v-if="infoParams.fxsState==3">已采纳</p>
            </template>
            <template v-if="roles.includes('guanli')">
              <p class="wait-status" v-if="infoParams.glState==0">待审核</p>
              <p class="wait-status" v-if="infoParams.glState==1">待处置</p>
              <p class="deal-status" v-if="infoParams.glState==2">已处置</p>
              <p class="finish-status" v-if="infoParams.glState==4">已完结</p>
              <p class="expire-status" v-if="infoParams.glState==3">已过期</p>
            </template>
            <template v-if="roles.includes('chuzhi')">
              <!-- <p class="wait-status" v-if="infoParams.czState==0">无需处理</p> -->
              <p class="wait-status" v-if="infoParams.czState==1">待处置</p>
              <p class="deal-status" v-if="infoParams.czState==2">已处置</p>
              <p class="trans-status" v-if="infoParams.czState==3">已转派</p>
              <p class="expire-status" v-if="infoParams.czState==4">已过期</p>
              <p class="finish-status" v-if="infoParams.czState==5">已完结</p>
            </template>
          </div>
          <template v-if="roles.includes('guanli')">
            <div class="countdown" v-if="infoParams.glState==1&&infoParams.deadline">
              <img src="@/assets/images/countdown.png" alt="">
              倒计时：
              <TimeDown :targetDate="infoParams.deadline" @clearTime="queryList"></TimeDown>
            </div>
          </template>
          <template v-if="roles.includes('chuzhi')">
            <div class="countdown" v-if="infoParams.czState==1&&infoParams.deadline">
              <img src="@/assets/images/countdown.png" alt="">
              倒计时：
              <TimeDown :targetDate="infoParams.deadline" @clearTime="queryList"></TimeDown>
            </div>
          </template>
        </div>
        <div class="detail-main">
          <div class="head"><span @click="goLink(infoParams.articleLink)">{{infoParams.articleTitle}}</span></div>
          <div class="head-sub">
            <div class="sub-main sub-curp" @click="copyLink(infoParams.articleLink)">
              <img src="@/assets/images/copy.png" alt="">
              <span>复制原文链接</span>
            </div>
            <div class="sub-main sub-time">
              <span>{{ infoParams.originalTime }}</span>
            </div>
            <div class="sub-main">
              <img src="@/assets/images/type.png" alt="">
              <span>{{infoParams.infoTypeName}}</span>
            </div>
            <div class="sub-main">
              <img src="@/assets/images/media.png" alt="">
              <span>{{selectDictLabel(mediaList, infoParams.mediaType)}}</span>
            </div>
            <div class="sub-main">
              <img src="@/assets/images/location.png" alt="">
              <span>{{infoParams.articleAreaName}}</span>
            </div>
            <div class="sub-main">
              <img src="@/assets/images/nature.png" alt="">
              <span>{{selectDictLabel(tendencyList, infoParams.tendency)}}</span>
            </div>
          </div>
          <div class="detail-msg">
            <div class="msg-title">信息描述：</div>
            <div class="msg-content">
              {{ infoParams.eventDesc ||'暂无'}}
            </div>
          </div>
          <div class="detail-msg">
            <div class="msg-title">处置建议：</div>
            <div class="msg-content">
              {{infoParams.suggestions ||'暂无'}}
            </div>
          </div>
          <div class="detail-msg" v-if="roles.includes('fenxishi')">
            <div class="msg-title">接收人：</div>
            <div class="msg-content">
              {{infoParams.dispenser ||'暂无'}}
            </div>
          </div>
          <div class="detail-msg" v-if="roles.includes('guanli')">
            <div class="msg-title">{{infoParams.submitType==1?'分发人：':'报送人：'}}</div>
            <div class="msg-content">
              {{infoParams.dispenser ||'暂无'}}
            </div>
          </div>
          <div class="detail-msg" v-if="roles.includes('chuzhi')">
            <div class="msg-title">分发人：</div>
            <div class="msg-content">
              {{infoParams.dispenser || '暂无'}}
            </div>
          </div>
          <div class="detail-msg">
            <div class="msg-title">附件：</div>
            <div class="msg-content" v-if="fileList.length>0">
              <div class="file-list">
                <div v-for="(item, index) in fileList" :key="index" class="file-main">
                  <div class="file-mask">
                    <i class="el-icon-zoom-in" @click="openImg(item)"></i>
                  </div>
                  <img :src="item.url" alt="" v-if="isImgType(item)"/>
                </div>
              </div>
              <div class="msg-word">
                <a class="word-list" v-for="item in fileList" :key="item.url">
                  <i></i>
                  {{item.name}}
                </a>
              </div>
            </div>
            <div v-else>暂无</div>
          </div>
        </div>
      </div>
      <div class="detail-line"></div>
      <div class="detail-right">
        <div class="detail-title">
          <img src="@/assets/images/dispose.png" alt="">
          <span>处置流程</span>
        </div>
        <div class="detail-flow">
          <el-timeline class="flow-timeline">
            <el-timeline-item color='#2E54EC' placement="bottom"
                              v-for="(activity, index) in activities"
                              :key="index"
                              :timestamp="activity.createTime">
              <div class="flow-detail">
                <h4>{{activity.actName}}</h4>
                <p class="flow-create">{{activity.createBy}}</p>
                <span class="flow-result" v-if="activity.description">{{activity.description}}</span>
                <span class="flow-comment" v-if="activity.comment">{{activity.comment}}</span>
                <div class="msg-content mt10">
                  <div class="file-list">
                    <div v-for="(item, indexf) in activity.flowList" :key="indexf" class="file-main">
                      <div class="file-mask">
                        <i class="el-icon-zoom-in" @click="openImg(item)"></i>
                      </div>
                      <img :src="item.url" alt="" v-if="isImgType(item)"/>
                    </div>
                  </div>
                  <div class="msg-word">
                    <a :class="`word-lists flow-list${index}`" v-for="(item,indexb) in activity.flowList"
                       :key="item.url">
                      <i></i>{{item.name}}
                    </a>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
          <template v-if="roles.includes('chuzhi')">
            <div v-if="infoParams.czState==1" class="operate-btn">
              <el-button type="primary" size="small" @click="dealSet"
                         v-if="canOperate(user.userId,infoParams.assigneer)">处 置
              </el-button>
              <el-button plain type="primary" size="small" @click="transferSet"
                         v-if="canOperate(user.userId,infoParams.assigneer)">转 派
              </el-button>
            </div>
          </template>
          <template v-if="roles.includes('guanli')">
            <div v-if="infoParams.glState==0" class="operate-btn">
              <el-button type="primary" size="small" v-if="canOperate(user.userId,infoParams.assigneer)"
                         @click="auditSet">审 核
              </el-button>
              <el-button plain type="primary" size="small" v-if="canOperate(user.userId,infoParams.assigneer)"
                         @click="dealSet">处 置
              </el-button>
            </div>
            <div v-if="infoParams.glState==2&&canOperate(user.userId,infoParams.assigneer)" class="operate-btn">
              <el-button type="primary" size="small" @click="finishSet">完 结</el-button>
              <el-button plain type="primary" size="small" @click="reDealSet">重新处置</el-button>
            </div>
          </template>
        </div>
      </div>
      <el-dialog title="照片" :visible.sync="dialogVisible" width="680px" append-to-body>
        <img :src="bigImg" alt="" class="big-img"/>
      </el-dialog>
      <!-- 转派 -->
      <TransferDialog ref="trans" @success="queryList" :infoId="infoId" :dialogTransVisible="dialogTransVisible"
                      @cancelTrans="dialogTransVisible=false"></TransferDialog>
      <!-- 处置 -->
      <DealDialog ref="deal" @success="queryList" :infoId="infoId" :processResult="processResult"
                  :dialogDealVisible="dialogDealVisible" @cancelDeal="dialogDealVisible=false"></DealDialog>
      <!-- 审核 -->
      <AuditDialog ref="audit" @success="queryList" :infoId="infoId" :dialogAuditVisible="dialogAuditVisible"
                   @cancelAudit="dialogAuditVisible=false"></AuditDialog>
      <!-- 完结 -->
      <FinishDialog ref="finish" @success="queryList" :infoId="infoId" :dialogFinishVisible="dialogFinishVisible"
                    @cancelFinish="dialogFinishVisible=false"></FinishDialog>
      <!-- 重新处置 -->
      <ReDealDialog ref="refDeal" @success="queryList" :deadline="deadline" :infoId="infoId"
                    :dialogReDealVisible="dialogReDealVisible" @cancelReDeal="dialogReDealVisible=false"></ReDealDialog>
    </div>
  </div>
</template>
<script>
import TimeDown from "@/components/TimeDown/index";
import {isImgType, copyLink, renderFileIcon, canOperate} from '@/utils/index'
import {getUrlByFileIds} from '@/api/fileList/fileIndex'
import {getInfoDetailsApi, getAreaTreeApi, getInfoTypeList, getReceiver, processList} from '@/api/infoSubmit/index'
import {decryptByAES, encryptByAES} from '@/utils/jsencrypt'
import TransferDialog from "./components/TransferDialog.vue";
import DealDialog from "./components/DealDialog.vue";
import AuditDialog from "./components/AuditDialog.vue";
import FinishDialog from "./components/FinishDialog.vue";
import ReDealDialog from "./components/ReDealDialog.vue";

export default {
  name: 'detailSubmit',
  components: {
    TimeDown, TransferDialog, DealDialog, AuditDialog, FinishDialog, ReDealDialog
  },
  data() {
    return {
      canOperate,
      isImgType,
      copyLink,
      renderFileIcon,
      dialogVisible: false,
      bigImg: '',
      resultName: {
        1: '无需处置',
        2: '修改材料',
        3: '直接处置',
        4: '分发',
        5: '完结'
      },
      loading: false,
      infoParams: {},
      mediaList: [],
      tendencyList: [],
      deptOptions: [],
      typeList: [],
      activities: [],
      fileIds: [],
      fileList: [],
      flowIds: [],
      flowList: [],
      dialogTransVisible: false,
      dialogDealVisible: false,
      dialogAuditVisible: false,
      infoId: '',
      processResult: '',
      dialogFinishVisible: false,
      dialogReDealVisible: false,
      queryType: {
        typeName: '',
        pageQuery: false
      },
      deadline: ''
    }
  },
  computed: {
    roles() {
      return this.$store.getters.roles
    },
    user() {
      return this.$store.getters.user
    }
  },
  methods: {
    queryList() {
      this.queryFileDetail()
      this.queryProcessList()
    },
    // 获取字典
    getDict() {
      // 媒体类型
      this.getDicts("sys_media_type").then(response => {
        this.mediaList = response.data
      })
      // 倾向性
      this.getDicts("tendency_type").then(response => {
        this.tendencyList = response.data
      })
    },
    // 跳转原文链接
    goLink(link) {
      window.open(link, '_blank')
    },
    // 放大照片
    openImg(item) {
      this.dialogVisible = true;
      if (item.raw) {
        this.bigImg = URL.createObjectURL(item.raw);
      } else {
        this.bigImg = item.url
      }
    },
    // 流程详情processList
    async queryProcessList() {
      try {
        let param = undefined
        if (this.$takeAES) {
          param = encryptByAES(this.$route.query.id)
        } else {
          param = this.$route.query.id
        }
        let res = await processList(this.$route.query.id)
        if (this.$takeAES) {
          this.activities = JSON.parse(decryptByAES(res.data))
        } else {
          this.activities = res.data
        }
        this.activities.map((item, index) => {
          if (item.files.length > 0) {
            let flowIds = []
            flowIds = item.files.map((itemb) => itemb.id)
            let param = undefined
            if (this.$takeAES) {
              param = encryptByAES(flowIds)
            } else {
              param = flowIds
            }
            getUrlByFileIds(flowIds).then((res) => {
              let decryptFile = ''
              if (this.$takeAES) {
                decryptFile = JSON.parse(decryptByAES(res.encryptRows))
              } else {
                decryptFile = res.rows
              }
              decryptFile.map((itemc) => {
                itemc.name = itemc.originalName
              })
              this.$set(item, 'flowList', decryptFile)
              this.$nextTick(() => {
                let className = `flow-list${index}`
                this.renderFileIcon(className, item.flowList)
              })
            })
          }
        })
      } finally {

      }
    },
    // 获取信息详情
    async queryFileDetail() {
      try {
        this.loading = true
        let param = undefined
        if (this.$takeAES) {
          param = encryptByAES(this.$route.query.id)
        } else {
          param = this.$route.query.id
        }
        let res = await getInfoDetailsApi(param)
        let datas = {}
        if (this.$takeAES) {
          datas = JSON.parse(decryptByAES(res.data))
        } else {
          datas = res.data
        }
        this.infoParams = datas.info
        this.fileIds = datas.files.map((item) => item.id) || []
        if (this.fileIds.length > 0) {
          let param = undefined
          if (this.$takeAES) {
            param = encryptByAES(this.fileIds)
          } else {
            param = this.fileIds
          }
          getUrlByFileIds(this.fileIds).then((res) => {
            let decryptFile = ''
            if (this.$takeAES) {
              decryptFile = JSON.parse(decryptByAES(res.encryptRows))
            } else {
              decryptFile = res.rows
            }
            decryptFile.map((item) => {
              item.name = item.originalName
            })
            this.fileList = decryptFile
            this.renderFileIcon('word-list', this.fileList)
          })
        }
      } finally {
        this.loading = false
      }

    },
    // 转派
    transferSet() {
      this.infoId = this.infoParams.id
      this.dialogTransVisible = true
      this.$refs.trans.resetParams()
    },
    // 处置
    dealSet() {
      this.infoId = this.infoParams.id
      this.dialogDealVisible = true
      this.$refs.deal.resetParams()
      if (this.infoParams.czState == 1) {
        this.processResult = '7' // 处置者
      } else {
        this.processResult = '3' // 管理者
      }
    },
    // 审核
    auditSet() {
      this.infoId = this.infoParams.id
      this.dialogAuditVisible = true
      this.$refs.audit.resetParams()
    },
    // 完结
    finishSet() {
      this.infoId = this.infoParams.id
      this.dialogFinishVisible = true
      this.$refs.finish.resetParams()
    },
    // 重新处置
    reDealSet() {
      this.infoId = this.infoParams.id
      this.dialogReDealVisible = true
      this.deadline = this.infoParams.deadline || '暂无'
      this.$refs.refDeal.resetParams()
    }

  },
  created() {
    this.getDict()
    this.queryFileDetail()
    this.queryProcessList()
  }
}
</script>
<style scoped lang="scss">
@import "./detail.scss";
</style>
