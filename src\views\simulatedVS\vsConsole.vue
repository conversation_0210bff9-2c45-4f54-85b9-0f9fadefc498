<template>
    <div class="vsConsole">
        <!-- <button @click="setStep('999')">111</button> -->
        <div class="page-left">
            <StepProgress :steps="steps" :current-step="currentStep" :next-step="nextStep" @clickStep="clickStep" />
            <VsScreen ref="miniVsScreen" class="miniVsScreen" :drillTaskId="drillTaskId" :current-step="currentStep"
                :next-step="nextStep" :newCurrentStage="newCurrentStage" :detailInfo="detailInfo"></VsScreen>
        </div>
        <div class="page-right">
            <roleOption :drillTaskId="drillTaskId" :current-step="currentStep" :next-step="nextStep" @setStep="setStep"
                :detailInfo="detailInfo"></roleOption>
        </div>
    </div>
</template>

<script>
import { WS_EVENTS, safeOn } from '@/utils/eventBus'
import { drillTaskQueryOneApi, drillProcessStartApi } from "@/api/simulatedVS/index.js";
import StepProgress from '@/views/simulatedVS/components/StepProgress.vue';
import VsScreen from '@/views/simulatedVS/vsScreen.vue';
import RoleOption from '@/views/simulatedVS/components/roleOption.vue';
export default {
    components: { StepProgress, VsScreen, RoleOption },
    data() {
        return {
            currentStep: '',
            nextStep: '',
            newCurrentStage: {
                blueStageScore: '',
                redStageScore: '',
                scoreType: '',
            }, // 当前阶段信息
            steps: [],
            detailInfo: {
                redCaptainUser: {},
                blueCaptainUser: {}
            },
        }
    },
    computed: {
        drillTaskId() {
            return this.$route.query?.drillTaskId || ''
        },
    },
    created() {
        // 安全监听消息
        this.unlistenMessage = safeOn(WS_EVENTS.MESSAGE, this.handleMessage)
    },
    beforeDestroy() {
        // 清理所有监听
        this.unlistenMessage?.()
    },
    mounted() {
        if (this.$route.query?.drillTaskId) {
            this.queryDetailInfo()
        } else {
            this.$message.error('id不能为空')
        }
    },
    methods: {
        setStep(value) {
            let { currentStep, nextStep } = value
            this.currentStep = currentStep;
            this.nextStep = nextStep;
        },
        queryDetailInfo() {
            // drillTaskQueryOneApi({ drillTaskId: this.drillTaskId }).then(res => {
            drillProcessStartApi({ drillTaskId: this.drillTaskId }).then(res => {
                if (res.code == '200') {
                    console.log('detailInfo', res.data)
                    this.detailInfo = res.data
                    this.steps = res.data.drillProcessStageRes.map(item => ({ step: item.processStageId, title: item.stageName }))
                    this.setStep({ currentStep: res.data.currentStageId, nextStep: res.data.nextStageId })

                    let stageList = res.data.drillProcessStageRes.filter(item => item.processStageId == res.data.currentStageId)
                    if (stageList.length > 0) {
                        this.newCurrentStage = JSON.parse(JSON.stringify(stageList))[0] // 当前阶段信息
                    } else {
                        this.newCurrentStage = {}
                    }
                    console.log('currentStep1', this.currentStep, 'nextStep1', this.nextStep, 'newCurrentStage', this.newCurrentStage)
                }
            })

        },
        clickStep(step) {
            console.log('stepChange', step)
        },

        handleMessage({ channel, data }) {
            if (channel === 'DRILL_COMMENT') { // 评论通知

            }
            if (channel === 'DRILL_STAGE') { // 阶段通知
                let { drillTaskId, currentStageId, nextStageId, newCurrentStage } = data
                if (drillTaskId != this.drillTaskId) {
                    return console.log('drillTaskId不一致')
                }
                this.currentStep = currentStageId
                this.nextStep = nextStageId
                this.newCurrentStage = newCurrentStage
                if (!nextStageId) {//到最终总结阶段，更新detailInfo
                    this.queryDetailInfo()
                }
            }
        },
    }
}
</script>
<style scoped lang="scss">
.vsConsole {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px 20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #f4f7f9;
    height: calc(100vh - 80px);
    font-family: PingFangSC, PingFang SC;

    .page-left {
        width: calc(100vw - 27vw - 40px - 20px);
        background-color: #fff;
        padding: 10px;

        .miniVsScreen {
            height: calc(100% - 70px);
            margin-top: 10px;
        }
    }

    .page-right {
        width: 27vw;
        background-color: #fff;
        padding: 20px;
        overflow-y: auto;
    }

}


/* 手机横屏模式优化 */
@media screen and (max-width: 992px) and (orientation: landscape) {
    .vsConsole {
        padding: 10px 10px;
        height: calc(100vh - 40px);
        flex-direction: row; /* 明确指定为水平布局 */

        .page-left {
            width: calc(100vw - 27vw - 20px - 10px);
            padding: 5px;
            height: 100%; /* 确保高度填满父容器 */

            .miniVsScreen {
                height: calc(100% - 35px);
                margin-top: 5px;
            }
        }

        .page-right {
            width: 27vw;
            padding: 5px;
            height: 100%; /* 确保高度填满父容器 */
        }
    }
}

/* 手机竖屏模式优化 - 上下布局 */
@media screen and (max-width: 768px) and (orientation: portrait) {
    .vsConsole {
        padding: 10px;
        height: auto; /* 允许高度自适应 */
        min-height: calc(100vh - 40px);
        flex-direction: column; /* 改为垂直布局 */

        .page-left {
            width: 100%; /* 宽度占满 */
            height: auto;
            padding: 5px;
            margin-bottom: 10px; /* 添加底部间距 */

            .miniVsScreen {
                height: 50vh; /* 设置一个固定高度 */
                margin-top: 5px;
            }
        }

        .page-right {
            width: 100%; /* 宽度占满 */
            height: auto;
            padding: 5px;
            min-height: 40vh; /* 设置最小高度 */
        }
    }
}
</style>