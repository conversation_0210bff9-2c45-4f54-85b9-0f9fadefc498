<template>
  <div :class="['timeline-item', {'timeline-item-right': teamType === 1}, {'marTop': isMarginTop}]">
    <div class="timeline-top">
      <!-- <span v-if="item.isCaptain" class="top-role">队长</span>
      <span v-else></span> -->
      <span>
        <!-- {{ item.role==1?'队长':'队员' }}： -->
        {{ nickName || '' }}
      </span>
      <span class="top-time">{{ createTime || '' }}</span>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'TimelineItemTemplate',
  props: {
    teamType: {
      type: [String, Number],
      default: 2 // 默认蓝方
    },
    nickName: {
      type: String,
      default: ''
    },
    createTime: {
      type: String,
      default: ''
    },
    isMarginTop: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped lang="scss">
.timeline-item {
  position: relative;
  float: left;
  clear: left;
  width: 45%;
  margin: 10em 2.5%;
  z-index: 15;

  &:after {
    content: "";
    position: absolute;
    right: -32em;
    /* 调整位置 */
    top: 20em;
    width: 0;
    height: 0;
    border-top: 10em solid transparent;
    border-bottom: 10em solid transparent;
    border-right: 10em solid #0543C6;
    /* 蓝色小三角 */
  }

  .timeline-top {
    font-size: 10em;
    display: flex;
    justify-content: space-between;
    align-items: end;

    .top-role {
      padding: 0.1em 1em;
      background-image: url("../../../assets/images/simulatedVS/blueRectangle.png");
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100% 100%;
    }

    .top-time {
      /* 时间显示样式 */
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.timeline-item-right {
  float: right;
  clear: right;

  &:after {
    left: -32.5em;
    /* 调整位置 */
    right: auto;
    border-right: none;
    border-left: 10em solid #D41503;
    /* 红色小三角 */
  }

  .timeline-top {
    .top-role {
      background-image: url("../../../assets/images/simulatedVS/redRectangle.png");
    }
  }
}

.marTop {
  margin-top: 40em !important;
}
</style>
