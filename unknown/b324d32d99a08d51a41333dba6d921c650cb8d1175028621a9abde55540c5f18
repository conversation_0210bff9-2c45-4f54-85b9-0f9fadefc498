<template>
  <div class="article-detail-wrap">
    <div class="detail-left" v-loading="loading">
      <el-popover placement="top-start" width="800" trigger="hover">
        <div v-html="detailParams.title" class="detail-popover"></div>
        <div class="detail-title" slot="reference" v-html="detailParams.title">
        </div>
      </el-popover>
      <div class="detail-operate">
        <div class="operate-content" @click="copyAritical(detailParams)">
          <img src="@/assets/images/copy.png" alt="">
          复制
        </div>
        <!-- <div class="operate-content" @click="openSendMsg(detailParams)">
            <img src="@/assets/images/send.png" alt="" class="footIcon" />
            报送
        </div> -->
        <!-- <el-dropdown>
            <div class="operate-content">
                <img src="@/assets/images/send.png" alt="">
                报送
            </div>
            <el-dropdown-menu  slot="dropdown">
                <el-dropdown-item  @click.native="openSendMsg(detailParams)">短信报送</el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown> -->
        <!-- <el-dropdown>
            <div class="operate-content">
                <img src="@/assets/images/noise.png" alt="">
                噪音
            </div>
            <el-dropdown-menu  slot="dropdown">
                <el-dropdown-item @click.native="markNoise(detailParams)">{{detailParams.isSpam?'取消噪音':'噪音'}}</el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown> -->
        <div class="operate-content" @click="goOrigin(detailParams.url)">
          <img src="@/assets/images/goOrigin.png" alt="">
          查看原文
        </div>
        <div class="operate-content" @click="copyText(detailParams.url,true)">
          <img src="@/assets/images/copyLink.png" alt="">
          拷贝地址
        </div>
        <!-- <div class="operate-content">
            <img src="@/assets/images/filterInfo.png" alt="">
            过滤信息
        </div> -->
        <el-dropdown>
          <div class="operate-content">
            <img src="@/assets/images/markIcon.png" alt="">
            标记
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="markDisposition(detailParams)">{{detailParams.deal==1?'取消处置':'处置'}}
            </el-dropdown-item>
            <el-dropdown-item @click.native="markKeyFocus(detailParams)">
              {{detailParams.follow==1?'取消重点关注':'重点关注'}}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <div class="operate-content" @click="goPublicOpinionMonitor(detailParams)">
          <img src="@/assets/images/createPlan.png" alt="">
          创建专项监测
        </div>
        <div class="operate-content" @click="goEntityIdentification(detailParams)">
          <img src="@/assets/images/recognition.png" alt="">
          实体识别
        </div>
      </div>
      <div class="site-table">
        <div class="site-wrap">
          <div class="row name-row bdl">来源</div>
          <div class="row data-row">{{ detailParams.hostName || '暂无' }}</div>
          <div class="row name-row">发布时间</div>
          <div class="row data-row"> {{ detailParams.time || '暂无' }}</div>
        </div>
        <div class="site-wrap">
          <div class="row name-row bdl">作者</div>
          <div class="row data-row" style="cursor: pointer;" @click="goHomepage(detailParams)"> {{ detailParams.author || '暂无' }}</div>
          <div class="row name-row">倾向性</div>
          <div class="row data-row"> {{ emotionParams[detailParams.emotional] || '暂无' }}</div>
        </div>
        <div class="site-wrap">
          <div class="row name-row bdl">关键词</div>
          <div class="row data-row">
                        <span v-if="keywordsList.length>0">
                            <span :class="wordsIndex==index?'active':''" v-for="(item,index) in keywordsList"
                                  :key="index" @click="setBackColor(1,item,index)">
                                {{item }}
                            </span>
                        </span>
            <span v-else>暂无</span>
          </div>
          <div class="row name-row">地域</div>
          <div class="row data-row">
                        <span v-if="addressList.length>0">
                            <span :class="areaIndex==index?'active':''" v-for="(item,index) in addressList" :key="index"
                                  @click="setBackColor(2,item,index)">
                                {{item }}
                            </span>
                        </span>
            <span v-else>暂无</span>
          </div>
        </div>
        <div class="site-wrap">
          <div class="row name-row bdl">host</div>
          <div class="row data-row"> {{ detailParams.host || '暂无' }}</div>
          <div class="row name-row bdl">原文链接</div>
          <div class="row data-row">
            <div @click="goOrigin(detailParams.url)" class="cp data-url">{{ detailParams.url }}</div>
          </div>
        </div>
      </div>
      <div class="abstract">
        <div class="form-show">
          <div class="show-content">
            <img src="@/assets/images/book.png" alt="">
            {{ detailParams.readNum||0}}
          </div>
          <div class="show-content">
            <img src="@/assets/images/good.png" alt="">
            {{ detailParams.goodNum||0 }}
          </div>
          <div class="show-content">
            <img src="@/assets/images/message.png" alt="">
            {{ detailParams.commentNum||0 }}
          </div>
          <div class="show-content">
            <img src="@/assets/images/share.png" alt="">
            {{ detailParams.reprintNum||0 }}
          </div>
          <!-- <div class="show-content">
              <img src="@/assets/images/collect.png" alt="">
              {{ detailParams.collect||0 }}
          </div>
          <div class="show-content">
              <img src="@/assets/images/look.png" alt="">
              {{ detailParams.poorNum||0 }}
          </div> -->
        </div>
      </div>
      <div class="abstract-wrap">
        <img src="@/assets/images/abstract.png" alt=""> 
        <div v-html="abstractParams"></div>
      </div>
      <div class="formH3">
        <div>
          <div class="formH3Point"></div>
          <div>正文</div>
        </div>
      </div>
      <div class="detail-content" v-html="text">
      </div>
      <template v-if="imageText">
        <div class="formH3">
          <div>
            <div class="formH3Point"></div>
            <div>图文识别</div>
          </div>
        </div>
        <div class="detail-content" v-html="imageText">
        </div>
      </template>
      <div class="formH3">
        <div class="formH3Point"></div>
        <div>相似信息</div>
      </div>
      <div class="similar-info">
        <el-table :data="tableData" border style="width: 100%" v-loading="tableLoading">
          <el-table-column prop="title" align="center" show-overflow-tooltip label="标题">
            <template slot-scope="scope">
              <a class="link" @click="goOrigin(scope.row.url)" type="primary">{{scope.row.title}}</a>
            </template>
          </el-table-column>
          <el-table-column prop="hostName" align="center" show-overflow-tooltip label="来源"></el-table-column>
          <el-table-column prop="time" align="center" label="时间"></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="detail-right">
      <div class="formH3">
        <img class="word-title" src="@/assets/images/wordCloud.png" alt="">
        <div>关键词云</div>
      </div>
      <div class="cloud-word">
        <cloudChart v-show="cloudData.length" :showLoading="loading" style="width:100%;height:100%"
                    ref="cloud" :data="cloudData"></cloudChart>
        <div v-if="cloudData.length ==0 && !loading" class="noneData">
          <img src="@/assets/images/none.png" alt="">
          <div>暂无数据</div>
        </div>
      </div>

      <div class="formH3">
        <img class="word-title" src="@/assets/images/relatedPic.png" alt="">
        <div>相关图片</div>
      </div>
      <div class="cloud-word">
        <el-carousel v-if="detailParams.picUrl&&detailParams.picUrl.length>0" :interval="3000" indicator-position="none" @change="carouselChange">
          <el-carousel-item v-for="item in detailParams.picUrl" :key="item">
            <el-image fit="cover" style="width: 100%;height: 100%;" :src="item"
                      :preview-src-list="detailParams.picUrl"></el-image>
          </el-carousel-item>
          <!-- <img :src="detailParams.picUrl[carouselImgIndex]" alt="img" class="carouselImg"/> -->
        </el-carousel>
        <div v-else class="noneData">
          <img src="@/assets/images/none.png" alt="">
          <div>暂无数据</div>
        </div>
      </div>
    </div>
    <!-- 短信报送 -->
    <SendMsg :visible.sync="sendMsgDialog" :sendMsgRow="sendMsgRow" @visibleChange="visibleChange"></SendMsg>
  </div>
</template>
<script>
import {
  infoOne,
  infoSimilar,
  updateTrash,
  updateDeal,
  updateFollow,
  infoSummary,
  detailWordApi
} from "@/api/search/index";
import cloudChart from './components/cloudChart.vue'
import {copyText, replaceHtml, copyAritical, goHomepage} from "@/utils/index"
import SendMsg from './components/sendMsg.vue';

export default {
  components: {cloudChart, SendMsg},
  data() {
    return {
      goHomepage,
      imageText: '',
      text: '',
      loading: false,
      cloudData: [],
      tableData: [],
      abstractParams: '',
      detailParams: {},
      emotionParams: {
        0: '中性',
        1: '敏感',
        2: '非敏感'
      },
      tableLoading: false,
      sendMsgDialog: false,
      sendMsgRow: {},
      keywordsList: [],
      addressList: [],
      wordsIndex: undefined,
      areaIndex: undefined,
      carouselImgIndex: 0,
    }
  },
  async created() {
    if (this.$route.query.keyWords) {
      this.keywordsList = this.$route.query.keyWords.split(' ')
    } else {
      this.keywordsList = []
    }
    await this.queryInfo()
    await this.queryAbstract()

  },
  methods: {
    processTags(container, tagName, classNameToAdd, textToSearch) {
      const tags = container.querySelectorAll(tagName);
      let foundMatch = false
      tags.forEach(tag => {
        tag.removeAttribute('class'); // 移除所有class
        if (tag.textContent.includes(textToSearch)) {
          tag.classList.add(classNameToAdd); // 如果文本包含指定内容，则添加class
          foundMatch = true
        }
      });
      if (tags.length > 0 && foundMatch) {
        tags[0].scrollIntoView({behavior: 'smooth'}); // 滚动到第一个符合条件的标签
      }
    },
    // 设置背景色
    setBackColor(type, value, index) {
      const detailContents = document.querySelectorAll('.detail-content');
      detailContents.forEach(detailContent => {
        if (type === 1) {
          this.wordsIndex = index
          this.areaIndex = undefined
          this.processTags(detailContent, 'em', 'red', value);
        } else {
          this.areaIndex = index
          this.wordsIndex = undefined
          this.processTags(detailContent, 'span', 'blue', value);
        }
      });
    },
    // 获取图文
    splitStringByMarker(str) {
      // 使用正则表达式查找特定的模式（<<<图文内容>>>）
      const regex = /<<<图文内容>>>/;
      // 检查字符串中是否包含该模式
      if (regex.test(str)) {
        // 如果包含，使用split方法切割字符串，并返回切割后的数组
        // 注意：split方法默认会移除匹配到的部分，因此如果你需要保留该部分，可能需要用其他方式处理
        // 这里假设我们不需要保留匹配到的部分，只是简单地切割
        this.text = str.split(regex)[0]
        this.imageText = str.split(regex)[1]
      } else {
        // 如果不包含，可以返回一个特定的值或消息表示没有找到
        this.text = str
        this.imageText = ''
      }
    },
    // 获取正文摘要
    async queryAbstract() {
      let htmlText = replaceHtml(this.detailParams.text)
      let res = await infoSummary(htmlText)
      this.abstractParams = res.msg
    },
    //开启发送短信弹窗
    openSendMsg(row) {
      this.sendMsgDialog = true
      this.sendMsgRow = row
      this.sendMsgRow.planId = this.$route.query.planId || ''
      this.sendMsgRow.keyWord1 = this.$route.query.keyWords || ''
      this.sendMsgRow.publishTime = this.$route.query.time
    },
    //同步sendMsgDialog值
    visibleChange(value) {
      this.sendMsgDialog = value
    },
    //处置||移除
    async markDisposition(row) {
      let val = row.deal ? 0 : 1;
      let res = await updateDeal({md5: row.md5, deal: val, indexId: row?.id, createTime: row?.time})
      if (res.code == 200) {
        this.$set(row, 'deal', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'deal', row.deal);
        this.$message.error(res.msg)
      }
    },
    //重点关注||移除
    async markKeyFocus(row) {
      let val = row.follow ? 0 : 1;
      let res = await updateFollow({md5: row.md5, follow: val, indexId: row?.id, createTime: row?.time})
      if (res.code == 200) {
        this.$set(row, 'follow', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'follow', row.follow);
        this.$message.error(res.msg)
      }
    },
    //标记噪音
    async markNoise(row) {
      let val = row.isSpam ? 1 : 2;
      let res = await updateTrash({md5: row.md5, noSpam: val})
      if (res.code == 200) {
        this.$set(row, 'isSpam', val == 1 ? false : true);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'isSpam', row.isSpam);
        this.$message.error(res.msg)
      }
    },
    // 跳转详情页
    goOrigin(url) {
      window.open(url, '_blank')
    },
    // 复制文章
    copyAritical(row) {
      row.realTitle = row.title
      row.publishTime = row.time
      row.typeName = row.hostName
      row.hitWords = this.$route.query.keyWords
      row.contentAreaCodeName = row.address
      row.emotionFlag = row.emotional
      copyAritical(row)
    },
    copyText(text, tips) {
      copyText(text, tips)
    },
    async queryInfo() {
      this.querySimilar()
      try {
        this.cloudData = []
        this.loading = true
        let res = await infoOne({
          id: this.$route.query.id,
          keyWords: this.$route.query.keyWords,
          planId: this.$route.query.planId,
          time: this.$route.query.time
        })
        this.cloudData = res.data.words
        this.loading = false
        this.detailParams = res.data.detail
        let newUrl = this.detailParams.picUrl?.map(item => process.env.VUE_APP_BASE_API + '/common/url/image?url=' + encodeURIComponent(item));
        this.detailParams.picUrl = newUrl
        console.log('this.detailParams.picUrl :>> ', this.detailParams.picUrl);
        if (this.detailParams.address) {
          this.addressList = this.detailParams.address.split(' ')
        } else {
          this.addressList = []
        }
        this.splitStringByMarker(this.detailParams.text)
        // let similar = await infoSimilar({ id: this.$route.query.id,time:this.$route.query.time,startTime:this.$route.query.startTime,endTime:this.$route.query.endTime })
        // this.tableData = similar.data
      } finally {
        this.loading = false
      }
    },
    async querySimilar() {
      try {
        this.tableLoading = true
        let res = await infoSimilar({
          id: this.$route.query.id,
          time: this.$route.query.time,
          md5: this.$route.query.md5
        })
        this.tableLoading = false
        this.tableData = res.data
      } finally {
        this.tableLoading = false
      }
    },
    carouselChange(index) {
      this.carouselImgIndex = index
    },
    goPublicOpinionMonitor(row) {
      let req = {
        id: row.id,
        time: row.time
      }
      detailWordApi(req).then(res => {
        // this.$router.push({
        // path: '/publicOpinionMonitor',
        // })
        let params = {
          kw2: res.msg,
          // id:row.id,
          // kw1:'跳转测试',
          // excludeWord:'111111',
          // planId:row.planId,
        }
        sessionStorage.setItem("datailOption", JSON.stringify(params))
        const planRoute = this.$router.resolve({
          path: '/publicOpinionMonitor'
        })
        window.open(planRoute.href, '_blank')
      })
    },
    goEntityIdentification(row) {
      let params = {
        id: row.id,
        time: row.time
      }
      const fullPath = this.$router.resolve({ path: '/entityIdentify/entityIdentify', query: params })
      window.open(fullPath.href, '_blank')
    }
  }
}
</script>
<style>
.detail-popover em {
  color: red;
  font-style: normal;
}

.detail-title em {
  color: red;
  font-style: normal;
}

.detail-content em {
  color: red;
  font-style: normal;
}

.detail-content span {
  color: blue;
  font-style: normal;
}

.detail-content em.red {
  background: #f1bf41;
}

.detail-content span.blue {
  background: #f1bf41;
}
</style>
<style lang="scss" scoped>
.detail-operate {
  display: flex;
  align-items: center;
  justify-content: center;

  .operate-content {
    display: flex;
    align-items: center;
    margin-right: 20px;
    font-size: 12px;
    line-height: 17px;
    color: #666666;
    cursor: pointer;

    img {
      width: 12px;
      margin-right: 5px;
      vertical-align: text-bottom;
    }
  }
}

.bdl {
  border-left: 1px solid #efefef;
}

.cp {
  cursor: pointer;
}

.cloud-word {
  height: 250px;

  .noneData {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      height: 80px;
      margin-bottom: 10px;
    }
  }
}

.article-detail-wrap {
  margin: 20px 40px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .detail-left {
    width: calc(100% - 420px);
    background: #fff;
    padding: 30px 40px;

    .detail-title {
      margin-bottom: 38px;
      font-weight: 500;
      font-size: 24px;
      color: #333333;
      line-height: 36px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }

  .detail-right {
    width: 400px;
    background: #fff;
    padding: 20px;
  }
}

.site-table {
  margin: 20px 0 40px 0;
  border-top: 1px solid #efefef;

  .site-wrap {
    display: flex;
    border-bottom: 1px solid #efefef;
  }

  .row {
    min-height: 40px;
    padding: 8px;
    font-size: 14px;
    line-height: 20px;
  }

  .el-row {
    border-bottom: 1px solid #efefef;

    &:last-child {
      border-bottom: none;
    }

  }

  .name-row {
    width: 15%;
    background-color: #f9f9fc;
    text-align: center;
    color: #666;
    border-right: 1px solid #efefef;
  }

  .data-row {
    border-right: 1px solid #efefef;
    width: 35%;
    text-align: center;
    color: #333;

    span {
      cursor: pointer;

      &.active {
        color: red;
      }
    }
  }

  .data-url {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.form-devide {
  justify-content: space-between;
}

.abstract {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.abstract-wrap {
  margin: 0 0 36px 0;
  padding: 15px 28px;
  background: linear-gradient(180deg, #F2F8FF 0%, #F9F9FC 100%);
  font-weight: 500;
  font-size: 16px;
  color: #333333;
  line-height: 30px;
  letter-spacing: 1px;

  img {
    width: 70px;
    height: 30px;
    vertical-align: bottom;
  }
}

.form-show {
  display: flex;
  align-items: center;

  .show-content {
    margin-right: 20px;
    font-size: 14px;
    color: #999999;
    line-height: 20px;

    img {
      display: inline-block;
      width: 16px;
      margin-right: 6px;
      vertical-align: text-bottom;
    }
  }
}

.formH3 {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 14px;
  border-bottom: 1px solid #EEE;
  font-size: 16px;
  color: #333;

  div {
    display: inline-block;
    vertical-align: middle;
  }

  .word-title {
    margin-right: 6px;
    width: 20px;
  }

  .formH3Point {
    width: 8px;
    height: 8px;
    margin-right: 5px;
    background: #247CFF;
    border-radius: 50%;
  }
}

.detail-content {
  margin-bottom: 50px;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 28px;
  max-height: 400px;
  overflow-y: auto;
}

.similar-info {
  margin-top: 40px;

  .link {
    color: #46a6ff;
    padding: 3px 0;

    &:hover {
      border-bottom: 1px solid #1890ff;
    }
  }
}

.footIcon {
  cursor: pointer;
}


.cloud-word {
  // ::v-deep .el-carousel {
  //     width: 100%;

  //     .el-carousel__container {
  //         height: auto;
  //     }

  //     img {
  //         width: 100%;
  //         display: block;
  //         margin: auto;
  //     }

  //     .carouselImg {
  //         opacity: 0;
  //     }

  //     .el-carousel__indicators--horizontal {
  //         // background: #00000030;
  //     }
  // }


  ::v-deep {
    .el-carousel {
      .el-image__error {
        height: 300px;
      }
    }
  }
}
</style>
