.dataTable-report {
  width: 100%;
  //overflow: hidden;
  // padding: 20px;
  padding: 20px 0px;
  box-sizing: border-box;
  background: #fff;

  /* 去掉全选按钮 */
  ::v-deep .el-table .disabledCheck .cell .el-checkbox__inner {
    display: none !important;
  }

  ::v-deep .el-table .el-table__header-wrapper th,
  .el-table .el-table__fixed-header-wrapper th {
    border-right: none;
  }

  .tableItemTitle {
    .tableTitle {
      margin-top: 25px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      ::v-deep em {
        color: #f46263;
      }

      .tableItemImg {
        width: 29px;
      }

      .tableItemImg,
      .tableTitleSpan {
        font-size: 16px;
        color: #333333;
        line-height: 22px;
        vertical-align: middle;
        margin-right: 14px;
      }

      .tableTitleSpan {
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        max-width: calc(100% - 190px);
        font-size: 16px;
        line-height: 22px;
        color: #333;
        cursor: pointer;
        font-weight: 600;
      }

      .article-type {
        margin: 0 0 0 10px;
        display: inline-block;
        padding: 2px 6px;
        background: #247CFF;
        font-size: 12px;
        color: #FFFFFF;
        line-height: 17px;
        border-radius: 2px;
        // width: 36px !important;
        width: fit-content;
        white-space: nowrap;
      }
    }

    .tableMain {
      margin: 9px 0 7px 0;
      position: relative;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      /* 控制显示的行数 */
      -webkit-box-orient: vertical;
      max-height: calc(2em * 2);
      /* 控制显示的高度 */
      overflow: hidden;
      font-size: 14px;
      line-height: 20px;
      color: #666;
      cursor: pointer;
      background: #F3F5FC;
      padding: 13px 20px;
      box-sizing: border-box;

      ::v-deep em {
        color: #f46263;
      }
    }

    .tableFoot {
      font-size: 12px;
      color: #666;

      .footInfo {
        display: none;
        margin-right: 40px;
        font-size: 14px;
        color: #999;

        img {
          width: 16px;
          margin-right: 6px;
        }
      }

      .footButtonGroup {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        .text-three {

          img {
            width: 56px;
            height: 22px;
            vertical-align: top;
            margin-right: 6px;
          }

          .word {
            font-size: 14px;
            color: #FFFFFF;
          }
        }

        .keyword {
          font-size: 12px;
          color: #F46263;
          line-height: 17px;
          max-width: 12em;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }

        .footButonItem {
          font-size: 12px;
          display: inline-block;
          margin-right: 20px;
          cursor: pointer;
          white-space: nowrap;

          &:last-child {
            // margin-right: 0;
          }
        }
      }

      img {
        width: 14px;
        margin-right: 3px;
      }

      img,
      span {
        display: inline-block;
        vertical-align: middle;
      }
    }
  }

  .cover-column {
    opacity: 0.5;
  }

  .follow-img {
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
  }

  .read-img {
    position: absolute;
    top: 30%;
    right: 20%;
    width: 80px;
  }
}

.export-download {
  margin-left: 15px;
  font-size: 16px;
  vertical-align: middle;
  cursor: pointer;
  color: #247CFF;
}

.exportImg {
  width: 16px;
  vertical-align: middle;
  margin-left: 20px;
}

.sortIconGroup {
  i {
    cursor: pointer;
  }

  .active {
    color: #247CFF;
  }
}

.is_data_fixed {
  padding: 0 20px 20px 20px;
  width: inherit;
  position: fixed;
  top: 80px;
  background: #fff;
  z-index: 299;
}

::v-deep {
  .el-backtop {
    background-color: rgba(255, 255, 255, 0.6);
  }
}

.emotionSelect {
  ::v-deep .el-input.el-input--mini.el-input--suffix {
    width: 80px;
  }
}

.pagination-container {
  text-align: right;
  margin-top: 10px;
  padding: 0 20px !important;

  .el-pagination {
    position: relative;
  }
}

.header-title {
  display: flex;
  justify-content: space-between;

  .title-rg {
    font-size: 14px;
    color: #247CFF;
    cursor: pointer;
  }
}

.jump-page {
  margin-right: 50px;
  font-size: 14px;
  line-height: 22px;

  span {
    color: #000;

    &.jump-line {
      margin: 0 16px;
    }
  }

  i {
    color: #000;

    &.el-icon-arrow-right {
      margin-left: 18px;
    }

    &.el-icon-arrow-left {
      margin-right: 18px;
    }
  }
}

.tableTitle {
  ::v-deep .el-input--mini .el-input__inner {
    height: 24px;
    line-height: 24px;
  }

  ::v-deep .el-input__suffix {
    right: 0;
    top: 2px;
  }

  ::v-deep .el-input--suffix .el-input__inner {
    padding-right: 0px;
    border-radius: 5px;
    border: none;
  }

  ::v-deep .el-input__inner {
    padding: 0 5px 0 10px;
  }


  .table-sense {

    ::v-deep .el-input .el-select__caret.is-reverse {
      margin-top: -4px;
    }

    ::v-deep .el-input.el-input--mini.el-input--suffix {
      width: 56px;
    }

    ::v-deep .el-icon-arrow-up:before {
      content: '\e78f';
    }

    ::v-deep .el-input__inner {
      background: #fcc3c2;
      color: #b52626;
    }

    ::v-deep .el-input .el-select__caret {
      color: #b52626;

    }
  }

  .table-nosense {
    ::v-deep .el-input.el-input--mini.el-input--suffix {
      width: 68px;
    }

    ::v-deep .el-input .el-select__caret.is-reverse {
      margin-top: -4px;
    }

    ::v-deep .el-icon-arrow-up:before {
      content: '\e78f';
    }

    ::v-deep .el-input__inner {
      background: #a8dfef;
      color: #006e8f;
    }

    ::v-deep .el-input .el-select__caret {
      color: #006e8f;
    }
  }

  .table-neutral {
    ::v-deep .el-input .el-select__caret.is-reverse {
      margin-top: -4px;
    }

    ::v-deep .el-input.el-input--mini.el-input--suffix {
      width: 56px;
    }

    ::v-deep .el-icon-arrow-up:before {
      content: '\e78f';
    }

    ::v-deep .el-input__inner {
      background: #ffdc70;
      color: #8e5d00;
    }

    ::v-deep .el-input .el-select__caret {
      color: #8e5d00;
    }
  }
}
