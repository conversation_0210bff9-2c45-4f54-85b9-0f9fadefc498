import request from '@/utils/request'

// 查看定时报告设置
export function planReportApi(params) {
  return request({
    url: '/planReport/selectPage',
    method: 'get',
    params
  })
}

// 修改定时报告设置
export function updatePlanReport(data) {
  return request({
    url: '/planReport/update',
    method: 'post',
    data
  })
}

// 新增定时报告设置
export function insertPlanReport(data) {
  return request({
    url: '/planReport/insert',
    method: 'post',
    data
  })
}
