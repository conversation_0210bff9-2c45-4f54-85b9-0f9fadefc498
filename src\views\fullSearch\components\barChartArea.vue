<template>
  <div ref="barChart" style="width: 100%; height: 100%;position: relative;"></div>
</template>
<script>
import * as echarts from "echarts";

export default {
  name: "barChart",
  props: {
    data: {
      type: Object,
      default: () => {
      },
    },
    showLoading: {
      type: Boolean,
      default: true,
    },
    allCount: {
      type: Number,
      default: 0,
    },
    toolName: {
      type: String,
      default: "饼图",
    },
    color: {
      type: Array,
      default: () => ["#F4A259", "#F07167", "#00B4D8"],
    },
    isToImg: {
      type: String,
      default: '',
    },
    isDown: {
      type: Boolean,
      default: false
    },
    isDataView: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    data() {
      this.$nextTick(() => {
        this.initChart();
      });
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    window.removeEventListener("resize", () => {
      if (this.chart) this.chart.resize();
    });
  },
  mounted() {
    this.initChart();
    if (this.chart) {
      this.chart.on('click', (params) => {
        this.$emit('goToExpendDetail', '地域分布', params.name, params.value)
      })
    }
    window.addEventListener("resize", () => {
      if (this.chart) this.chart.resize();
    });
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.barChart);
      // this.chart.showLoading();
      let data = this.data;

      const option = {
        color: ["#40C3E3"],
        grid: {
          left: "12%",
          right: "4%",
          bottom: "3%",
          top: "3%",
          // containLabel: true,
        },
        xAxis: {
          type: "value",
          splitLine: {
            show: false,
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "white",
            },
          },
        },
        yAxis: {
          type: "category",
          data: data.name,
          inverse: true,//轴反转
          axisLabel: {
            textStyle: {
              color: function (value, index) {
                // var num = myColor.length;
                // return myColor[index % num]

                if (index == 0) {
                  return "#FF0000";
                } else if (index == 1) {
                  return "#FF7F2D";
                } else if (index == 2) {
                  return "#FFB10E";
                } else {
                  return "#000000";
                }
              },
            },
            formatter: function (value, index) {
              if (value.length > 6) {
                return value.slice(0, 6) + '...';
              } else {
                return value;
              }
            },
            rich: {},
          },

          axisLine: {
            show: false,
            lineStyle: {
              color: "#000000",
            },
          },
          axisTick: {
            show: false,
            length: 9,
            alignWithLabel: true,
            lineStyle: {
              color: "#7DFFFD",
            },
          },
        },
        toolbox: {
          right: "0",
          top: "-7",
          show: true,
          feature: {
            dataView: {  //设置数据视图
              show: this.isDataView,
              title: '数据视图',
              readOnly: false,
              lang: ['数据视图', '关闭', '刷新'],
            },
            restore: {  //设置数据重置
              show: this.isDataView,
              title: '还原',
            },
            saveAsImage: {show: true, name: "地域分布TOP10"},
          },
        },
        series: {
          name: '',
          type: "bar",
          data: data.value,
          barWidth: "40%",
          datasetIndex: 1,
          label: {
            show: true,
            position: "right",
            color: "#40C3E3",
          },
        },
      };

      // if (!this.showLoading) {
      //   this.chart.hideLoading();
      // }
      this.chart.setOption(option, true);
      if (this.chart) {
        this.chart.on('rendered', () => {
          if (this.isToImg) {
            const chartRef = this.isDown ?
              this.chart.getDataURL({type: "png", pixelRatio: 2}) //拿到base64 地址，就好下载了。
              :
              this.chart.getDataURL({type: "png", pixelRatio: 2, excludeComponents: ['toolbox']}) //拿到base64 地址，就好下载了。
            this.$emit('chartRef', this.isToImg, chartRef)
          }
        });
      }
      this.$nextTick(() => {
        this.chart.resize();
      });
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep {
  textarea {
    line-height: 1.8em !important;
  }
}
</style>
