<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="模板名称" prop="templateName">
        <el-input v-model="queryParams.templateName" placeholder="请输入模板名称" clearable size="small"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <!-- <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button> -->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="模板名称" align="center" prop="templateName"/>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
                     @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createBy"/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
    <!-- 添加/修改/查看 模板 -->
    <el-dialog :visible.sync="templateDialog" :title="templateDialogTitle" width="40%">
      <span style="color: #333333;">模板内容</span>
      <div class="templateContent" v-html="templateContent"></div>
      <template #footer>
        <div style="text-align: center;">
          <span class="dialog-footer">
            <el-button type="primary" :loading="submitLoading" @click="submitForm">确定</el-button>
            <el-button @click="closeTemplateDialog">取消</el-button>
          </span>
        </div>

      </template>
    </el-dialog>
  </div>
</template>

<script>
import {templateListApi, updataTemplateApi} from "@/api/system/warnTemplate";

export default {
  components: {},
  name: "warnTemplate",
  data() {
    return {
      templateDialog: false,
      templateDialogTitle: '添加模板',
      templateContent: '',
      submitLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 模板表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
        templateName: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        templateName: [
          {required: true, message: "请输入分类名称,20字以内", trigger: "blur"},
          {min: 1, max: 20, message: '长度在20字以内', trigger: 'blur'}
        ]
      }
    };
  },
  computed: {
    deptId() {
      return this.$store.state.user.deptId
    }
  },
  created() {
    this.getList();
  },
  methods: {
    // 状态修改
    handleStatusChange(row) {
      let text = row.status == '0' ? "启用" : "停用";
      this.$confirm('确认要' + text + '"' + row.templateName + '"模板吗?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        let params = {
          id: row.id,
          status: row.status
        }
        return updataTemplateApi(params);
      }).then(() => {
        this.msgSuccess(text + "成功");
      }).catch(() => {
        row.status = row.status == '1' ? '0' : '1';
      });
    },
    // 关闭添加任务弹窗
    closeTemplateDialog() {
      this.templateDialog = false
    },
    /** 查询模板列表 */
    getList() {
      this.loading = true;
      templateListApi(this.queryParams).then(response => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postName: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.postId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    // 添加模板按钮
    handleAdd() {
      this.templateDialog = true
      this.templateDialogTitle = '添加模板'


    },
    // 添加/修改模板-提交表单
    submitForm() {
      this.templateDialog = false
    },

    // 查看按钮
    handleView(row) {
      this.templateDialog = true
      this.$nextTick(() => {

        this.disabled = true
        this.templateDialogTitle = row.templateName;
        this.templateContent = row.templateContent
      })
    },
  }
};
</script>
<style scoped lang="scss">
.templateContent {
  border: 1px solid #ccc;
  margin: 20px 20px 0;
  padding: 20px;
  color: #333333;
  line-height: 2.5em;
}
</style>
