<template>
  <div ref="weekPublic" class="chart"/>
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    chartData: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    // this.chart = null
    return {
      chart: null
    }
  },
  watch: {
    chartData() {
      this.$nextTick(() => {
        this.initChart()
      })
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.initChart()
    if (this.chart) {
      this.chart.on('click', (params) => {
        const seriesName = params.seriesName == '总' ? '全部' : params.seriesName
        const seriesValue = params.value == 0.1 ? 0 : params.value
        this.$emit('goToExpendDetail', '信息趋势', seriesName, seriesValue)
      })
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.weekPublic)
      const xxData = this.chartData?.xxData
      const yyData = this.chartData?.yyData?.map(item => item.map(subItem => subItem == 0 ? 0.1 : subItem));
      const aimData = this.chartData?.aimData
      let legendSelected = {}
      aimData?.map((item) => {
        if (item.name == '总') {
          legendSelected[item.name] = true
        } else {
          legendSelected[item.name] = false
        }
      })

      // const xxData = ['2024-05-08 00:00:00', '2024-05-08 00:00:00', '2024-05-08 00:00:00', '2024-05-08 00:00:00', '2024-05-08 00:00:00', '08/06', '08/07']
      // const yyData = [[39348858, 22, 234551, 8, 10, 3, 19], [0.1, 3, 6, 8, 2, 3, 1], [0.1, 4, 6, 8, 10, 3, 19], [1, 4, 16, 18, 10, 3, 19]]
      // const aimData = [{ name: '网站', aimId: 1 }, { name: '微博', aimId: 2 }, { name: '抖音', aimId: 3 }, { name: '头条', aimId: 4 }]


     
     const data = []
      const colors = ['#1D80DA', '#02F4FF', '#E3BC2D', '#FF6632', '#A7FFB0', '#8A01E1', '#f462d0 ']
      for (var i = 0; i < yyData?.length; i++) {
        data.push(
          {
            name: aimData[i].name,
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            // shadowColor: 'rgba(0, 0, 0, 0.1)',
            // shadowBlur: 10,
            showSymbol: false,
            lineStyle: {
              width: 2,
              color: colors[i]
            },
            emphasis: {
              color: colors[i],
              itemStyle: {
                color: colors[i],
                borderColor: 'rgba(0,196,132,0.2)',
                extraCssText: 'box-shadow: 4px 4px 4px rgba(0, 0, 0, 1);',
                borderWidth: 10
              }
            },
            data: yyData[i]
          }
        )
      }
      // aimData
      const option = {
        tooltip: {
          trigger: 'axis',
          show: true,
          // backgroundColor: '#1d398e',
          // borderColor: '#1d398e',
          // borderWidth: 0,
          // padding: 10,
          textStyle: {
            // color: '#fff', // 文字的颜色
            fontSize: '0.12rem'
          },
          formatter: function (params) {
            let html = params[0].name
            params.forEach((item, index) => {
              html += (`<br/>${item.marker + item.seriesName}  ${item.value === 0.1 ? 0 : item.value}`)
            })
            return html
          },
          position: function (point, params, dom, rect, size) {
              //  size为当前窗口大小
              if ((size.viewSize[0] / 2) >= point[0]) {
                  //其中point为当前鼠标的位置
                  return [point[0] + 30, '3%'];
              } else {
                  //其中point为当前鼠标的位置
                  return [point[0] - 200, '3%'];
              }
          }
        },
        color: ['#1D80DA', '#02F4FF', '#E3BC2D', '#FF6632', '#A7FFB0', '#8A01E1', '#f462d0 '],
        legend: {
          // selected: legendSelected,
          // selectedMode: 'single',
          type: 'scroll',
          show: true,
          icon: 'roundRect',
          // top: '-3%',
          itemWidth: 10,
          itemHeight: 8,
          textStyle: {
            fontSize: '0.12rem',
            color: '#fff'
          }
        },
        grid: {
          top: '20%',
          left: '0%',
          right: '3%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          axisLine: {
            show: true
          },
          axisTick: {
            show: true,
            inside: true
          },
          axisLabel: {
            interval: 0,
            fontSize: '0.12rem',
            // labelLine: 'showAbove',
            // rotate: -5,
            // formatter: function(value) {
            // // 根据需要设置换行的逻辑
            // // 例如，按空格或固定长度进行换行
            //     return value.replace(/(\S{10})/g, "$1\n");
            // },
            color: 'rgba(255,255,255,0.5)'
          },
          splitLine: {
            show: false
          },
          data: xxData,
          boundaryGap: false,
        }],

        yAxis: [{
          type: 'log',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#fff',
              opacity: 0.06
            }
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            margin: 20,
            fontSize: '0.12rem',
            color: 'rgba(255,255,255,0.6)',
            formatter: (value) => {
              return value === 0.1 ? 0 : value
            }
          },
          axisTick: {
            show: false
          }
        }],
        dataZoom: {
          type: 'slider',
          show: false,
          realtime: false,
          // start: 0,
          // end: 50,
          // filterMode: 'filter',
          // startValue: 0,
          // endValue: 4,
          height: '0',
          top: '90%',
          handleSize: 10,
        },
        series: data
      }
      this.chart.setOption(option, true)
    }
  }
}
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}

::v-deep .echarts_tooltip {
  font-size: 0.12rem;
  background-image: url('../../../assets/images/tooltipPop.png');
  background-size: cover;
  width: 200px;
  height: 125px;

  .tooltipMain {
    padding: 0.18rem;

    .tooltipTitle {
      color: #04d9ee
    }

    .numItem {
      color: #fff;
      margin-left: 0.1rem;
    }
  }
}
</style>

