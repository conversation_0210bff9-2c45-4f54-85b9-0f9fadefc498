<template>
  <div class="app-container">
    <div>
      <el-tabs v-model="queryParams.typeId" @tab-click="getTab">
        <el-tab-pane v-for="(item,index) in tabs" :label="item.type" :name="item.id" :key="index"></el-tab-pane>
      </el-tabs>
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
          :disabled="multiple"
        >删除
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="wordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="序号" type="index" width="55" align="center" :index="indexMethod"/>
      <el-table-column label="词组名称" align="center" prop="name"/>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.state"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" align="center"/>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改词组对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="词组名称" prop="name">
          <el-input v-model.trim="form.name" placeholder="请输入词组名称，20字以内"/>
        </el-form-item>
        <el-form-item label="关键词" prop="word">
          <el-input v-model="form.word" type="textarea" placeholder="请输入关键词，以空格隔开"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {addSelectWord, delWord, getWordLib, getWordList, updateSelectWord} from '@/api/system/storeWord.js'

export default {
  name: "StoreWord",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      wordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        typeId: undefined,
      },
      // 表单参数
      form: {},
      tabs: [],
      // 表单校验
      rules: {
        name: [
          {required: true, message: "词组名称不可为空", trigger: "blur"},
          {min: 1, max: 20, message: '词组名称限制在20字以内', trigger: 'change'}
        ],
        word: [
          {required: true, message: "关键词不可为空", trigger: "blur"}
        ]
      }
    };
  },
  created() {
    this.getWordType()
  },
  methods: {
    indexMethod(index) {
      return this.queryParams.pageNum * this.queryParams.pageSize - this.queryParams.pageSize + (index + 1)
    },
    async getWordType() {
      const res = await getWordLib({})
      this.tabs = res.data
      this.queryParams.typeId = this.tabs[0].id
      this.getList();
    },
    getTab() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      getWordList(this.queryParams).then(res => {
        this.loading = false
        this.wordList = res.rows
        this.total = res.total
      })
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.state === "0" ? "启用" : "停用";
      this.$confirm('确认要修改词组状态吗?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return updateSelectWord({id: row.id, state: row.state});
      }).then(() => {
        this.msgSuccess('操作成功');
        this.getList()
      }).catch(function () {
        row.state = row.state === "0" ? "1" : "0";
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        name: undefined,
        word: undefined
      };
      this.resetForm("form");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增词组";
      this.form.typeId = this.queryParams.typeId
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = "修改词组";
      this.form.name = row.name
      this.form.word = row.word
      this.form.id = row.id
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.title == '修改词组') {
            updateSelectWord(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSelectWord(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });

    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let wordIds = row.id || this.ids;
      if (Array.isArray(wordIds)) {
        wordIds = wordIds.join(',')
      }
      this.$confirm('是否确认删除所选中的数据?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delWord({ids: wordIds});
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
  }
}
</script>
