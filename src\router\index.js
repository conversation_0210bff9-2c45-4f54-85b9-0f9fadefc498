import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import ParentView from '@/components/ParentView';

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
 noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
 title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
 icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
 breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
 }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: (resolve) => require(['@/views/redirect'], resolve)
      }
    ]
  },
  {
    path: '/login',
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/error/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/error/401'], resolve),
    hidden: true
  },
  {
    path: '/submit',
    component: Layout,
    redirect: 'noredirect',
    hidden: true,
    children: [{
      path: 'addSubmit',
      component: (resolve) => require(['@/views/infoSubmit/addSubmit'], resolve),
      name: 'addSubmit',
      meta: {title: '新增报送'}
    }]
  },
  {
    path: '/submit',
    component: Layout,
    redirect: 'noredirect',
    hidden: true,
    children: [{
      path: 'updateSubmit',
      component: (resolve) => require(['@/views/infoSubmit/addSubmit'], resolve),
      name: 'updateSubmit',
      meta: {title: '修改报送'}
    }]
  },
  {
    path: '/send',
    component: Layout,
    redirect: 'noredirect',
    hidden: true,
    children: [{
      path: 'addSend',
      component: (resolve) => require(['@/views/infoSubmit/addSubmit'], resolve),
      name: 'addSend',
      meta: {title: '新增分发'}
    }]
  },
  {
    path: '/send',
    component: Layout,
    redirect: 'noredirect',
    hidden: true,
    children: [{
      path: 'updateSend',
      component: (resolve) => require(['@/views/infoSubmit/addSubmit'], resolve),
      name: 'updateSend',
      meta: {title: '修改分发'}
    }]
  },
  // 详情页
  {
    path: '/detail',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [{
      path: 'detailSubmit',
      component: (resolve) => require(['@/views/infoSubmit/detailSubmit.vue'], resolve),
      name: 'detailSubmit',
      meta: {title: '详情', icon: 'user', noCache: true,}
    }]
  },
  // 单点登录
  {
    path: '/singleLogin',
    component: (resolve) => require(['@/views/singleLogin/index.vue'], resolve),
    meta: {title: '单点登录'},
    hidden: true
  },
  {
    path: '/phoneDetail',
    component: (resolve) => require(['@/views/phone/detail.vue'], resolve),
    hidden: true
  },
  // 微信消息推送绑定
  {
    path: '/wechatAlert',
    component: (resolve) => require(['@/views/wechatAlert'], resolve),
    hidden: true
  },
  // 预警列表
  {
    path: '/warnList',
    component: (resolve) => require(['@/views/warnList'], resolve),
    meta: {title: '预警列表'},
    hidden: true
  },
  {
    path: '/home',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'expendDetail',
        component: (resolve) => require(['@/views/home/<USER>'], resolve),
        name: 'expendDetail',
        meta: {title: '详情'}
      }
    ]
  },
  {
    path: '/homeScreen/start',
    component: (resolve) => require(['@/views/homeScreen/start.vue'], resolve),
    meta: {title: '舆情大屏'},
    hidden: true
  },
  {
    path: '/homeScreen/screen',
    component: (resolve) => require(['@/views/homeScreen/screen.vue'], resolve),
    meta: {title: '舆情态势感知'},
    hidden: true
  },
  {
    path: '/homeScreen/screenData',
    component: (resolve) => require(['@/views/homeScreen/screenData.vue'], resolve),
    meta: {title: '数据采集态势'},
    hidden: true
  },
  {
    path: '/iframe/screen',
    component: (resolve) => require(['@/views/iframe/screen.vue'], resolve),
    hidden: true
  },
  // {
  //   path: '/',
  //   component: Layout,
  //   hidden: true,
  //   redirect: 'noredirect',
  //   children: [
  //     {
  //       path: '/publicOpinionReport',
  //       component: (resolve) => require(['@/views/publicOpinionReport/index.vue'], resolve),
  //       name: 'publicOpinionReport',
  //     }
  //   ]
  // },
  {
    path: '/outReport',
    name: 'OutReport',
    component: (resolve) => require(['@/views/outReport/index'], resolve),
    meta: {title: '报告详情'},
    hidden: true
  },
  // 创建自定义模板
  {
    path: '/publicOpinionReport',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: '/customTemplate',
        component: (resolve) => require(['@/views/publicOpinionReport/components/customTemplate.vue'], resolve),
        name: 'customTemplate',
        hidden: true,
        meta: {title: '模板创建'}
      },
      {
        path: '/updateReport',
        component: (resolve) => require(['@/views/publicOpinionReport/components/updateReport.vue'], resolve),
        name: 'updateReport',
        hidden: true,
        meta: {title: '修改报告'}
      }
    ]
  },
  // 修改报告
  // {
  //   path: '/publicOpinionReport',
  //   component: Layout,
  //   hidden: true,
  //   redirect: 'noredirect',
  //   children: [
  //     {
  //       path: 'updateReport',
  //       component: (resolve) => require(['@/views/publicOpinionReport/components/updateReport.vue'], resolve),
  //       name: 'updateReport',
  //       meta: { title: '修改报告' }
  //     }
  //   ]
  // },
  {
    path: '/fullSearch',
    component: Layout,
    redirect: 'noredirect',
    children: [
      {
        path: 'searchResult',
        component: (resolve) => require(['@/views/fullSearch/result'], resolve),
        name: 'searchResult',
        hidden: true,
        meta: {title: '搜索结果'}
      },
      {
        path: 'dataDetail',
        component: (resolve) => require(['@/views/fullSearch/detail'], resolve),
        name: 'dataDetail',
        hidden: true,
        meta: {title: '数据详情'}
      },
      {
        path: 'rankDetail',
        component: (resolve) => require(['@/views/fullSearch/rankDetail'], resolve),
        name: 'rankDetail',
        hidden: true,
        meta: {title: '排行详情'}
      },
      {
        path: 'similarArticles',
        component: (resolve) => require(['@/views/fullSearch/similarArticles'], resolve),
        name: 'similarArticles',
        hidden: true,
        meta: {title: '相似信息详情'}
      },
      {
        path: 'searchLog',
        component: (resolve) => require(['@/views/fullSearch/searchLog'], resolve),
        name: 'searchLog',
        hidden: true,
        meta: {title: '搜索记录'}
      }
    ]
  },
  {
    path: '/authorDetail',
    component: Layout,
    redirect: 'noredirect',
    children: [
      {
        path: 'authorDetail',
        component: (resolve) => require(['@/views/authorDetail/index'], resolve),
        name: 'authorDetail',
        hidden: true,
        meta: {title: '作者结果'}
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: (resolve) => require(['@/views/system/user/profile/index'], resolve),
        name: 'Profile',
        meta: {title: '个人中心', icon: 'user'}
      }
    ]
  },
  {
    path: '/dict',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'type/data/:dictId(\\d+)',
        component: (resolve) => require(['@/views/system/dict/data'], resolve),
        name: 'Data',
        meta: {title: '字典数据', icon: ''}
      }
    ]
  },
  {
    path: '/job',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'log',
        component: (resolve) => require(['@/views/monitor/job/log'], resolve),
        name: 'JobLog',
        meta: {title: '调度日志'}
      }
    ]
  },
  {
    path: '/gen',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'edit/:tableId(\\d+)',
        component: (resolve) => require(['@/views/tool/gen/editTable'], resolve),
        name: 'GenEdit',
        meta: {title: '修改生成配置'}
      }
    ]
  },
  {
    path: '/simulatedVS',
    component: Layout,
    redirect: 'noredirect',
    children: [
      {
        path: 'addSimulate',
        component: (resolve) => require(['@/views/simulatedVS/addSimulate'], resolve),
        name: 'addSimulate',
        meta: {title: '新增模拟'},
        hidden: true
      },
      {
        path: 'vsConsole',
        component: (resolve) => require(['@/views/simulatedVS/vsConsole'], resolve),
        name: 'vsConsole',
        meta: {title: '任务演练'},
        hidden: true
      }
    ]
  },
  {
    path: '/entityIdentify',
    component: Layout,
    redirect: 'noredirect',
    children: [
      {
        path: 'entityIdentify',
        component: (resolve) => require(['@/views/fullSearch/EntityIdentification'], resolve),
        name: 'entityIdentify',
        meta: {title: '实体识别'},
        hidden: true
      },
      
    ]
  },
  {
    path: '/vsScreen',
    component: (resolve) => require(['@/views/simulatedVS/vsScreenPage'], resolve),
    name: 'vsScreen',
    meta: {title: '投屏'},
    hidden: true
  },
  // 签到页面
  {
    path: '/vsSingUp',
    component: (resolve) => require(['@/views/vsSingUp'], resolve),
    meta: {title: '扫码签到'},
    hidden: true
  }
]
import {baseRoute} from '@/utils/index.js'

const url = location.pathname
const base = baseRoute(url)
export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({y: 0}),
  routes: constantRoutes,
  base
})
