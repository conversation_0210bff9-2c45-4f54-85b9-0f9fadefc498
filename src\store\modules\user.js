import { getInfo, getSystemLogoApi, login, logout } from '@/api/login'
import { getRouters } from '@/api/menu'
import { getToken, removeToken, setToken } from '@/utils/auth'
import { baseRoute } from '@/utils/index.js'
import ws from '@/utils/websocket/websocket'

const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    logoImg: "",
    backImage: "",
    sysName: "",
    titleName: "",
    titleLogo: "",
    logo: "",
    sideLogo: "",
    userId: "",
    user: {},
    deptId: "",
    newPath: ""
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
      if(token){
        ws.initConnect()
      }
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_USERID: (state, userId) => {
      state.userId = userId
    },
    SET_USER: (state, user) => {
      state.user = user
    },
    SET_DEPTID: (state, deptId) => {
      state.deptId = deptId
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_IMG: (state, logoImg) => {
      state.logoImg = logoImg
    },
    SET_BGIMG: (state, backImage) => {
      state.backImage = backImage
    },
    SET_SYSNAME: (state, sysName) => {
      state.sysName = sysName
    },
    SET_TITLELOGO: (state, titleLogo) => {
      state.titleLogo = titleLogo
    },
    SET_LOGO: (state, logo) => {
      state.logo = logo
    },
    SET_TITLENAME: (state, titleName) => {
      state.titleName = titleName
    },
    SET_SITELOGO: (state, siteLogo) => {
      state.siteLogo = siteLogo
    },
    SET_NEWPATH: (state, newPath) => {
      state.newPath = newPath
    },
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid).then(res => {
          setToken(res.token)
          commit('SET_TOKEN', res.token)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    GetRouters({ commit }) {
      return new Promise((resolve, reject) => {
        getRouters().then(res => {
          if (res.code === 200 && res.data && res.data.length > 0 && res.data[0].children && res.data[0].children.length > 0) {
            const newPath = res.data[0].children[0].path;
            commit('SET_NEWPATH', newPath)
          } else {
            console.error('无法从响应中获取有效的路由数据');
          }
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 单点登录
    LoginBySingle({ commit }, params) {
      console.log('params :>> ', params);
      return new Promise((resolve, reject) => {
        // ApiSingleLogin(params).then(res => {
        setToken(params.token)
        commit('SET_TOKEN', params.token)
        resolve()
        // }).catch(error => {
        //   reject(error)
        // })
      })
    },
    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo(state.token).then(res => {
          const user = res.user
          const avatar = user.avatar ? process.env.VUE_APP_BASE_API + user.avatar : require("@/assets/images/profile.jpg");
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', user.userName)
          commit('SET_USERID', user.userId)
          commit('SET_DEPTID', user.deptId)
          commit('SET_AVATAR', avatar)
          commit('SET_USER', user)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          window.localStorage.setItem('accountData', '')
          window.localStorage.setItem('wordsData', '')
          window.localStorage.setItem('areaData', '')
          window.localStorage.setItem('pointData', '')
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          commit('SET_USER', {})
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    },
    // 获取登录logo
    getSysLogo({ commit }, sysInfo) {
      return new Promise((resolve, reject) => {
        var url = document.location.pathname;
        var index = baseRoute(url)
        if (index) {
          getSystemLogoApi(index).then(res => {
            // debugger
            if (res.data) {
              sessionStorage.setItem("idUrl", index)
              commit('SET_IMG', res.data.loginBg2) // 登录小背景图
              commit('SET_SYSNAME', res.data.title) // 系统名称
              commit('SET_BGIMG', res.data.loginBg1) // 登录大背景图
              commit('SET_TITLELOGO', res.data.titleLogo)   // 浏览器icon
              commit('SET_SITELOGO', res.data.titleLogo)    // 标题logo
              commit('SET_LOGO', res.data.logo)
              commit('SET_TITLENAME', res.data.systemName)    // 标题name
            }
            resolve(res)
          }).catch(error => {
            reject(error)
          })
        } else {
          // 没有输入组织 默认boryou
          location.href = `boryou/login`
        }
      })
    }
  }
}

export default user
