import request from "@/utils/request";

// 查询演练任务
export function drillTaskQueryApi(data) {
  return request({
    url: "/drill/task/query",
    method: "post",
    data,
  });
}
// 新增演练任务
export function drillTaskSaveApi(data) {
  return request({
    url: "/drill/task/save",
    method: "post",
    data,
  });
}
// 查询演练人员
export function drillTaskUserApi(data) {
  return request({
    url: "/drill/task/user",
    method: "post",
    data,
  });
}
// 根据任务id查询任务
export function drillTaskQueryOneApi(data) {
  return request({
    url: "/drill/task/query/one",
    method: "post",
    data,
  });
}
// 开始演练
export function drillProcessStartApi(data) {
  return request({
    url: "/drill/process/start",
    method: "post",
    data,
  });
}
// 下一阶段
export function drillStageNextApi(data) {
  return request({
    url: "/drill/stage/next",
    method: "post",
    data,
  });
}
// 发布指令或评论
export function drillCommentPublishApi(data) {
  return request({
    url: "/drill/comment/publish",
    method: "post",
    data,
  });
}
// 上传文件
export function fileUploadFileApi(data) {
  return request({
    url: "/file/uploadFile",
    method: "post",
    data,
  });
}
// 文件链接获取
export function fileGetUrlByIdsApi(data) {
  return request({
    url: "/file/getUrlByIds",
    method: "post",
    data,
  });
}
// 离开演练(中途离开，清除推送)
export function drillProcessLeaveApi(data) {
  return request({
    url: "/drill/process/leave",
    method: "post",
    data,
  });
}
// 结束演练
export function drillProcessEndApi(data) {
  return request({
    url: "/drill/process/end",
    method: "post",
    data,
  });
}
// 查询任务下指令或评论(阶段,指令类型,队伍类型)
export function drillCommentQueryApi(data) {
  return request({
    url: "/drill/comment/query",
    method: "post",
    data,
  });
}
// 倒计时开始
export function drillTimerStartApi(data) {
  return request({
    url: "/drill/timer/start",
    method: "post",
    data,
  });
}

// 签到
export function drillSingUpApi(data) {
  return request({
    url: "/drill/sign/save",
    method: "post",
    data,
  });
}
// 任务演练签到查询
export function drillSingQueryApi(data) {
  return request({
    url: "/drill/sign/query",
    method: "post",
    data,
  });
}

// 点赞
export function commentLikeAddApi(data) {
  return request({
    url: "/comment/like/add",
    method: "post",
    data,
  });
}

// 取消点赞
export function commentLikeCancelApi(data) {
  return request({
    url: "/comment/like/cancel",
    method: "post",
    data,
  });
}

// 新增回复评论
export function commentReplyAddApi(data) {
  return request({
    url: "/comment/reply/add",
    method: "post",
    data,
  });
}

// 查询专家点评
export function drillScoreQueryApi(data) {
  return request({
    url: "/drill/score/query",
    method: "post",
    data,
  });
}

// 专家点评统计
export function drillScoreStatisticsApi(data) {
  return request({
    url: "/drill/score/statistics",
    method: "post",
    data,
  });
}