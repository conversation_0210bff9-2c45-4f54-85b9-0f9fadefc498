<template>
  <div>
    <el-dialog :visible.sync="sendMsgDialog" title="选择推送用户" width="50%" @close="cancelMsgDialog">
      <el-form ref="sendMsgForm" :model="sendMsgForm" label-width="120px" :rules="rules" @submit.prevent>
        <el-form-item v-for="item in messageTemplate" :label="`${item.name}：`" :prop="item.field" :key="item.field">
          <el-input v-model="sendMsgForm.formdata[item.field]"/>
        </el-form-item>
        <el-form-item label="处置时效：" prop="deadlineNum">
          <!-- <el-select v-model="sendMsgForm.deadlineNum" placeholder="请选择">
              <el-option v-for="item in timeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select> -->
          <el-input-number v-model="sendMsgForm.deadlineNum" controls-position="right" :min="1"></el-input-number>
          <div style="margin-left: 10px;display: inline-block;">小时</div>
        </el-form-item>
        <el-form-item label="处置建议：" prop="suggest">
          <el-input type="textarea" v-model="sendMsgForm.suggest"/>
        </el-form-item>

        <el-form-item label="姓名：" prop="userName">
          <el-input v-model.trim="sendMsgForm.userName"/>
        </el-form-item>
        <el-form-item label="手机号码：" prop="phone">
          <el-input v-model.trim="sendMsgForm.phone"/>
        </el-form-item>
        <el-form-item label="接收人：" prop="recipient">
                    <span style="display: flex;justify-content: space-between;">
                        <div style="white-space: nowrap;">请选择需要接收消息的联系人</div>
                        <el-input style="margin: 0 40px;" v-model.trim="searchWord" placeholder="请输入联系人"
                                  clearable>
                            <el-button slot="append" icon="el-icon-search" @click="getContactsList"></el-button>
                        </el-input>
                        <el-button type="primary" plain icon="el-icon-plus" @click="addNewUser">添加</el-button>
                    </span>

        </el-form-item>


        <el-table :data="contactsList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center"></el-table-column>
          <el-table-column prop="username" label="姓名" align="center">
          </el-table-column>
          <el-table-column prop="phone" label="手机号" align="center">
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <el-button type="text" style="color: #409eff;" @click="updataRow(scope.row)">修改</el-button>
              <el-popconfirm title="确定删除该联系人吗？" @confirm="delRow(scope.row)" style="margin-left: 10px;">
                <el-button slot="reference" type="text" style="color: #f56c6c;">删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>


      </el-form>
      <template #footer>
        <div style="text-align: center;">
          <el-button type="primary" :loading="sendMsgLoading" @click="submitSendMsgForm">确定</el-button>
          <el-button @click="cancelMsgDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>


    <el-dialog :visible="userDialog" :title="userDialogTitle" width="30%" @close="cancelUserDialog">
      <el-form ref="userFormRef" :model="userForm" label-width="120px" :rules="formrules" @submit.prevent>
        <el-form-item prop="id" v-show="false">
          <el-input v-model="userForm.id"/>
        </el-form-item>
        <el-form-item label="姓名：" prop="username">
          <el-input v-model.trim="userForm.username"/>
        </el-form-item>
        <el-form-item label="手机号码：" prop="phone">
          <el-input v-model.trim="userForm.phone"/>
        </el-form-item>

      </el-form>
      <template #footer>
        <div style="text-align: center;">
          <el-button type="primary" :loading="userLoading" @click="submitUserForm">确定</el-button>
          <el-button @click="cancelUserDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import { baseRoute } from '@/utils/index.js'
import {addContactsApi, updataContactsApi, delContactsApi, getContactsApi, sendMsgApi} from "@/api/search/index";
import {getDeptMessageTemplateApi} from "@/api/system/warnTemplate";

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    sendMsgRow: {
      type: Object,
      default: () => {
      },
    }
  },
  computed: {
    deptId() {
      return this.$store.state.user.deptId
    },
    userId() {
      return this.$store.state.user.userId
    }
  },
  watch: {
    visible(newValue) {
      this.sendMsgDialog = newValue; // 确保当父组件的值变化时，子组件也同步更新
      if (newValue) {
        this.$nextTick(async () => {
          let formdata = {}
          // 使用传递过来的对象对表单进行填充
          this.messageTemplate.map((item) => {
            formdata[item.field] = this.sendMsgRow[item.field]
          })
          // 判断模板中是否有改字段，进行特殊处理
          if (this.isInTemplate('courtName')) {
            formdata.courtName = this.sendMsgRow.hitCourtNames || '暂无'
          }
          if (this.isInTemplate('riskLevel')) {
            formdata.riskLevel = "低"
          }
          if (this.isInTemplate('source')) {
            formdata.source = this.getSource()
          }
          if (this.isInTemplate('summary')) {
            formdata.summary = this.replaceHtml(this.sendMsgRow.summary)
          }

          // url转换为短链
          let publishTimeStr = this.sendMsgRow.publishTime.replace(/-/g, '').replace(/[\s:]/g, '');
          // 编码中文参数
          let encodedHitWords = encodeURIComponent(this.sendMsgRow.hitWords);
          let origin = document.location.origin;
          let index = baseRoute(document.location.pathname)
          let res = await axios.post('https://t.boryou.com/su/gen', {lu: `${origin}/${index}/phoneDetail?id=${this.sendMsgRow.id}&planId=${this.sendMsgRow.planId}&keyWord1=${encodedHitWords}&publishTime=${publishTimeStr}&userId=${this.userId}`})
          formdata.url = `https://t.boryou.com/su/${res.data.data}`

          this.sendMsgForm = {
            formdata,
            deadlineNum: 24
          }


          this.$refs.sendMsgForm.resetFields()
          this.getContactsList()
        })
      }
    },
  },
  data() {
    return {
      sendMsgDialog: this.visible,
      sendMsgForm: {//发送短信表单内容
        formdata: [],
        phone: '',
        deadlineNum: 24,
        suggest: '',
        userName: '',
      },
      sendMsgLoading: false,
      rules: {
        phone: [
          {validator: this.validatePhoneNumber, trigger: 'change'}
        ],
      },
      formrules: {
        username: [
          {required: true, message: '请输入姓名', trigger: 'blur'},
        ],
        phone: [
          {required: true, message: '请输入手机号码', trigger: 'blur'},
          {validator: this.validatePhoneNumber, trigger: 'change'}
        ],
      },
      searchWord: '',
      contactsList: [],

      userDialog: false,
      userForm: {
        username: '',
        phone: ''
      },
      userLoading: false,
      selectedRows: [],
      userDialogTitle: '新增联系人',
      messageTemplate: [],

      timeOptions: [
        {
          value: 1,
          label: '1小时'
        }, {
          value: 2,
          label: '2小时'
        }, {
          value: 3,
          label: '3小时'
        }, {
          value: 4,
          label: '4小时'
        }, {
          value: 5,
          label: '5小时'
        }, {
          value: 6,
          label: '6小时'
        }, {
          value: 7,
          label: '7小时'
        }, {
          value: 8,
          label: '8小时'
        }, {
          value: 9,
          label: '9小时'
        }, {
          value: 10,
          label: '10小时'
        }, {
          value: 11,
          label: '11小时'
        }, {
          value: 12,
          label: '12小时'
        }, {
          value: 13,
          label: '13小时'
        }, {
          value: 14,
          label: '14小时'
        }, {
          value: 15,
          label: '15小时'
        }, {
          value: 16,
          label: '16小时'
        }, {
          value: 17,
          label: '17小时'
        }, {
          value: 18,
          label: '18小时'
        }, {
          value: 19,
          label: '19小时'
        }, {
          value: 20,
          label: '20小时'
        }, {
          value: 21,
          label: '21小时'
        }, {
          value: 22,
          label: '22小时'
        }, {
          value: 23,
          label: '23小时'
        }, {
          value: 24,
          label: '24小时'
        },
      ],
    }
  },
  created() {
    this.getTemplate()
  },
  methods: {
    // 获取短信模板
    getTemplate() {
      getDeptMessageTemplateApi({deptId: this.deptId}).then(res => {
        this.messageTemplate = JSON.parse(res.data.templateScript)
      })
    },
    // 判断字段是否在模板中
    isInTemplate(fieldValue) {
      const foundItem = this.messageTemplate.find(item => item.field === fieldValue);
      return foundItem !== undefined;
    },
    getSource() {
      let source = `${this.sendMsgRow.typeName != '短视频' ? this.sendMsgRow.typeName : ''} ${this.sendMsgRow.host} ${this.sendMsgRow.typeName == '短视频' ? this.sendMsgRow.author : ''}`
      return source || '暂无'
    },
    // 同步Visible到父组件
    updateVisible() {
      this.$emit('visibleChange', this.sendMsgDialog);
    },
    // 自定义手机号码验证函数
    validatePhoneNumber(rule, value, callback) {
      // 检查值是否为空，如果是，则直接通过验证
      if (!value) {
        callback();
      } else {
        const phonePattern = /^1[3-9]\d{9}$/;
        // 如果值不为空，则继续进行手机号码格式验证
        if (!phonePattern.test(value)) {
          callback(new Error('请输入正确的11位手机号码'));
        } else {
          callback();
        }
      }
    },
    // 提交表单
    submitSendMsgForm() {
      this.$refs.sendMsgForm.validate(valid => {
        if (valid) {
          let info = this.sendMsgRow


          // let phoneList = this.selectedRows.filter(item => item.phone).map(item => item.phone)
          // if (this.sendMsgForm.phone) {
          //     // 将 sendMsgForm.phone 添加到列表中，如果它尚不存在于列表中
          //     if (!phoneList.includes(this.sendMsgForm.phone)) {
          //         phoneList.push(this.sendMsgForm.phone);
          //     }
          // }
          // if (phoneList.length == 0) {
          //     this.$message.warning('请填写手机号码或选择至少一个有手机号的联系人')
          //     return
          // }


          let userIds = this.selectedRows.map(item => item.id)

          if (userIds.length == 0 && !this.sendMsgForm.phone) {
            this.$message.warning('请填写手机号码或选择至少一个有手机号的联系人')
            return
          }
          let param = {
            ...this.sendMsgForm,
            // courtName: info.hitCourtNames,
            // riskLevel: "低",
            // source: `${info.typeName != '短视频' ? info.typeName : ''} ${info.host} ${info.typeName == '短视频' ? info.author : ''}`,
            // summary: this.replaceHtml(info.title),
            // url: info.url,
            // phoneList: phoneList,
            userIds,
            md5: info.md5,
            deptId: this.deptId,
            docIndexId: info.id,
            time: info.publishTime
          }
          // delete param.phone
          this.sendMsgLoading = true
          sendMsgApi(param).then(res => {
            this.sendMsgLoading = false
            this.$message.success('发送成功')
            if (this.$parent.submitSearch) {
              this.$parent.submitSearch()
            }
            this.cancelMsgDialog()
          }).catch(err => this.sendMsgLoading = false)
        }
      })
    },
    cancelMsgDialog() {
      this.sendMsgDialog = false
      this.searchWord = ''
      this.updateVisible()
    },
    //除em标签
    replaceHtml(str) {
      let strMsg = str.replace(/<[^>]+>/g, "").substring(0, 50)
      return strMsg || '暂无'
    },

    addNewUser() {
      this.userDialog = true
      this.userDialogTitle = '新增联系人'
      this.$refs['userFormRef']?.resetFields()
    },

    submitUserForm() {
      this.$refs['userFormRef'].validate(valid => {
        if (valid) {
          this.userLoading = true
          let Api = addContactsApi
          if (this.userDialogTitle == '修改联系人') {
            Api = updataContactsApi
          }
          Api(this.userForm).then(res => {
            this.userLoading = false
            this.$message.success(this.userDialogTitle == '修改联系人' ? '修改成功' : '新增成功')
            this.cancelUserDialog()
            this.getContactsList()
          }).catch(err => this.userLoading = false)
        }
      })
    },

    cancelUserDialog() {
      this.userDialog = false
      this.$nextTick(() => {
        this.userForm = {
          username: '',
          phone: ''
        }
      })
    },

    getContactsList() {
      let param = {
        username: this.searchWord,
        type: '0'
      }
      getContactsApi(param).then(res => {
        this.contactsList = res.data
      })
    },
    //table选中项改变
    handleSelectionChange(val) {
      this.selectedRows = val
    },

    async delRow(row) {
      try {
        await delContactsApi(row.id)
        this.$message.success('删除成功')
        this.getContactsList()
      } catch (err) {

      }
    },
    async updataRow(row) {
      this.userDialog = true
      this.userDialogTitle = '修改联系人'
      this.$nextTick(() => {
        this.userForm = row
      })
      this.$refs['userFormRef']?.resetFields()
    },
  },
}
</script>

<style lang="scss" scoped></style>
