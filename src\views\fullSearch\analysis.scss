.analysis-wrap {
  background: #fff;
  padding: 20px;
  display: flex;
  justify-content: space-between;

  .analysis-contain {
    width: 88%;
  }

  .step-ul-7r {
    flex: 1;
    box-sizing: border-box;
    position: fixed;
    right: 70px; // 70
    bottom: 4%;

    // max-height: 300px;
    // overflow-y: auto;
  }

  .step-ul {
    flex: 1;
    box-sizing: border-box;
    position: fixed;
    right: 40px;
    bottom: 5%;
    // max-height: 300px;
    // overflow-y: auto;
  }

  .sideCatalogBg {
    position: relative;
    overflow: hidden;

    .step-line {
      position: absolute;
      left: 4px;
      height: 92%;
      margin-top: 12px;
      border-left: 2px solid rgba(0, 0, 0, 0.06);
    }
  }

  .ul-li {
    line-height: 24px;
    text-align: left;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    display: flex;
    margin-bottom: 10px;
    cursor: pointer;

    .step-icon {
      margin-right: 22px;

      img {
        width: 10px;
        height: 10px;
      }
    }

    .step-label {
      font-size: 14px;
      color: #303133;
    }
  }

  .tabActive {
    line-height: 24px;
    text-align: left;
    font-size: 14px;
    color: #247CFF;
    display: flex;
    margin-bottom: 10px;
    position: relative;
    cursor: pointer;

    .step-icon {
      position: relative;
      left: -8px;
      top: 0px;

      img {
        width: 30px;
        height: 14px;
      }
    }

    .step-label {
      color: #1989fe;
      margin-left: 2px;
    }
  }
}

.wrap-text {
  padding: 20px;
  font-size: 18px;
  color: #333333;
  line-height: 28px;
}

.overview {
  display: flex;
  align-items: stretch;
  /* 让子元素在高度上拉伸 */
  justify-content: space-between;
  margin-bottom: 50px;

  .overview-list {
    // width: 10%;
    flex: 1;
    box-sizing: border-box;
    position: relative;
    color: #fff;
    font-size: 1.57vw;
    line-height: 1.87vw;
    margin-left: 1vw;
    height: auto;
  }

  .overview-list:nth-child(1) {
    flex: 1.5;
    aspect-ratio: 40/24;
    margin-left: 0;
    height: 100%;
    max-width: 15%;
  }

  .overview-item {
    color: #000;
    background: #FFFFFF;
    // box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.15);
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    background-color: #eaf6ff;
  }

  .overview-total {
    background: url("../../assets/images/total.png") no-repeat center / cover;
    background-size: 100%;
  }

  .overview-positive {
    background: url("../../assets/images/positive.png") no-repeat center / cover;
    background-size: 100%;
  }

  .overview-neutral {
    background: url("../../assets/images/neutral.png") no-repeat center / cover;
    background-size: 100%;
  }

  .overview-negative {
    background: url("../../assets/images/negative.png") no-repeat 50%;
    background-size: 100%;
  }

  .overview-content {
    // padding: 1.8vw 0 0 50%;
    padding: 0 0 0 40%;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;

    p {
      margin: 0;
    }

    .overview-name {
      // margin-bottom: 0.36vw;
      font-size: 0.83vw;
      line-height: 1.2vw;
    }

    .overview-number {
      word-wrap: break-word;
      font-size: 1.3vw;
      line-height: normal;
    }

  }

  .overview-content-item {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    p {
      margin: 0;
    }

    .overview-name {
      font-size: 0.83vw;
      line-height: 1.2vw;
      color: #666666;
    }

    .overview-img {
      width: 1.3vw;
    }
  }
}

.chart-wrap {
  margin-bottom: 50px;

  .chart-title {
    display: flex;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #EEE;
    position: relative;

    .chart-name {
      margin: 0 10px;
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      line-height: 25px;
    }

    img {
      width: 18px;

      &.name-question {
        width: 16px;
      }
    }
  }

  .chart-main {
    padding-top: 25px;
    height: 400px;
    display: flex;
    justify-content: space-around;

    .noneData {

      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      img {
        width: 60px;
        height: 80px;
        margin-bottom: 10px;
      }
    }

    ::v-deep .hotSearchTable {

      .el-table__header-wrapper th,
      .el-table__fixed-header-wrapper th {
        background-color: transparent;
        border-bottom: #9DA5BE dashed 1px;

        .cell {
          color: #000;
          font-size: 14px;
          font-weight: 500;
        }
      }

    }
  }

  .chart-pie {
    width: 50%;

    .noneData {

      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 20%;

      img {
        width: 60px;
        height: 80px;
        margin-bottom: 10px;
      }
    }
  }

  .chart-table {
    width: 50%;

    .noneData {

      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 30%;

      img {
        width: 60px;
        height: 80px;
        margin-bottom: 10px;
      }
    }

    .chart-media-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .chart-table-title {
      padding: 10px 0;

      .chart-table-title-leftBlock {
        width: 4px;
        height: 16px;
        background: #247CFF;
        display: inline-block;
        vertical-align: middle;
        margin-right: 2px;
      }

      .chart-table-title-text {
        font-size: 14px;
        color: #333333;
        display: inline-block;
        vertical-align: middle;
        font-family: PingFangSC, PingFang SC;
      }
    }

    ::v-deep.el-table {
      border: 1px solid #EFEFEF;
      border-bottom: #EFEFEF;
      border-right: 1px solid #EFEFEF;

      .el-table--striped .el-table__body tr.el-table__row--striped.el-table__row--striped .el-table__row--striped td {
        background-color: #f9f9fc;
      }
    }
  }


  .c_event_img {
    width: 250px;
    max-height: 200px;
    margin-right: 20px;
  }

  .c_event_detail {
    flex-grow: 1;
    font-size: 14px;

    .c_event_digest {
      margin-top: 5px;
      line-height: 25px;
      // min-height: 70px;
    }

    .c_event_media {
      border-top: #cccccc dashed 1px;
      font-size: 12px;
      line-height: 39px;
      display: flex;
      justify-content: flex-start;

      .mediaContent {
      }

      a {
        display: inline-block;
        border-radius: 4px;
        line-height: 18px;
        color: #fff;
        background: #1B76FF;
        border: #1B76FF solid 1px;
        padding-left: 5px;
        padding-right: 5px;
        margin-right: 5px;
      }

      span {
        white-space: nowrap;
      }
    }
  }
}

.all-number {
  color: #247CFF;
  font-weight: bold;
  font-size: 16px;
}

.mediClass {
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover {
    color: #247CFF;
  }
}


.eventContextList {
  height: 500px;
  width: 100%;
  padding: 0 20px;
  // width: 500px;
  overflow-y: auto;

  .eventContextListItem {
    display: flex;
    justify-content: flex-start;
    border-top: 1px dashed #eee;
    padding: 10px 0;

    &:first-child {
      border-top: none;
      padding-top: 0;
    }

    .eventContext_num {
      height: 25px;
      width: 25px;
      text-align: center;
      line-height: 25px;
      color: #fff;
      background-color: #1B76FF;
      border-radius: 4px;
      font-weight: bold;
      margin-right: 10px;
      flex-shrink: 0
    }

    .eventContext_context {
      .context_title {
        display: flex;
        align-items: center;

        span {
          vertical-align: text-top;
          cursor: pointer;


          display: -webkit-box;
          -webkit-line-clamp: 1;
          /* 显示的行数 */
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        img {
          vertical-align: middle;
          margin-left: 5px;
          height: 16px;
        }
      }

      .context_info {
        margin-top: 10px;

        > span {
          margin-right: 20px;
          white-space: nowrap;
          color: #1677FF;
        }
      }

    }
  }
}


.eventContextList::-webkit-scrollbar-track-piece {
  background-color: transparent;
}

.eventContextList::-webkit-scrollbar {
  width: 7px;
  height: 7px;
  background-color: transparent;
}

.eventContextList::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: hsla(220, 4%, 58%, .3);
}
