import {parseTime} from './boryou'
import request from '@/utils/request'
import {Message} from 'element-ui'
import {jumpLinkApi} from "@/api/menu";
import {downloadFileById} from "@/api/infoSubmit/index"

/**
 * 表格时间格式化
 */
export function formatDate(cellValue) {
  if (cellValue == null || cellValue == "") return "";
  var date = new Date(cellValue)
  var year = date.getFullYear()
  var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) s++
    else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xDC00 && code <= 0xDFFF) i--
  }
  return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    })
  ).join('&')
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

export function makeMap(str, expectsLowerCase) {
  const map = Object.create(null)
  const list = str.split(',')
  for (let i = 0; i < list.length; i++) {
    map[list[i]] = true
  }
  return expectsLowerCase
    ? val => map[val.toLowerCase()]
    : val => map[val]
}

export const exportDefault = 'export default '

export const beautifierConf = {
  html: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'separate',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  },
  js: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'normal',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  }
}

// 首字母大小
export function titleCase(str) {
  return str.replace(/( |^)[a-z]/g, L => L.toUpperCase())
}

// 下划转驼峰
export function camelCase(str) {
  return str.replace(/_[a-z]/g, str1 => str1.substr(-1).toUpperCase())
}

export function isNumberStr(str) {
  return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str)
}

export function resetTag(arr) {
  arr.map((item) => {
    item.tag = false;
  });
  return arr
}

export function transImage(value,host) {
  let imgSrc = ''
  let valueNew
  if (value !== 11) {
    valueNew = Number(value)
  } else {
    valueNew =  host
  }
  switch (valueNew) {
    case 0:
      imgSrc = require('@/assets/images/0.png')
      break
    case 3:
      imgSrc = require('@/assets/images/3.png')
      break
    case 5:
      imgSrc = require('@/assets/images/5.png')
      break
    case 1:
      imgSrc = require('@/assets/images/1.png')
      break
    case 2:
      imgSrc = require('@/assets/images/2.png')
      break
    case 6:
      imgSrc = require('@/assets/images/6.png')
      break
    case 8:
      imgSrc = require('@/assets/images/8.png')
      break
    case 9:
      imgSrc = require('@/assets/images/9.png')
      break
    case 10 :
      imgSrc = require('@/assets/images/10.png')
      break
    case 11 :
      imgSrc = require('@/assets/images/11.png')
      break
    case 17 :
      imgSrc = require('@/assets/images/17.png')
      break
    case 24 :
      imgSrc = require('@/assets/images/24.png')
      break
    case 25 :
      imgSrc = require('@/assets/images/25.png')
      break
    case 26 :
      imgSrc = require('@/assets/images/26.png')
      break
    case'懂车帝':
      imgSrc = require('@/assets/images/dcd.png')
      break
    case'抖音':
      imgSrc = require('@/assets/images/douyin.png')
      break
    case'小红书':
      imgSrc = require('@/assets/images/xhs.png')
      break
    case'今日头条':
      imgSrc = require('@/assets/images/today.png')
      break
    case'西瓜视频':
      imgSrc = require('@/assets/images/watermelon.png')
      break
    case'微博':
      imgSrc = require('@/assets/images/3.png')
      break
    case'快手':
      imgSrc = require('@/assets/images/fasthead.png')
      break
    case'好看视频':
      imgSrc = require('@/assets/images/beauty.png')
      break
    case'哔哩哔哩':
      imgSrc = require('@/assets/images/bilibili.png')
      break
    case'美拍':
      imgSrc = require('@/assets/images/mp.png')
      break
    case'秒拍':
      imgSrc = require('@/assets/images/second.png')
      break
    case'其他':
      imgSrc = require('@/assets/images/11.png')
      break
    default:
      imgSrc = require('@/assets/images/11.png')
      break
  }
  return imgSrc
}

export function downPostBlobFile(url, data, fileName, callback = () => {
}, errCallback = () => {
}) {
  return request({
    url,
    method: 'post',
    responseType: 'blob',
    data: data
  }).then(response => {
    // 处理返回的文件流
    const blob = response
    if (blob && blob.size === 0) {
      this.$notify.error('内容为空，无法下载')
      return
    }
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    window.setTimeout(function () {
      window.URL.revokeObjectURL(blob)
      document.body.removeChild(link)
    }, 0)
    callback()
  }).catch(err => {
    errCallback()
  })
}

export function downGetBlobFile(url, params, fileName, callback = () => {
}, errCallback = () => {
}) {
  return request({
    url,
    method: 'get',
    responseType: 'blob',
    params
  }).then(response => {
    // 处理返回的文件流
    const blob = response
    if (blob && blob.size === 0) {
      this.$notify.error('内容为空，无法下载')
      return
    }
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    window.setTimeout(function () {
      window.URL.revokeObjectURL(blob)
      document.body.removeChild(link)
    }, 0)
    callback()
  }).catch(err => {
    errCallback()
  })
}

// 获取地址前缀
export function baseRoute(url) {
  const deductUrl = url.substring(1, url.length + 1)
  const indexUrl = deductUrl.substring(0, deductUrl.indexOf('/'))
  return indexUrl
}

// 复制
export function copyText(content, tips) {
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(content).then(() => {
      if (tips) {
        Message({
          message: '成功复制网址：' + content,
          type: 'success'
        })
      } else {
        Message({
          message: '复制成功',
          type: 'success'
        })
      }
    }).catch((error) => {
      Message({
        message: '复制失败',
        type: 'error'
      })
    })
  } else {
    // 创建text area
    const textArea = document.createElement('textarea')
    textArea.value = content
    // 使text area不在viewport，同时设置不可见
    textArea.style.position = 'absolute'
    textArea.style.opacity = 0
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    return new Promise((res, rej) => {
      // 执行复制命令并移除文本框
      document.execCommand('copy') ? res() : rej()
      textArea.remove()
      Message({
        message: '复制成功',
        type: 'success'
      })
    })
  }
}

// 除em标签
export function replaceHtml(str) {
  // 正则表达式匹配HTML标签
  const regex = /<[^>]+>/g;
  return str.replace(regex, '');
}

// 复制文章
export function copyAritical(row) {
  let text = `标题：${replaceHtml(row.realTitle || row.title || '无')}\n摘要：${replaceHtml(row.text).substring(0, 200) || '暂无'}\n链接：${row.url || '暂无'}\n时间：${row.publishTime || '暂无'}\n来源：${row.typeName || '暂无'}\n作者：${row.author || '暂无'}\n属性：${row.emotionFlag == 2 ? "非敏感" : row.emotionFlag == 1 ? "敏感" : "中性"}\n涉及关键词：${row?.hitWords || '暂无'}\n精准地域：${row?.contentAreaCodeName || '暂无'}`
  copyText(text)
}

export function goHomepage(row) {
  // let params = {
  //   type: row.type,
  //   url: row.url,
  //   bizId: row.bizId,
  //   author: row.author,
  //   authorId: row.authorId
  // }
  // jumpLinkApi(params).then(res => {
  //   console.log('res', res)
  //   if (res.code == 200) {
  //     window.open(res.data, '_blank')
  //   }
  // })

  let query = {
    type: row.type,
    author: row.author,
    authorId: row.authorId,
    bizId: row.bizId,
    url: row.url,
    host: row.host,
  }

  const fullPath = this.$router.resolve({ path: '/authorDetail/authorDetail', query })
  window.open(fullPath.href, '_blank')
}

// 下载文件-支持ie浏览器
export function downloadFile(file) {
  downloadFileById(file.id).then((res) => {
    blobTransFile(res, file.originalName)
  })
}

// 判断文件是不是照片类型
export function isImgType(item) {
  let fileName = item.name;
  let fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
  if (
    ["png", "jpg", "jpeg", ".gif", "PNG", "JPG", "JPEG", "GIF"].indexOf(
      fileType
    ) != -1
  ) {
    return true;
  } else {
    return false;
  }
}

// 复制原文链接
export function copyLink(link) {
  this.$copyText(link).then(message => {
    this.$message.success('复制成功')
  }).catch(err => {
    this.$message.error('复制失败')
  })
}

function blobTransFile(response, fileName) {
  const blob = response
  if (blob && blob.size === 0) {
    this.$notify.error('内容为空，无法下载')
    return
  }
  const link = document.createElement('a')
  link.href = window.URL.createObjectURL(blob)
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  window.setTimeout(function () {
    window.URL.revokeObjectURL(blob)
    document.body.removeChild(link)
  }, 0)
}

// 文件列表icon样式重置
export function renderFileIcon(className, fileList) {
//找出所有文件图标的class
  this.$nextTick(() => {
    let fileElementList = document.getElementsByClassName(
      className
    );
    if (fileElementList && fileElementList.length > 0) {
      for (let ele of fileElementList) {
        let fileName = ele.innerText;
        //获取文件名后缀
        let fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
        let iconElement = ele.getElementsByTagName("i")[0];
        let type = fileType.trim().replace(/\s/g, "");
        if (["png", "jpg", "jpeg", ".gif", "PNG", "JPG", "JPEG", "GIF",].indexOf(type) != -1
        ) {
          iconElement.className = "imgicon-img"; // 图⽚，动图
          ele.classList.add("imgicon"); // 照片的话不显示在文件列表里
        } else if (["doc", "docx", "DOC", "DOCX"].indexOf(type) != -1) {
          iconElement.className = "imgicon-docx"; // 文档
          let file = fileList.find((item) => item.name.trim() == fileName.trim());
          if (file.url) {
            ele.addEventListener("click", function () {
              downloadFile(file)
            });
          } else {
            ele.addEventListener("click", function () {
              downloadFile(file)
            });
          }
        } else if (["xls", "xlsx", "XLS", "XLSX"].indexOf(type) != -1) {
          iconElement.className = "imgicon-xlsx"; // 表格
          let file = fileList.find((item) => item.name.trim() == fileName.trim());
          if (file.url) {
            ele.addEventListener("click", function () {
              downloadFile(file)
            });
          } else {
            ele.addEventListener("click", function () {
              downloadFile(file)
            });
            // let url = URL.createObjectURL(file.raw);
            // ele.setAttribute("href", url);
            // ele.setAttribute("target", "_blank");
          }
        } else if (["ppt", "pptx", "PPT", "PPTX"].indexOf(type) != -1) {
          iconElement.className = "imgicon-pptx"; // PPT
          let file = fileList.find((item) => item.name.trim() == fileName.trim());
          if (file.url) {
            ele.addEventListener("click", function () {
              downloadFile(file)
            });
          } else {
            ele.addEventListener("click", function () {
              downloadFile(file)
            });
            // let url = URL.createObjectURL(file.raw);
            // ele.setAttribute("href", url);
            // ele.setAttribute("target", "_blank");
          }
        } else if (["zip", "ZIP"].indexOf(type) != -1) {
          iconElement.className = "imgicon-zip"; // 压缩包
          let file = fileList.find((item) => item.name.trim() == fileName.trim());
          if (file.url) {
            ele.addEventListener("click", function () {
              downloadFile(file)
            });
          } else {
            ele.addEventListener("click", function () {
              downloadFile(file)
            });
            // let url = URL.createObjectURL(file.raw);
            // ele.setAttribute("href", url);
            // ele.setAttribute("target", "_blank");
          }
        } else if (["pdf", "PDF"].indexOf(type) != -1) {
          iconElement.className = "imgicon-pdf"; // PDF
          let file = fileList.find((item) => item.name.trim() == fileName.trim());
          if (file.url) {
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
              ele.addEventListener("click", function () {
                downloadFile(file.url)
              });
            } else {
              ele.setAttribute("href", file.url);
              ele.setAttribute("target", "_blank");
            }
          } else {
            let url = URL.createObjectURL(file.raw);
            ele.setAttribute("href", url);
            ele.setAttribute("target", "_blank");
          }
        } else {
          iconElement.className = "imgicon-default"; //默认图标
        }
      }
    }
  });
}

// 获取文件名称
export function fileZipName(name) {
  let index = name.lastIndexOf('.')
  let result = name.slice(0, index)
  return `${result}.zip`
}

// 判断是否可操作
export function canOperate(userId, assigneer) {
  let assigneerArr = assigneer ? assigneer.split(',') : []
  let param = assigneerArr.find((item) => item == userId)
  if (param) {
    return true
  } else {
    return false
  }
}

// atobToblob
export function atobToblob(intArray, type) {
  // 创建一个 Blob 对象
  var blob = new Blob([intArray], {type: type});
  return blob;
}
// 获取域名
export function getDomain(url) {
  let startIndex = url.indexOf("//") + 2;
  let endIndex = url.indexOf("/", startIndex);
  if (endIndex === -1) {
      endIndex = url.length;
  } 
  let domain = url.substring(startIndex, endIndex);
  return domain
}
