import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/boryou.scss' // boryou css
import App from './App'
import store from './store'
import router from './router'
import permission from './directive/permission'
import ws from '@/utils/websocket/websocket'
import '@/directive/table-sticky.js'
import SelectAdd from "@/components/SelectAdd";
import './assets/icons' // icon
import './permission' // permission control
import {getDicts} from "@/api/system/dict/data";
import {getConfigKey} from "@/api/system/config";
import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  download,
  handleTree
} from "@/utils/boryou";
import Pagination from "@/components/Pagination";
import FloatingButton from '@/components/FloatingButton';
import VueClipBoard from 'vue-clipboard2';
import md5 from 'md5'
// 自定义表格工具扩展
import RightToolbar from "@/components/RightToolbar"
import scroll from 'vue-seamless-scroll'
//全局修改默认配置，点击空白处不能关闭弹窗
Element.Dialog.props.closeOnClickModal.default = false

// 全局方法挂载
Vue.prototype.$md5 = md5
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.$ws = ws

Vue.prototype.msgSuccess = function (msg) {
  this.$message({showClose: true, message: msg, type: "success", duration: 1000});
}

Vue.prototype.msgError = function (msg) {
  this.$message({showClose: true, message: msg, type: "error", duration: 2000});
}

Vue.prototype.msgInfo = function (msg) {
  this.$message.info(msg);
}

// 全局组件挂载
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('SelectAdd', SelectAdd)
Vue.component('FloatingButton', FloatingButton)

Vue.use(scroll)
Vue.use(permission)
Vue.use(VueClipBoard);
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false
router.beforeEach((to, from, next) => {
  // 逻辑...
  let container = document.getElementsByClassName('topbar-container')
  if (container.length > 0) {
    let links = container[0].querySelectorAll('a');
    let haveSend = ''
    let haveSubmit = ''
    let searchPage = ''
    // 遍历这些a标签
    links.forEach((link) => {
      // 检查href属性是否包含"info"
      if (link.href.includes('infoSend')) {
        let liEle = link.querySelector('li')
        haveSend = liEle
      }
      if (link.href.includes('infoSubmit')) {
        haveSubmit = link.querySelector('li')
      }
      if (link.href.includes('searchRank')) {
        searchPage = link.querySelector('li')
      }
    });
    if (to.path == '/send/addSend' || to.path == '/send/updateSend' || to.path == '/infoSend') {

    } else {
      if (haveSend) {
        if (haveSend.classList.contains('is-active')) {
          haveSend.classList.remove('is-active');
        }
      }
    }
    if (to.path == '/submit/addSubmit' || to.path == '/submit/updateSubmit' || to.path == '/infoSubmit') {

    } else {
      if (haveSubmit) {
        if (haveSubmit.classList.contains('is-active')) {
          haveSubmit.classList.remove('is-active');
        }
      }
    }
    if (to.path == '/fullSearch/rankDetail' || to.path == '/fullSearch/searchRank') {

    } else {
      if (searchPage) {
        if (searchPage.classList.contains('is-active')) {
          searchPage.classList.remove('is-active');
        }
      }
    }
  }
  next()
})
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
