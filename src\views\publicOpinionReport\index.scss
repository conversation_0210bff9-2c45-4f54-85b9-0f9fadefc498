.dataContainer {
  width: 100%;
  // padding: 20px 40px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  background: #F4F7FB;

  .dataSidebar {
    width: 100px;
    background: #fff;
    box-sizing: border-box;
    margin-right: 10px;
    height: calc(100vh - 80px);
    overflow-y: auto;
    padding-top: 56px;
    // overflow-y: auto;
    // transition: width .2s;
    box-shadow: 2px 0px 4px 0px rgba(0, 0, 0, 0.15);
  }

  // .tree-button {
  //     position: absolute;
  //     top: 50%;
  //     left: 124px;
  //     z-index: 99;
  //     width: 16px;
  //     // height: 50px;
  //     color: #fff;
  //     border-color: transparent #848ca6 transparent transparent;
  //     border-style: solid;
  //     border-width: 12px 12px 12px 0;
  //     transform: translateY(-50%);
  //     cursor: pointer;
  //     transition: border-right .2s;

  //     &:hover {
  //         border-right: 16px solid #848ca6;
  //     }

  //     img {
  //         width: 16px;
  //         margin: 5px 0 5px 2px;
  //     }
  // }
}

// .dataContainer.collapse {
//     .dataSidebar {
//         // position: absolute;
//         width: 0px;
//         padding: 0;
//         margin: 0;
//     }

//     .tree-button {
//         left: -16px;
//         transform: translateY(-50%) rotateY(180deg);
//         transform-origin: 100% 50%;
//     }

//     .dataSide {
//         display: none;
//     }
// }

.toolbar-menus {
  width: 100px;
  height: 100%;
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-direction: column;
  align-items: center;

  li {
    width: 84px;
    height: 84px;
    list-style: none;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #999999;
    line-height: 22px;
    font-size: 16px;
    margin-bottom: 68px;
    // padding-top: 8px;

    img {
      width: 30px;
      height: 30px;
      margin-bottom: 10px;
    }

    &.active {
      color: #247CFF;
      background: #EDF2FE;
      border-radius: 8px;
    }
  }
}

.report-content {
  flex: 1;
  background: #F4F7FB;
  height: calc(100vh - 80px);
  overflow-y: auto;
  padding: 10px 20px 30px 0px;
}
