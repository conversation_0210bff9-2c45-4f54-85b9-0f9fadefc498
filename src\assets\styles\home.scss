$themes: (
  light: (//整体主色调/菜单栏背景/图标.按钮主色/悬停状态
    mainColor: #333333, //主题色
    titleColor: #999999, //标题色
    borderColor: #247CFF, //边框色
    timeFontColor: #247CFF, // 时间-字体颜色
    dateBorderColr: #DCDEE0, // 日期-边框色
    subTitleColor: #999999, // 小标题-颜色
    homeBackground: #F4F7FB, // 背景颜色
    accountColor:#333,
    yqColor:#247CFF
  ),
  dark: (//整体主色调/菜单栏背景/图标.按钮主色/悬停状态
    mainColor: #FFFFFF, //主题色
    titleColor: #FFFFFF, // 标题色
    borderColor: #00B3FF, // 边框色
    timeFontColor: #FFFFFF, // 时间-字体颜色
    dateBorderColr: #FFFFFF, // 日期-边框色
    subTitleColor: #02F4FF, // 小标题-颜色
    homeBackground: none,
    accountColor:#02F4FF,
    yqColor:#57CDFF
  ),
);

//切换主题时 获取不同的主题色值
@mixin themeify {

  @each $theme-name,
  $theme-map in $themes {
    //!global 把局部变量强升为全局变量
    $theme-map: $theme-map !global;

    //判断html的data-theme的属性值  #{}是sass的插值表达式
    //& sass嵌套里的父容器标识   @content是混合器插槽，像vue的slot
    [data-theme="#{$theme-name}"] & {
      @content;
    }
  }
}

//从主题色map中取出对应颜色
@function themed($key) {
  @return map-get($theme-map, $key);
}

//获取背景颜色
@mixin background_color($color) {
  @include themeify {
    background-color: themed($color) !important;
  }
}

//获取字体颜色
@mixin font_color($color) {
  @include themeify {
    color: themed($color) !important;
  }
}

// 获取边框颜色
@mixin border_color($color-key) {
  @include themeify {
    border: 1px solid themed($color-key) !important;
  }
}
