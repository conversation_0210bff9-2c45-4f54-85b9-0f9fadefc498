<template>
    <div class="app-container">

        <div class="search-area">
            <div class="head-input">
                <el-input placeholder="" ref="myInput" v-model.trim="queryParams.taskTitle"
                    @keyup.enter.native="handleQuery" class="input-with-select">
                    <template slot="prepend">
                        <div class="append-head">标题<span class="line"></span></div>
                    </template>
                    <el-button slot="append" icon="el-icon-search" @click="handleQuery">搜索</el-button>
                </el-input>
            </div>

            <div class="add-button">
                <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">
                    新增
                </el-button>
            </div>
        </div>



        <div v-if="postList.length" class="list-box" v-loading="loading">
            <div class="list-item" v-for="item in postList" :key="item.typeId">
                <div class="list-item-box" @click="handleEdit(item)">
                    <div class="box-top">
                        <el-tooltip class="item" effect="dark" :content="item.taskTitle" placement="top-start">
                            <div class="box-top-title">{{ item.taskTitle }}</div>
                        </el-tooltip>
                        <div class="box-top-status">
                            <div v-show="item.status==3" class="status-over">已结束</div>
                            <div v-show="item.status==2" class="status-going">
                                <img src="@/assets/images/simulatedVS/status-going.png" alt="">
                                进行中
                            </div>
                            <div v-show="item.status==1" class="status-nostart">
                                <img src="@/assets/images/simulatedVS/status-nostart.png" alt="">
                                未开始
                            </div>
                        </div>
                    </div>
                    <!-- <el-tooltip class="item" effect="dark" content="南宁慧泊停车费舆论风波" placement="top-start"> -->
                    <div class="box-describe">
                        {{ item.taskContent }}
                    </div>
                    <!-- </el-tooltip> -->
                    <div class="box-info">
                        <img src="@/assets/images/simulatedVS/drillEvent.png" alt="">
                        <span>演练事件：{{ item.drillEvent }}</span>
                    </div>
                    <div class="box-score">
                        <div class="box-score-item">
                            <img src="@/assets/images/simulatedVS/blueRectangle.png" alt="">
                            <!-- <div style="flex: 1;">蓝方：{{item.blueScore}}分</div> -->
                            <div style="flex: 1;">蓝方：{{item.blueStageScore||0}}分</div>
                            <div style="color: #999999;">{{ item.estimateDrillTime }}</div>
                        </div>
                        <div class="box-score-item">
                            <img src="@/assets/images/simulatedVS/redRectangle.png" alt="">
                            <!-- <div style="flex: 1;">红方：{{item.redScore}}分</div> -->
                            <div style="flex: 1;">红方：{{item.redStageScore||0}}分</div>
                            <div style="color: #999999;">{{ parseTime(item.drillEndTime) }}</div>
                        </div>
                    </div>
                </div>
                <div class="list-item-btn">
                    <div class="option-btn" @click="goVsScreen(item)">
                        <img src="@/assets/images/simulatedVS/screenCast.png" alt="">
                        投屏
                    </div>
                    <div v-if="item.roleInfo!='spectator'" class="option-btn" @click="goVsConsole(item)">
                        <img src="@/assets/images/simulatedVS/vsIcon.png" alt="">
                        演练
                    </div>
                    <!-- <div v-if="item.status==3" class="option-btn" @click="goVsConsole(item)">
                        <img src="@/assets/images/simulatedVS/replayIcon.png" alt="">
                        复盘
                    </div>
                    <div v-if="item.status!=3" class="option-btn" style="color: #999999;"><img
                            src="@/assets/images/simulatedVS/replayGrey.png" alt="">复盘</div> -->
                    <div class="option-btn" @click="openSingUpSheet(item)">
                        <img src="@/assets/images/simulatedVS/singUpSheet.png" alt="">
                        签到表
                    </div>
                </div>
            </div>
        </div>
        <el-empty v-else description="暂无演练"></el-empty>

        <pagination v-show="total>0" style="background-color: transparent;" :total="total"
            :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList"
            :pageSizes="[8, 12,24, 36, 48]" />

        <el-dialog :visible.sync="singUpSheetDialog" :title="dialogTitle" width="80%" append-to-body>
            <el-table v-loading="tableLoading" :data="singUpSheetData" border stripe style="width: 100%"
                max-height="450">
                <el-table-column label="序号" type="index" align="center"></el-table-column>
                <el-table-column prop="userName" label="姓名" align="center"></el-table-column>
                <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
                <!-- <el-table-column prop="createTime" label="时间" align="center"></el-table-column> -->
            </el-table>
            <pagination v-show="sheetTotal>0" style="background-color: transparent;" :total="sheetTotal"
                :page.sync="sheetParams.pageNum" :limit.sync="sheetParams.pageSize" @pagination="getSingUpSheet" />
            <template #footer>
                <div class="dialog-footer" style="text-align: center;">
                    <el-button type="primary" @click="singUpSheetDialog = false">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { drillTaskQueryApi, drillSingQueryApi } from "@/api/simulatedVS/index.js";

export default {
    name: "SimulatedVS",
    data() {
        return {
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 岗位表格数据
            postList: [],
            // 状态数据字典
            statusOptions: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 8,
                taskTitle: undefined,
            },

            singUpSheetDialog: false, // 签到表弹窗
            singUpSheetData: [],
            tableLoading: false,
            dialogTitle: '',
            // 签到表查询参数
            sheetParams: {
                pageNum: 1,
                pageSize: 20,
                drillTaskId: undefined,
            },
            sheetTotal: 0,
        };
    },
    computed: {
        userId() {
            return this.$store.state.user.userId
        }
    },
    created() {
        this.getList();
        this.getDicts("sys_normal_disable").then(response => {
            this.statusOptions = response.data;
        });
    },
    methods: {
        /** 查询列表 */
        getList() {
            this.loading = true;
            drillTaskQueryApi({ ...this.queryParams, userId: this.userId }).then(response => {
                this.postList = response.data.records;
                this.total = response.data.total;
                this.loading = false;
            });
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.$router.push({
                path: '/simulatedVS/addSimulate',
            })
        },
        /** 编辑操作 */
        handleEdit(item) {
            const { drillTaskId } = item
            this.$router.push({
                path: '/simulatedVS/addSimulate',
                query: {
                    drillTaskId,
                }
            })
        },

        goVsConsole(item) {
            const { drillTaskId } = item
            this.$router.push({
                path: '/simulatedVS/vsConsole',
                query: {
                    drillTaskId,
                }
            })
        },
        goVsScreen(item) {
            const { drillTaskId } = item
            const targetRoute = this.$router.resolve({
                path: '/vsScreen',
                query: {
                    drillTaskId,
                }
            });
            window.open(targetRoute.href, '_blank');
        },
        openSingUpSheet(item) {
            this.singUpSheetDialog = true
            this.dialogTitle = `签到表（${item.taskTitle}）`
            this.sheetParams = {
                pageNum: 1,
                pageSize: 20,
                drillTaskId: item.drillTaskId,
            }
            this.getSingUpSheet()
        },
        getSingUpSheet() {
            this.tableLoading = true
            let query = this.sheetParams
            drillSingQueryApi(query).then(response => {
                this.tableLoading = false
                if (response.code == '200') {
                    this.singUpSheetData = response.data.records
                    this.sheetTotal = response.data.total
                }
            }).catch(error => {
                this.tableLoading = false
            })
        },
    }
};
</script>
<style scoped lang="scss">
.app-container {
    font-family: PingFangSC, PingFang SC;
    background: #F4F7FB;
    min-height: calc(100vh - 80px);

    .search-area {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        margin: 40px 0;

        .add-button {
            margin: 0 20px 0 auto;
        }
    }

    .head-input {
        border: 1px solid #A9CBFF;
        border-radius: 5px;
        width: 45vw;
        display: flex;
        align-items: center;
        // margin: 0 auto 0 20px;

        .append-head {
            display: flex;
            align-items: center;
        }

        .line {
            display: inline-block;
            margin-left: 26px;
            width: 1px;
            height: 35px;
            background: #EEEEEE;
        }

        ::v-deep {
            .el-input-group__prepend {
                background: #fff;
                color: #666;
                padding: 0 0 0 20px;
                vertical-align: middle;
                display: table-cell;
                border-radius: 4px;
                white-space: nowrap;
                border: none;
            }

            .el-input__inner {
                height: 64px;
                line-height: 64px;
                border: none;
                vertical-align: middle;
                display: table-cell;
            }

            .el-input-group__append {
                border: none;
                background-color: #247CFF;
                color: #fff;
            }
        }


    }

    .list-box {
        display: grid;
        grid-template-columns: repeat(4, minmax(0, 1fr));
        gap: 10px 20px;
        /* 左右间距20px，上下间距10px */
        padding: 10px;

        /* 可选：为整个网格添加一些内边距 */
        .list-item {
            margin-bottom: 20px;
            border-radius: 10px;
            border: 1px solid #A9CBFF;

            width: 100%; // 确保填满网格列
            max-width: 100%; // 防止内容溢出
            box-sizing: border-box; // 包含padding和border

            // 确保按钮在最底部
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            background-color: #FFFFFF;

            .list-item-box {
                padding: 30px 20px;

                .box-top {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;

                    .box-top-title {
                        font-weight: 600;
                        font-size: 17px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }

                    .box-top-status {
                        flex-shrink: 0;
                        font-size: 12px;

                        img {
                            height: 13px;
                            vertical-align: middle;
                        }

                        .status-over {
                            padding: 4px 7px;
                            background-color: #EEEEEE;
                            border-radius: 15px;
                            color: #999999;
                        }

                        .status-going {
                            color: #247CFF;
                        }

                        .status-nostart {
                            color: #D9B600;
                        }
                    }
                }

                .box-describe {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    color: #999999;
                    font-weight: 400;
                    font-size: 14px;
                    margin-bottom: 15px;
                }

                .box-info {
                    font-weight: 600;
                    font-size: 15px;
                    display: flex;
                    align-items: center;
                    margin-bottom: 20px;

                    span {
                        display: block;
                        max-width: 100%;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }

                    img {
                        height: 16px;
                        margin-right: 5px;
                    }
                }

                .box-score {
                    .box-score-item {
                        display: flex;
                        align-items: center;
                        margin-top: 20px;

                        img {
                            height: 10px;
                            margin-right: 5px;
                        }
                    }
                }
            }

            .list-item-btn {
                display: flex;
                justify-content: space-around;
                background: #E8F2FF;
                border-bottom-left-radius: 10px;
                border-bottom-right-radius: 10px;
                padding: 20px 0;

                img {
                    height: 16px;
                    margin-right: 6px;
                }

                .option-btn {
                    display: flex;
                    align-items: center;
                    color: #247CFF;
                    cursor: pointer;
                }
            }
        }
    }

}

/* 手机设备通用样式 */
@media screen and (max-width: 992px) {
    .app-container {
        .search-area {
            flex-direction: column;
            margin: 15px 0;

            .add-button {
                margin: 10px 0 0 0;
                align-self: flex-end;
                padding-right: 10px;

                .el-button {
                    font-size: 12px;
                    padding: 7px 12px;
                }
            }
        }

        .head-input {
            margin: 0 auto;

            ::v-deep {
                .el-input__inner {
                    height: 45px;
                    line-height: 45px;
                    font-size: 14px;
                }

                .line {
                    height: 25px;
                }

                .el-input-group__prepend {
                    padding: 0 0 0 10px;
                    font-size: 14px;
                }

                .el-button {
                    font-size: 14px;
                }
            }
        }

        .list-box {
            gap: 10px;
            padding: 5px;

            .list-item {
                margin-bottom: 10px;

                .list-item-box {
                    padding: 15px 10px;

                    .box-top {
                        margin-bottom: 5px;

                        .box-top-title {
                            font-size: 14px;
                        }

                        .box-top-status {
                            font-size: 10px;

                            img {
                                height: 10px;
                            }

                            .status-over {
                                padding: 2px 5px;
                            }
                        }
                    }

                    .box-describe {
                        font-size: 12px;
                        margin-bottom: 10px;
                    }

                    .box-info {
                        font-size: 13px;
                        margin-bottom: 10px;

                        img {
                            height: 13px;
                        }
                    }

                    .box-score {
                        .box-score-item {
                            margin-top: 10px;
                            font-size: 12px;

                            img {
                                height: 8px;
                            }
                        }
                    }
                }

                .list-item-btn {
                    padding: 10px 0;
                    font-size: 12px;

                    img {
                        height: 13px;
                        margin-right: 3px;
                    }
                }
            }
        }

        /* 调整分页组件 */
        ::v-deep .el-pagination {
            padding: 10px 5px;
            font-size: 12px;

            .btn-prev, .btn-next {
                padding: 0 5px;
            }

            .el-pager li {
                min-width: 25px;
            }
        }
    }
}

/* 手机横屏模式 - 2列布局 */
@media screen and (max-width: 992px) and (orientation: landscape) {
    .app-container {
        .head-input {
            width: 80vw;
        }

        .list-box {
            grid-template-columns: repeat(2, minmax(0, 1fr));
        }
    }
}

/* 手机竖屏模式 - 1列布局 */
@media screen and (max-width: 992px) and (orientation: portrait) {
    .app-container {
        .head-input {
            width: calc(100vw - 60px);
        }

        .list-box {
            grid-template-columns: repeat(1, minmax(0, 1fr));

            .list-item {
                .list-item-box {
                    .box-top {
                        .box-top-title {
                            max-width: 70%;
                        }
                    }
                }
            }
        }
        /* 调整分页组件 */
        ::v-deep .el-pagination {
            .el-pagination__jump{
                display: none;
            }
        }
    }
}
</style>