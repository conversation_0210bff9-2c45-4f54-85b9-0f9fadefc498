import request from '@/utils/request'

// 部门设置短信模板列表
export function templateListApi(query) {
  return request({
    url: '/message/list',
    method: 'get',
    params: query
  })
}

// 获取部门模板内容
export function getDeptMessageTemplateApi(query) {
  return request({
    url: '/message/getDeptMessageTemplate',
    method: 'get',
    params: query
  })
}

//更新短信模板-启用停用状态
export function updataTemplateApi(data) {
  return request({
    url: '/message',
    method: 'put',
    data
  })
}
