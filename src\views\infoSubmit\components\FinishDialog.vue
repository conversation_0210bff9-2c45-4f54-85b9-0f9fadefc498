<template>
  <div>
    <!-- 完结 -->
    <el-dialog title="完结" width="30%" :visible.sync="dialogFinishVisible" :before-close="cancelDialog"
               :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form :model="form" ref="dynamicValidateForm" label-width="60px" :rules="rules">
        <div class="finish">确定完结该任务吗? 完结后，不可撤销。</div>
        <el-form-item label="评论：" prop="comment">
          <el-input contenteditable=true type="textarea" placeholder="请输入200字符以内的评论"
                    v-model.trim="form.comment"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitForm('dynamicValidateForm')">确 定</el-button>
        <el-button @click="cancelDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {approvalSubmit} from '@/api/infoSubmit/index'
import {decryptByAES, encryptByAES} from '@/utils/jsencrypt'

export default {
  name: 'FinishDialog',
  props: {
    dialogFinishVisible: {
      type: Boolean,
      default: false,
    },
    infoId: {
      type: String,
      default: '',
    },
    processResult: {
      type: String,
      default: '8',
    }
  },
  data() {
    return {
      rules: {
        comment: [
          {min: 0, max: 200, message: '长度在200个字符以内', trigger: 'blur'}
        ],
      },
      form: {
        comment: ''
      },
      loading: false
    }
  },
  methods: {
    cancelDialog() {
      this.$emit('cancelFinish')
    },
    resetParams() {
      this.form = {
        comment: ''
      }
      this.$nextTick(() => {
        this.$refs['dynamicValidateForm'].resetFields()
      })
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          try {
            let params = JSON.parse(JSON.stringify(this.form))
            this.loading = true
            let allParams = {}
            if (this.$takeAES) {
              allParams = {
                encryptJson: encryptByAES(JSON.stringify({
                  ...params,
                  processResult: this.processResult,
                  infoId: this.infoId
                }))
              }
            } else {
              allParams = {...params, processResult: this.processResult, infoId: this.infoId}
            }
            let res = await approvalSubmit(allParams)
            this.$message.success(res.msg)
            this.cancelDialog()
            this.$emit('success')
          } finally {
            this.loading = false
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.finish {
  font-size: 14px;
  line-height: 28px;
  margin-left: 5px;
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: center;
}
</style>
