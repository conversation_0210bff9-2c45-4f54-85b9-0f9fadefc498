@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

@font-face {
  font-family: sourcehan;
  src: url('./SourceHanSansCN-Regular.ttf')
}

body {
  font-size: 14px;
  height: 100%;
  background: #fff;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: sourcehan;
  // font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  font-size: 16px;
  height: 100%;
  box-sizing: border-box;
}

#app {
  font-size: 14px;
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  // font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}


.topbar-container {
  ::-webkit-scrollbar-track-piece {
    background-color: transparent;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  ::-webkit-scrollbar-thumb {
    display: none;

    &:hover {
      width: 0px;
      height: 0px;
      background-color: transparent;
    }
  }

  overflow-x: scroll;
  overflow-y: hidden;
  scrollbar-width: none; /* 尝试隐藏滚动条，目前仅在一些浏览器中有效 */
  -ms-overflow-style: none; /* 针对IE和Edge的旧版本 */
  // width: calc(100% - 450px);
  flex: 1;
  -ms-flex: 1; /* 添加此行以支持 IE 10+ */
  height: 80px;
  background: linear-gradient(180deg, #3485FF 0%, #0066FF 100%);

  li.is-active {
    border-bottom: 4px solid #fff !important;
    opacity: 1;
  }

  .el-menu-item,
  .el-submenu__title {
    height: 80px !important;
    line-height: 80px !important;
    margin-right: 20px;
    opacity: 0.9;
  }

  .el-submenu {
    .el-submenu__title {
      height: 76px !important;
    }
  }

  .svg-icon {
    margin-right: 4px;
    // margin-left: 20px;
  }

  .el-submenu__icon-arrow {
    right: -14px;
    top: 55%;
  }
}

.top-menu {
  .svg-icon {
    margin-right: 0.2rem;
  }
}

/* 手机横屏模式全局优化 */
@media screen and (max-width: 992px) and (orientation: landscape) {
  /* 调整全局字体大小 */
  body, #app {
    font-size: 12px;
  }

  /* 调整顶部菜单栏 */
  .topbar-container {
    height: 40px;

    .el-menu-item,
    .el-submenu__title {
      height: 40px !important;
      line-height: 40px !important;
      margin-right: 10px;
      font-size: 12px;
    }

    .el-submenu {
      .el-submenu__title {
        height: 40px !important;
      }
    }

    .el-submenu__icon-arrow {
      top: 50%;
    }
  }

  /* 调整内容区域的间距 */
  .app-container {
    padding: 10px;
  }

  .components-container {
    margin: 15px 25px;
  }

  .pagination-container {
    margin-top: 15px;
  }

  /* 调整表单元素大小 */
  .el-form-item {
    margin-bottom: 12px;

    .el-form-item__label {
      line-height: 30px;
      padding: 0 8px 0 0;
    }

    .el-form-item__content {
      line-height: 30px;
    }
  }

  .el-input__inner {
    height: 30px;
    line-height: 30px;
  }

  .el-button {
    padding: 7px 14px;
    font-size: 12px;
  }

  /* 调整表格样式 */
  .el-table {
    font-size: 12px;

    th {
      padding: 5px 0;
    }

    td {
      padding: 5px 0;
    }
  }
}
