<template>
    <div class="dataAcq">
        <div class="dataAcqBody">
            <!-- 标题 -->
            <div class="dataHead">
                <img src="@/assets/images/dataAcqFont.png" alt="">
            </div>
            <div class="dataCot">
                <!-- 翻页 -->
                <div class="hometablist">
                    <span class="tab-prev" @click="goPage('prev')">上一页</span>
                    <!-- <span class="tab-next" @click="goPage('next')">下一页</span> -->
                    <div class="date">{{currentTime}}</div>
                </div>
                <div class="dataContent">
                    <div class="dataOneFloor">
                        <!-- 采集情况 -->
                        <div class="dataCollectCot  addDataOneFloor">
                            <h2 class="dataCollH2">
                                <span>采集情况</span>
                                <div class="spanImg">
                                    <img src="@/assets/images/dataLi.png" alt="">
                                </div>
                            </h2>
                            <div class="dataCollection">
                                <ul>
                                    <li v-for="(item,index) in situationList" :key="index">
                                        <img v-if="item.name=='微信'" style="height:16px" src="@/assets/images/weixin.png" alt="">
                                        <img v-if="item.name=='微博'" src="@/assets/images/wb.png" alt="">
                                        <img v-if="item.name=='图片'" style="height:16px"  src="@/assets/images/picture.png" alt="">
                                        <img v-if="item.name=='论坛社区'" src="@/assets/images/forum.png" alt="">
                                        <img v-if="item.name=='广播'" src="@/assets/images/broadcast.png" alt="">
                                        <img v-if="item.name=='电子报刊'" src="@/assets/images/report.png" alt="">
                                        <img v-if="item.name=='新闻'" src="@/assets/images/news.png" alt="">
                                        <img v-if="item.name=='视频'" src="@/assets/images/video.png" alt="">
                                        <img v-if="item.name=='客户端'" src="@/assets/images/client.png" alt="">
                                        <img v-if="item.name=='电视'" src="@/assets/images/tv.png" alt="">
                                        <img v-if="item.name=='专利'" src="@/assets/images/patent.png" alt="">
                                        <img v-if="item.name=='境外'" src="@/assets/images/abroad.png" alt="">
                                        <img v-if="item.name=='政务'" src="@/assets/images/abroad.png" alt="">
                                        <p>
                                            <span>{{item.name}}</span>
                                            <em>{{item.value}}</em>
                                        </p>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <!-- 地图分布 -->
                        <div class="bigMap  addBigMap">
                            <div class="dataMap  addDataMap">
                                <div class="dataMapHead  addDataMapHead">
                                    <Flipper ref="flipperRef"></Flipper>
                                </div>
                                <!-- 地图 -->
                                <div class="dataWordMap" ref="dataWordMap" style="width: 100%; -webkit-tap-highlight-color: transparent; user-select: none; position: relative;" id="dataWordMap" _echarts_instance_="ec_1732243768022"><div style="position: relative; width: 1372px; height: 177px; padding: 0px; margin: 0px; border-width: 0px; cursor: default;"><canvas data-zr-dom-id="zr_0" width="2744" height="354" style="position: absolute; left: 0px; top: 0px; width: 1372px; height: 177px; user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 0px; margin: 0px; border-width: 0px;"></canvas><canvas data-zr-dom-id="zr_1" width="2744" height="354" style="position: absolute; left: 0px; top: 0px; width: 1372px; height: 177px; user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 0px; margin: 0px; border-width: 0px;"></canvas><canvas data-zr-dom-id="zr_2" width="2744" height="354" style="position: absolute; left: 0px; top: 0px; width: 1372px; height: 177px; user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 0px; margin: 0px; border-width: 0px;"></canvas><canvas data-zr-dom-id="zr_3" width="2744" height="354" style="position: absolute; left: 0px; top: 0px; width: 1372px; height: 177px; user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 0px; margin: 0px; border-width: 0px;"></canvas><div style="position: absolute !important; visibility: hidden !important; padding: 0px !important; margin: 0px !important; border-width: 0px !important; user-select: none !important; width: 0px !important; height: 0px !important; inset: 0px auto auto 0px !important;"></div><div style="position: absolute !important; visibility: hidden !important; padding: 0px !important; margin: 0px !important; border-width: 0px !important; user-select: none !important; width: 0px !important; height: 0px !important; inset: 0px 0px auto auto !important;"></div><div style="position: absolute !important; visibility: hidden !important; padding: 0px !important; margin: 0px !important; border-width: 0px !important; user-select: none !important; width: 0px !important; height: 0px !important; inset: auto auto 0px 0px !important;"></div><div style="position: absolute !important; visibility: hidden !important; padding: 0px !important; margin: 0px !important; border-width: 0px !important; user-select: none !important; width: 0px !important; height: 0px !important; inset: auto 0px 0px auto !important;"></div></div><div style="position: absolute; display: none; border-style: solid; white-space: nowrap; background-color: rgb(21, 64, 161); border-width: 0px; border-color: rgb(255, 255, 204); border-radius: 4px; color: rgb(255, 255, 255); font: 14px / 21px sans-serif; padding: 5px; left: 197px; top: 100.844px; z-index: 100; pointer-events: auto;"><span style="color:#fff;">Yemen</span></div></div>
                            </div>
                        </div>
                        <!-- 实时采集数据 -->
                        <div class="dataCollectCot">
                            <h2 class="dataCollH2">
                                <span>实时采集数据</span>
                                <div class="spanImg">
                                    <img src="@/assets/images/dataLi.png" alt="">
                                </div>
                            </h2>
                            <div v-loading="keyLoading" @click="viewKey($event)" element-loading-background="rgba(18, 42, 96,0)" style="height:100%;">
                            <vue-seamless-scroll 
                                :step="0.1"
                                hover="true"
                                :data="warnList" 
                                :class-option="defaultOption(warnList)"
                                class="warnless-warp">
                            <div class="dataNewTime" id="newTimeCot">
                                <ul id="newTimeOne" v-for="(item,index) in warnList" :key="index">
                                    <li>
                                        <p class="keyItem" :data="JSON.stringify(item)"><a href="javascript:void(0)">{{ item.title }}</a></p>
                                        <dl>
                                            <dt>{{item.hostName||item.host}}</dt>
                                            <dd>
                                                <img src="@/assets/images/time.png" alt="">
                                                {{item.time}}
                                            </dd>
                                        </dl>
                                    </li>
                                </ul>
                            </div>
                        </vue-seamless-scroll>
                    </div>
                        </div>
                    </div>
                    <div class="dataOneFloor">
                        <!-- 24小时数据趋势 -->
                        <div class="dataOneDayTrend">
                            <h2 class="dataCollH2">
                                <span>24小时数据趋势</span>
                                <div style="width: 75%">
                                    <img src="@/assets/images/dataLongLi.png" alt="">
                                </div>
                            </h2>
                            <div class="dataOneDataChart">
                                <lineChart style="width:100%;height:100%" :data="infoLineData" :legendData="legendData"
                                :toolName="'信息来源走势'" :showLoading="infoLineLoading" :isToImg="'line1'"/>
                            </div>
                        </div>
                        <!-- 数据来源分布 -->
                        <div class="dataFrom">
                            <h2 class="dataCollH2">
                                <span>数据来源分布</span>
                                <div class="spanImg">
                                    <img src="@/assets/images/dataLi.png" alt="">
                                </div>
                            </h2>

                            <div class="dataOneDataChart">
                                <pieChart style="width:100%;height:100%" :show-loading="sourceLoading"
                                :toolName="'信息来源占比'" :data="sourceData" :radius="['30%', '50%']" :color="colorList" 
                                    :isToImg="'pie2'" :isDown="true"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import * as echarts from 'echarts';  
import lineChart from './components/lineChart.vue'
import pieChart from './components/pieChart.vue'
import Flipper from './Flipper.vue'
import { MapChart } from 'echarts/charts';
import { mapZHName } from '@/utils/index';
import worldJSON from "./world.json"
import {getGatherCase,getRealtimeInfo,getTodayTrend,getDataDistribute} from "@/api/screen/index"
  
import dayjs from "dayjs";
echarts.use([MapChart]);
echarts.registerMap('world', worldJSON); 
export default {
    components:{Flipper,lineChart,pieChart},
    data() { 
        const now = dayjs(); // 获取当前时间  
        const weekdays = ['日', '一', '二', '三', '四', '五', '六']; // 映射星期几的数字到名称  
        const weekdayIndex = now.day(); // 获取星期几的数字（0-6）  
        const weekdayName = weekdays[weekdayIndex]; // 获取星期几的名称  
        return{
            currentTime: `${now.format("YYYY/MM/DD HH:mm:ss")}   星期${weekdayName}`,  
            dataWordMap: undefined,
            situationList:[],
            allcountData:101845716,
            warnList:[],
            keyLoading:false,
            infoLineData: {},
            legendData: [],
            infoLineLoading:false,
            sourceLoading: false,
            sourceData: {},
            colorList:["#3953C4", "#228BD7", "#8440B8", "#8383D7", "#3BE390", "#63DEC0", "#FFCF5B", "#FFA94F", "#FF5F5C"],
            interval:null
        }
    },
    computed:{
      
    defaultOption() {
      return (data) => {
          return {
              step: 0.3, // 数值越大速度滚动越快
              limitMoveNum: 8, // 开始无缝滚动的数据量 this.dataList.length
              hoverStop: true, // 是否开启鼠标悬停stop
              direction: 1, // 0向下 1向上 2向左 3向右
              openWatch: true, // 开启数据实时监控刷新dom
              singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
              singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
              waitTime: 0 // 单步运动停止的时间(默认值1000ms)
          }
      }
    } 
    },
    created(){
        this.querySituation()
        this.queryTimeInfo()
        // this.querySoreceDistribute()
        this.queryOneDay()
        this.interval = setInterval(this.querySituation, 60000*10); // 10分钟
    },
    mounted() {
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];  
        // 在组件挂载后设置计时器  
        this.timer = setInterval(() => {  
        const now = dayjs();  
        const weekdayIndex = now.day();  
        const weekdayName = weekdays[weekdayIndex];  
        this.currentTime = `${dayjs().format("YYYY/MM/DD HH:mm:ss")}   星期${weekdayName}`;  
        }, 1000); 
        setTimeout(() => {
            this.map()
            // this.getCountData()
        }, 100);
    },
    beforeDestroy() {  
        // 在组件销毁前清除计时器和移除事件监听器
        clearInterval(this.timer);  
        clearInterval(this.interval);  
    },
    methods: {
        goPage(val) {
            if (val == 'next') {
                this.$router.go(-1)
            } else {
                this.$router.push({path:'/homeScreen/screen'})
            }
            
        },
        baseData(type) {
            var map = {
                "客户端": 2939901,
                "微信": 3603589,
                "视频": 44012059,
                "论坛社区": 1201013,
                "微博": 50012056,
                "新闻": 902139,
                "政务":62050,
                "电子报刊": 18687
                
            }
            var baseData = map[type];
            if (baseData == undefined) {
                baseData = 0;
            }
            return baseData;
        },
        // 数据来源分布
        async querySoreceDistribute() {
            try{
                this.sourceLoading = true
                let res = await getDataDistribute()
                this.sourceData = { data: res.data }
                
            }finally{
                this.sourceLoading = false
            }
        },
        // 实时采集数据
        async queryTimeInfo() {
            let res = await getRealtimeInfo()
            this.warnList = res.data
        },
        // 媒体类型数据-总数据
        async querySituation() {
            let res = await getGatherCase()  
            this.situationList = res.data.res
            this.situationList.map((item)=>{
                item.value = item.value + this.baseData(item.name)
            })
            this.situationList.sort((a, b) => b.value - a.value);
            this.allcountData = res.data.total + 102751494
            this.$refs.flipperRef?.toOrderNum(parseInt(this.allcountData))
            this.sourceData = { data: this.situationList }
        },
        // 24小时数据趋势
        async queryOneDay() {
            try {
                this.infoLineLoading = true
                let res = await getTodayTrend()
                this.timeAxis
                this.infoLineData = res.data
                this.infoLineData.seriesList = res.data.xs
                this.infoLineData.xs = res.data.timeAxis
            } finally {
                this.infoLineLoading = false
            }
        },
        // 实时数据点击
        viewKey(e) {
            const path = e.path || (e.composedPath && e.composedPath());
            let target = path.filter((r) => /keyItem/.test(r.className));
            if (target.length) { 
                target = target[0];
                const data = JSON.parse(target.getAttribute("data")); // 单项数据详情，点击那行数据的所有数据
                // 跳转详情页面
                window.open(data.url, '_blank')
            } 
        },
        // 世界地图注册
        map() {
            this.dataWordMap = echarts.init(this.$refs.dataWordMap);
        // 飞线颜色
        var flyLineColor = "#FFD400";
        //线条颜色
        var lineColor = "rgba(31,20,252,1)";
        //高亮地图填充色
        var lightColor = "#394493";
        // 散点图默认颜色
        var ScatterColor = "yellow"
        // 地图默认状态填充色
        var mapAreaColor = "#06265c"

        //字体颜色
        var textColor = "#fff";

        var geoCoordMap = {
            合肥: [117.233674, 31.826972],
            尼日利亚: [-4.388361, 11.186148],
            美国洛杉矶: [-118.24311, 34.052713],
            // 香港邦泰: [114.195466, 22.282751],
            新疆: [87.9236, 43.5883],
            四川: [103.9526, 30.7617],
            云南: [102.9199, 25.4663],
            黑龙江: [127.9688, 45.368],
            广东: [113.2644, 23.1291],
            福建: [119.2962, 26.0753],
        };
        var BJData = [
            [{
                name: "尼日利亚",
                value: 9100
            }, {
                name: "合肥",
                value: 1000
            }],
            [{
                name: "美国洛杉矶",
                value: 2370
            }, {
                name: "合肥"
            }],
            // [{
            //     name: "香港邦泰",
            //     value: 3130
            // }, {
            //     name: "合肥"
            // }],
            [{
                name: "新疆",
                value: 2130
            }, {
                name: "合肥"
            }],
            [{
                name: "四川",
                value: 2130
            }, {
                name: "合肥"
            }],
            [{
                name: "云南",
                value: 1130
            }, {
                name: "合肥"
            }],
            [{
                name: "黑龙江",
                value: 1130
            }, {
                name: "合肥"
            }],
            [{
                name: "广东",
                value: 1130
            }, {
                name: "合肥"
            }],
            [{
                name: "福建",
                value: 1130
            }, {
                name: "合肥"
            }],
        ];
        var convertData = function(data) {
            var res = [];
            for (var i = 0; i < data.length; i++) {
                var dataItem = data[i];
                var fromCoord = geoCoordMap[dataItem[0].name];
                var toCoord = geoCoordMap[dataItem[1].name];
                if (fromCoord && toCoord) {
                    res.push([{
                        coord: fromCoord,
                        value: dataItem[0].value
                    }, {
                        coord: toCoord
                    }]);
                }
            }
            return res;
        };

        var series = [];
        [
            ["合肥", BJData]
        ].forEach(function(item, i) {
            series.push({
                    "type": "lines",
                    "zlevel": 3,
                    "effect": {
                        "show": true,
                        "period": 4,
                        "trailLength": 0.7,
                        "symbol": "arrow",
                        "symbolSize": 3,
                        "color": flyLineColor
                    },
                    "lineStyle": {
                        "normal": {
                            "width": 0,
                            "curveness": 0.2
                        }
                    },
                    "data": convertData(item[1])
                }, {
                    "type": "lines",
                    'animation': false,
                    "zlevel": 2,
                    "symbolSize": 10,
                    "effect": {
                        "show": false,
                        "period": 6,
                        "trailLength": 0
                    },
                    "lineStyle": {
                        "color": lineColor,
                        "width": 1,
                        "opacity": 0.8,
                        "curveness": 0.2
                    },
                    "data": convertData(item[1])
                }, {
                    type: "effectScatter",
                    coordinateSystem: "geo",
                    zlevel: 1,
                    rippleEffect: {
                        //涟漪特效
                        period: 6, //动画时间，值越小速度越快
                        brushType: "stroke", //波纹绘制方式 stroke, fill
                        scale: 2 //波纹圆环最大限制，值越大波纹越大
                    },
                    label: {
                        normal: {
                            show: true,
                            position: "right", //显示位置
                            offset: [8, 0], //偏移设置
                            formatter: "{b}", //圆环显示文字
                            color: textColor,
                            fontSize: 10,
                            lineHeight: 2,
                            borderWidth: 0.5,
                            backgroundColor: "#061a4c",
                            borderColor: "#97b5f0",
                            padding: 1,
                        },
                        emphasis: {
                            show: true
                        }
                    },
                    symbol: "circle",
                    symbolSize: 12,
                    itemStyle: {
                        normal: {
                            show: true,
                            "color": ScatterColor,
                            "shadowBlur": 10,
                            "shadowColor": ScatterColor
                        }
                    },
                    data: item[1].map(function(dataItem) {
                        return {
                            name: dataItem[0].name,
                            value: geoCoordMap[dataItem[0].name].concat([dataItem[0].value])
                        };
                    })
                },
                //被攻击点 大头针
                {
                    type: "scatter",
                    coordinateSystem: "geo",
                    zlevel: 2,
                    rippleEffect: {
                        period: 4,
                        brushType: "stroke",
                        scale: 2
                    },
                    label: {
                        normal: {
                            show: true,
                            position: "right",
                            formatter: "{b}",
                            color: textColor,

                            fontSize: 10,
                            lineHeight: 14,
                            borderWidth: 1,
                            backgroundColor: "#061a4c",
                            borderColor: "#97b5f0",
                            padding: 3,

                        },
                        emphasis: {
                            show: true
                        }
                    },
                    symbol: "pin",
                    symbolSize: 30,
                    itemStyle: {
                        normal: {
                            show: true,
                            color: ScatterColor
                        }
                    },
                    data: [{
                        name: item[0],
                        value: geoCoordMap[item[0]].concat([100])
                    }]
                },
                // 被攻击点 散点
                {
                    type: "effectScatter",
                    coordinateSystem: "geo",
                    zlevel: 2,
                    symbol: "circle",
                    symbolSize: 12,
                    rippleEffect: {
                        //涟漪特效
                        period: 4, //动画时间，值越小速度越快
                        brushType: "fill", //波纹绘制方式 stroke, fill
                        scale: 6 //波纹圆环最大限制，值越大波纹越大
                    },
                    itemStyle: {
                        normal: {
                            show: true,
                            "shadowBlur": 12,
                            "shadowColor": "red",
                            "color": {
                                type: 'radial',
                                x: 0.5,
                                y: 0.5,
                                r: 0.5,
                                colorStops: [{
                                    offset: 0,
                                    color: 'red' // 0% 处的颜色
                                }, {
                                    offset: 0.8,
                                    color: 'transparent'
                                }, {
                                    offset: 1,
                                    color: 'red' // 100% 处的颜色
                                }],
                                global: false // 缺省为 false
                            },
                        }
                    },
                    label: {
                        normal: {
                            show: false
                        },
                    },
                    data: [{
                        name: item[0],
                        value: geoCoordMap[item[0]].concat([100])
                    }]
                },
                // 高亮显示中国
                {
                    type: 'map',
                    roam: false,
                    layoutCenter: ["5%", "100%"], //地图位置
                    layoutSize: "500%",
                    label: {
                        normal: {
                            show: false,
                        },
                        emphasis: {
                            show: false,
                        }
                    },

                    itemStyle: {
                        normal: {
                            areaColor: mapAreaColor,
                            borderColor: '#0D4CAB'
                        },
                        emphasis: {
                            areaColor: lightColor
                        }
                    },
                    map: 'world', //使用
                    // geoIndex: 0,
                    data: [{
                        "selected": true,
                        "name": "China"
                    }]
                },
            );
        });

        let option = {
            backgroundColor: '',
            title: {
                text: '',
                left: 'center',
                textStyle: {
                    color: '#fff'
                }
            },
            tooltip: {
                trigger: "item",
                backgroundColor: "#1540a1",
                borderColor: "#FFFFCC",
                showDelay: 0,
                hideDelay: 0,
                enterable: true,
                transitionDuration: 0,
                extraCssText: "z-index:100",
                formatter: function(params, ticket, callback) {
                    //根据业务自己拓展要显示的内容
                    var res = "";
                    var name = params.name;
                    var value = params.value[params.seriesIndex];
                    if (value) {
                        res =
                            "<span style='color:#fff;'>" +
                            name +
                            "</span><br/>数据：" +
                            value;
                    } else {
                        res =
                            "<span style='color:#fff;'>" +
                            name;
                    }
                    return res;
                }
            },
            // visualMap: {
            //     //图例值控制
            //     min: 0,
            //     max: 10000,
            //     // show: false,
            //     calculable: true,
            //     // color: ["#0bc7f3"],
            //     textStyle: {
            //         color: "#fff"
            //     },

            // },
            geo: {
                show: true,
                map: "world",
                label: {
                    emphasis: {
                        show: false
                    }
                },
                roam: false, //是否允许缩放
                layoutCenter: ["5%", "100%"], //地图位置
                layoutSize: "500%",
                itemStyle: {
                    normal: {
                        areaColor: mapAreaColor,
                        borderColor: '#8F98A6'
                    },
                    emphasis: {
                        areaColor: '#2a333d'
                    }
                }
            },

            series: series
        };
        this.dataWordMap.setOption(option);
        window.addEventListener("resize", () => {
            if (this.dataWordMap) this.dataWordMap.resize();
        });

    }
    }
}
</script>
<style scoped lang="scss">
.warnless-warp{
    overflow: hidden;
    color: #fff;
    padding: 1% 0;
    height: 100%;

    .item {
        height: 100%;

        .title {
            padding: 6px;
            display: inline-block;
        }
    }
}
    .dataMapHead dl {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        height: 90px;
        overflow: hidden;
        margin-top: 20px;
    }

    .spanImg {
        width: 70%;
    }
    .dataAcq {
        width: 100%;
        height: 100%;
        background: #040715;
        overflow: hidden;
    }

    .dataAcqBody {
        width: 100%;
        height: 100vh;
        overflow: hidden;
    }

    .dataHead {
        height: 8vh;
        line-height: 8vh;
        width: 100%;
        overflow: hidden;
        text-align: center;
        background: url("../../assets/images/headBg.png") no-repeat center center;
        background-size: 100% 100%;
    }

    .dataHead img {
        vertical-align: middle;
    }

    .hometablist {
        display: flex;
        flex-direction: row;
        position: absolute;
        top: 0.65rem;
        right: 0.25rem;
        color: #fff;
        font-size: 0.16rem;
        z-index: 110000;
        span{
            cursor: pointer;
        }
        .date{
            margin-left: 0.3rem;
            margin-right: 0.2rem;
        }
    }

    .jumpUL {
        /*float: left;*/
        display: flex;
        flex-direction: row;
        justify-content: start;
        height: 4vh;
        line-height: 4vh;
    }

    .jumpUL li {
        margin-right: 10px;
        color: #a4a5a7;
        font-size: 1rem;
        padding: 0 10px;
        cursor: pointer;
    }

    .jumpUL li.cur {
        color: #fff;
        background: url("../../assets/images/fontBg.png") no-repeat center center;
        background-size: 120% 130%;
    }

    .dataCot {
        width: 100%;
        overflow: hidden;
        padding: 0px 34px;
        box-sizing: border-box;
    }

    .jumpDl {
        height: 4vh;
        line-height: 4vh;
        display: flex;
        flex-direction: row;
        justify-content: right;
        float: right;
        color: #ffff;
        font-size: 0.9rem;
    }

    .jumpDl dt img {
        vertical-align: middle;
        cursor: pointer;
    }

    .jumpDl dt {
        margin-right: 10px;
        cursor: pointer;
    }

    #dataTime {
        cursor: unset;
    }

    .dataContent {
        width: 100%;
        overflow: hidden;
        margin-top: 2vh;
    }

    .dataOneFloor {
        width: 100%;
        margin-bottom: 1.5vh;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        position: relative;
    }

    .addDataOneFloor {
        position: absolute;
        left: 0;
        z-index: 10;
    }

    .dataCollH2 {
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        height: 3vh;
        line-height: 3vh;
        margin: 1vh 0;
    }

    .dataCollH2 span {
        display: inline-block;
        font-size: 0.2rem;
        background-image: -webkit-linear-gradient(bottom, #3b82f8, #5bc0e1);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: normal;
    }

    .dataCollH2 img {
        display: inline-block;
        vertical-align: middle;
    }

    .dataCollectCot {
        width: 25%;
        position: absolute;
        right: 0;
        /* background: #fff; */
    }

    .dataCollection {
        width: 100%;
        height: 51vh;
        overflow-y: scroll;
        background: url("../../assets/images/collectionBg.png") no-repeat center center;
        background-size: 100% 100%;
        padding: 10px 38px 0 38px;
        box-sizing: border-box;
        &::-webkit-scrollbar{
            display:none;
        }
    }

    .dataCollection ul {
        margin:0;
        padding: 0;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        list-style: none;
        /* overflow: hidden; */
    }

    .dataCollection ul li {
        list-style: none;
        /*height: 49px;
        line-height: 70px;*/
        color: #fff;
        font-size: 0.2rem;
        height: 48px;
        line-height: 48px;
    }

    .dataCollection ul li img {
        display: inline-block;
        float: left;
        vertical-align: middle;
        fill: #fff;
        width: 18px;
        height: 18px;
        margin-right: 10px;
        margin-top: 16px;
    }

    .dataCollection ul li svg g polyline {
        stroke: #fff;
    }

    .dataCollection ul li p {
        margin:0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-bottom: dotted 1px #2f333f;
        font-weight: bold;
    }

    .dataCollection ul li p em {
        display: inline-block;
        color: #2bb4d7;
        background: url("../../assets/images/fontBg.png") no-repeat center center;
        background-size: 110% 110%;
        padding: 0 10px;
        font-size: 25px;
        font-style:normal;
    }

    .bigMap {
        width: 44%;
        overflow: hidden;
        position: relative;
    }

    .addBigMap {
        width: 100%;
    }

    .dataMap {
        width: 100%;
        overflow: hidden;
        height: 56vh;
    }

    .addDataMap {
        position: relative;
    }

    .dataMapHead {
        width: 100%;
        overflow: hidden;
    }

    .addDataMapHead {
        width: 46%;
        overflow: hidden;
        margin-left: 27%;
        position: absolute;
        z-index: 10;
    }

    .dataMapHead dl {
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        justify-content: center;
        height: 90px;
        overflow: hidden;
    }

    .dataMapHead dl dt {
        width: 62px;
        height: 74px;
        margin: 0 4px;
        font-size: 0.6rem;
        color: #4aa3f1;
        text-align: center;
        line-height: 74px;
        background: url("../../assets/images/dataBorder.png") no-repeat center center;
        background-size: 100% 100%;
    }

    .dataMapHead dl dd {
        text-align: center;
        width: 32px;
        font-size: 3rem;
        color: #4aa3f1;
        line-height: 115px;
    }

    .dataNewTime {
        width: 100%;
        height: 80%;
        overflow: hidden;
        background: url("../../assets/images/collectionBg.png") no-repeat center center;
        background-size: 100% 100%;
        padding: 10px 38px;
        box-sizing: border-box;
    }

    .dataNewTime ul {
        width: 100%;
        overflow: hidden;
        padding: 0;
    }

    .dataNewTime ul li {
        width: 100%;
        overflow: hidden;
        border-bottom: dotted 1px #2f333f;
        height: 70px;
        padding: 6px 0px;
        box-sizing: border-box;
        cursor: pointer;
    }

    .dataNewTime p {
        margin: 0;
        width: 100%;
        overflow: hidden;
        height: 30px;
        line-height: 30px;
        color: #fff;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: bold;
    }

    .dataNewTime dl {
        margin: 0;
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        height: 28px;
        line-height: 28px;
        font-size: 0.14rem;
        font-weight: bold;
    }

    .dataNewTime dl dt {
        color: #4dadef;
    }

    .dataNewTime dl dd img {
        width: 14px;
        height: 14px;
        display: inline-block;
        vertical-align: text-top;
        margin-right: 6px;
    }

    .dataNewTime dl dd {
        display: flex;
        align-items: center;
        color: #fff;
    }

    .dataWordMap {
        width: 100%;
        overflow: hidden;
        height: 55.9vh;
    }

    .dataOneDayTrend {
        width: 74%;
    }

    .dataOneDataChart {
        width: 100%;
        height: 27vh;
        overflow: hidden;
        background: url("../../assets/images/collectionBg.png") no-repeat center center;
        background-size: 100% 100%;
        padding: 5px 10px;
        box-sizing: border-box;
    }

    .dataOneDataChart1 {
        width: 100%;
        height: 23vh;
        overflow: hidden;
        background: url("../../assets/images/collectionBg.png") no-repeat center center;
        background-size: 100% 100%;
        padding: 10px 38px;
        box-sizing: border-box;
    }

    .dataFrom {
        width: 25%;
    }

    .dataTotalOfPub {
        width: 100%;
        height: 16vh;
        padding: 0 1vh;
        box-sizing: border-box;
    }
    .adddataTotalOfPub{
        padding-top: 4vh;
    }

    .dataOneFloorLeft {
        width: 25%;
    }

    .dataCollectCotTop {
        width: 100%;
    }

    .dataEmotionEchart {
        width: 100%;
        height: 30vh;
        overflow: hidden;
    }

    .dataRank {
        width: 170px;
        height: auto;
        overflow: hidden;
        padding: 6px 10px;
        box-sizing: border-box;
        position: absolute;
        right: 10px;
        bottom: 0;
        border: solid 1px rgb(0, 121, 255, 0.3);
    }

    .dataRank h2 {
        width: 100%;
        overflow: hidden;
        color: #fff;
        height: 28px;
        line-height: 28px;
        font-weight: normal;
    }

    .dataRank h2 img {
        width: 20px;
        vertical-align: middle;
    }

    .dataRank ul {
        width: 100%;
        overflow: hidden;
        max-height: 50vh;
    }

    .dataRank ul li {
        width: 100%;
        overflow: hidden;
        height: 26px;
        line-height: 26px;
        color: #fff;
        font-size: 12px;
        background: rgb(7, 90, 188, 0.3);
        margin: 3px 0px;
        cursor: pointer;
        padding: 0 6px;
        box-sizing: border-box;
    }

    .dataRank ul li em {
        display: inline-block;
        float: left;
        margin-right: 8px;
    }

    .dataRank ul li span {
        display: inline-block;
        float: right;
    }

</style>