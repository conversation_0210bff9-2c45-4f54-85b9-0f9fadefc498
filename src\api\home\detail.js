import request from '@/utils/request'

// 事件
export function getPlanType(planType) {
  return request({
    url: `/home/<USER>
    method: 'get',
  })
}

// 查询
export function searchHome(data) {
  return request({
    url: '/search/home/<USER>',
    method: 'post',
    data
  })
}

// 实时数据刷新
export function homeDownCount(data) {
  return request({
    url: `/search/home/<USER>/count`,
    method: 'post',
    data
  })
}

// 搜索导出
export function homeDownExport(data) {
  return request({
    url: `/search/home/<USER>/export`,
    method: 'post',
    data
  })
}
