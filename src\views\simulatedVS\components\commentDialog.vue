<template>
    <el-dialog title="评论" :visible.sync="dialogVisible" width="65%" append-to-body @closed="handleClosed">
        <el-form ref="form" :model="localFormData" :rules="rules" label-width="0px">
            <el-form-item prop="content">
                <el-input type="textarea" v-model="localFormData.content" maxlength="10000" show-word-limit
                    placeholder="请输入评论" :autosize="{ minRows: 4, maxRows: 6 }" style="width: 100%" ref="contentInput" />
            </el-form-item>

            <div class="comment-tools">
                <div class="upload-btn">
                  <div class="upload-btn-item" v-popover:emojiPopover @click.stop="toggleEmojiPicker">
                    <img src="@/assets/images/simulatedVS/emoji.png" alt="">表情
                  </div>
                  <div class="upload-btn-item" @click="showImageUpload">
                    <img src="@/assets/images/simulatedVS/pictureIcon.png" alt="">图片
                  </div>
                </div>

                <!-- 表情选择器 -->
                <el-popover
                    ref="emojiPopover"
                    placement="bottom-start"
                    width="360"
                    trigger="manual"
                    popper-class="emoji-popover"
                    v-model="emojiPickerVisible"
                    :visible-arrow="false">
                    <emoji-picker @select="insertEmoji" @click.native.stop></emoji-picker>
                </el-popover>
            </div>

            <el-upload ref="upload" class="upload-file-uploader" :headers="headers" :action="uploadFileUrl"
                :file-list="uploadFileList" :before-upload="handleBeforeUpload" :on-success="handleFileSuccess"
                :on-remove="handleFileRemove" :on-exceed="handleExceed" :show-file-list="true" :accept="acceptList">
                <div class="upload-btn" v-show="imageUploadVisible" @click="setAccept('image')">
                </div>
                <!-- <div class="upload-btn-item" @click="setAccept('video')">
                    <img src="@/assets/images/simulatedVS/videoIcon.png" alt="">视频
                </div> -->
            </el-upload>

            <!-- 显示已上传的图片 -->
            <div class="uploaded-images" v-if="uploadFileList && uploadFileList.length > 0">
                <div v-for="(file, index) in uploadFileList" :key="index" class="image-item">
                    <img v-if="isImageFile(file)" :src="getFileUrl(file)" alt="上传图片" class="preview-image">
                    <div v-else class="file-icon">
                        <i class="el-icon-document"></i>
                        <span>{{ file.name }}</span>
                    </div>
                </div>
            </div>
        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="handleSubmit">确定</el-button>
            <el-button @click="handleCancel">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
import EmojiPicker from "@/components/EmojiPicker";

export default {
    components: {
        EmojiPicker
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        formData: {
            type: Object,
            default: () => ({})
        },
        uploadFileList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            rules: {
                content: [
                    { required: true, message: '请输入内容', trigger: 'change' }
                ]
            },
            headers: { Authorization: "Bearer " + getToken() },
            uploadFileUrl: process.env.VUE_APP_BASE_API + "/file/uploadFile",
            acceptList: '',
            imageTypes: ['png', 'jpeg', 'jpg'],
            videoTypes: ['mp4', 'avi', 'rmvb', 'mov', 'mkv'],
            imageLimit: 5,
            videoLimit: 1,
            emojiPickerVisible: false,
            imageUploadVisible: false,
            cursorPosition: 0,
            localFormData: { content: '' } // 本地表单数据
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        }
    },

    watch: {
        // 监听 formData 变化，更新本地表单数据
        formData: {
            handler(newVal) {
                if (newVal && newVal.content !== undefined) {
                    this.localFormData.content = newVal.content;
                }
            },
            immediate: true,
            deep: true
        },
        // 监听对话框可见性变化
        visible: {
            handler(newVal) {
                if (newVal) {
                    // 当对话框打开时，重置本地表单数据
                    this.localFormData.content = this.formData.content || '';
                }
            },
            immediate: true
        }
    },

    beforeDestroy() {
        // 组件销毁前移除事件监听器
        document.removeEventListener('click', this.closeEmojiPicker);
    },
    methods: {
        toggleEmojiPicker(event) {
            // 防止事件冲突
            if (event) {
                event.stopPropagation();
            }

            // 切换表情选择器的显示状态
            this.emojiPickerVisible = !this.emojiPickerVisible;
            this.imageUploadVisible = false;

            // 保存当前光标位置
            if (this.$refs.contentInput) {
                const textarea = this.$refs.contentInput.$el.querySelector('textarea');
                if (textarea) {
                    this.cursorPosition = textarea.selectionStart;
                }
            }

            // 添加点击外部区域关闭表情选择器
            if (this.emojiPickerVisible) {
                // 使用延时确保当前点击事件不会触发关闭
                setTimeout(() => {
                    document.addEventListener('click', this.closeEmojiPicker);
                }, 0);
            } else {
                document.removeEventListener('click', this.closeEmojiPicker);
            }
        },

        closeEmojiPicker(event) {
            // 获取表情选择器元素
            const emojiPopover = document.querySelector('.emoji-popover');

            // 如果点击的是表情选择器内部，不关闭
            if (emojiPopover && !emojiPopover.contains(event.target)) {
                this.emojiPickerVisible = false;
                document.removeEventListener('click', this.closeEmojiPicker);
            }
        },

        insertEmoji(emoji, event, shouldClose = true) {
            // 阻止事件冲突
            if (event) {
                event.stopPropagation();
            }

            const content = this.localFormData.content || '';
            const position = this.cursorPosition;

            // 在光标位置插入表情
            this.localFormData.content = content.substring(0, position) + emoji + content.substring(position);

            // 更新光标位置
            this.$nextTick(() => {
                if (this.$refs.contentInput) {
                    const textarea = this.$refs.contentInput.$el.querySelector('textarea');
                    if (textarea) {
                        textarea.focus();
                        const newPosition = position + emoji.length;
                        textarea.setSelectionRange(newPosition, newPosition);
                        this.cursorPosition = newPosition;

                        // 默认关闭选择器
                        if (shouldClose) {
                            this.emojiPickerVisible = false;
                            document.removeEventListener('click', this.closeEmojiPicker);
                        }
                    }
                }
            });
        },

        showImageUpload() {
            this.imageUploadVisible = !this.imageUploadVisible;
            this.emojiPickerVisible = false;
            this.setAccept('image');

            // 如果显示上传组件，则触发点击事件
            this.$nextTick(() => {
                if (this.imageUploadVisible && this.$refs.upload) {
                    const uploadBtn = this.$refs.upload.$el.querySelector('.el-upload');
                    if (uploadBtn) {
                        uploadBtn.click();
                    }
                }
            });
        },

        setAccept(type) {
            this.acceptList = type === 'image'
                ? this.imageTypes.map(item => `.${item}`).join(',')
                : this.videoTypes.map(item => `.${item}`).join(',');
        },

        handleBeforeUpload(file) {
            // 保持原有上传逻辑
            const isLt2M = file.size / 1024 / 1024;
            if (isLt2M > 30) {
                this.$message.error('上传文件大小不能超过30MB');
                return false;
            }

            const allowedExtensions = this.acceptList
                .split(',')
                .map(ext => ext.trim().toLowerCase().replace('.', ''))
                .filter(ext => ext);

            const fileExtension = file.name.split('.').pop().toLowerCase();
            if (!allowedExtensions.includes(fileExtension)) {
                this.$message.error(`只允许上传 ${allowedExtensions.join(', ')} 格式的文件`);
                return false;
            }

            const counts = this.countExistingFiles();
            const newFileType = this.getFileType(file);
            if (
                (newFileType === 'image' && counts.image >= this.imageLimit) ||
                (newFileType === 'video' && counts.video >= this.videoLimit)
            ) {
                this.$message.error(`最多上传${newFileType === 'image' ? this.imageLimit : this.videoLimit}个文件`);
                return false;
            }

            return true;
        },

        handleFileSuccess(response, file, fileList) {
            fileList[fileList.length - 1] = { ...file, fileId: response.data[0] }
            this.$emit('update:uploadFileList', fileList);
        },

        handleFileRemove(file, fileList) {
            this.$emit('update:uploadFileList', fileList);
        },

        handleExceed() {
            this.$message.warning('文件数量超出限制');
        },

        getFileType(file) {
            return this.imageTypes.some(ext => file.name.toLowerCase().endsWith(ext)) ? 'image' : 'video';
        },

        countExistingFiles() {
            return this.uploadFileList.reduce((acc, file) => {
                const type = this.getFileType(file);
                acc[type] = (acc[type] || 0) + 1;
                return acc;
            }, { image: 0, video: 0 });
        },

        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    this.$emit('submit', {
                        ...this.formData,
                        content: this.localFormData.content, // 使用本地表单数据
                        file: this.uploadFileList.map(item => item.fileId) || []
                    });
                    this.dialogVisible = false;
                }
            });
        },

        handleCancel() {
            this.dialogVisible = false;
        },

        isImageFile(file) {
            if (!file) return false;
            const fileName = file.name || '';
            return this.imageTypes.some(ext => fileName.toLowerCase().endsWith(ext));
        },

        getFileUrl(file) {
          return process.env.VUE_APP_BASE_API + '/file/public/query/' + file.fileId;
        },

        handleClosed() {
            this.$refs.form.resetFields();
            this.$emit('update:uploadFileList', []);
            this.emojiPickerVisible = false;
            this.imageUploadVisible = false;
            document.removeEventListener('click', this.closeEmojiPicker);

            // 重置本地表单数据
            this.localFormData.content = '';
        }
    }
};
</script>

<style scoped lang="scss">
.comment-tools {
    margin-bottom: 10px;
}

.upload-btn {
    display: flex;
    color: #999999;
    font-size: 16px;

    .upload-btn-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        cursor: pointer;

        img {
            height: 14px;
            margin-right: 5px;
            margin-top: 1px;
        }
    }
}

.upload-file-uploader {
    margin-top: 10px;
}

.uploaded-images {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;

    .image-item {
        position: relative;
        width: 80px;
        height: 80px;
        margin-right: 10px;
        margin-bottom: 10px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        overflow: hidden;

        .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .file-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;

            i {
                font-size: 24px;
                color: #909399;
            }

            span {
                font-size: 12px;
                color: #606266;
                margin-top: 5px;
                width: 100%;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding: 0 5px;
            }
        }
    }
}
</style>

<style>
/* 全局样式，不使用scoped */
.emoji-popover {
    padding: 0 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border: none !important;
    animation: emoji-fade-in 0.2s ease-out;
}

.emoji-popover .el-popover__title {
    margin: 0;
    padding: 10px;
}

.emoji-popover .popper__arrow {
    display: none;
}

@keyframes emoji-fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
