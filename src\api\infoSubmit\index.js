import request from '@/utils/request'

// 信息报送列表
export function getInfoDataApi(data) {
  return request({
    url: '/submit/list',
    method: 'post',
    data: data
  })
}

// 新增报送
export function sureAddSubmitApi(data) {
  return request({
    url: '/submit',
    method: 'post',
    data: data
  })
}

// 查看详情
export function getInfoDetailsApi(id) {
  return request({
    url: '/submit/' + id,
    method: 'get',
    // params: id
  })
}

// 修改报送
export function updateSubmitApi(data) {
  return request({
    url: '/submit',
    method: 'put',
    data: data
  })
}

// 查询部门列表
export function getAreaTreeApi() {
  return request({
    url: '/deptAreaTree/0',
    method: 'get',
    //   params: query
  })
}

// 接收人-下拉列表
export function getReceiver() {
  return request({
    url: '/submit/loadReceiver',
    method: 'get',
  })
}

// 报送接收人员-树
export function getManager() {
  return request({
    url: '/submit/loadManager',
    method: 'get',
  })
}

// 审核接收人员-树
export function getProcessor() {
  return request({
    url: '/submit/loadProcessor',
    method: 'get',
  })
}

// 信息类别列表
export function getInfoTypeList(data) {
  return request({
    url: '/type/typeList',
    method: 'post',
    data: data
  })
}

// 信息列表-新增
export function AddTypeApi(data) {
  return request({
    url: '/type/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 报送流程详情
export function processList(id) {
  return request({
    url: `/submit/process/list?infoId=${id}`,
    method: 'get',
  })
}

// 导出
export function exportList(data) {
  return request({
    url: '/submit/export',
    method: 'post',
    data: data
  })
}

// 链接解析
export function getSolrDataByUrl(data) {
  return request({
    url: '/external/getSolrDataByUrl',
    method: 'post',
    data: data
  })
}

// 审核
export function approvalSubmit(data) {
  return request({
    url: '/submit/approvalSubmit',
    method: 'post',
    data: data
  })
}

// 上报树
export function loadApprover() {
  return request({
    url: '/submit/loadApprover',
    method: 'get'
  })
}

// 下载文件 {id}
export function downloadFileById(id) {
  return request({
    url: `/file/downloadFile/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}
