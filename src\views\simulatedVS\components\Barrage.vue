<template>
    <!-- 弹幕容器 -->
    <div class="barrage-container">
        <!-- 使用transition-group实现弹幕动画 -->
        <transition-group tag="div" name="barrage">
            <!-- 弹幕项循环渲染 -->
            <div v-for="item in visibleList" 
                 :key="item.id" 
                 :style="getBarrageStyle(item)"
                 class="barrage-item">
                {{ item.text }}
            </div>
        </transition-group>
    </div>
</template>

<script>
export default {
    props: {
        // 弹幕移动速度（像素/秒）
        speed: { 
            type: Number, 
            default: 100 
        },
        // 最大显示行数
        maxLine: { 
            type: Number, 
            default: 7 
        },
        // 行高基础值（单位：px）
        lineHeight: {
            type: Number,
            default: 22,
            validator: (value) => value > 0
        },
        // 弹幕间最小水平间距
        minHorizontalGap: {
            type: Number,
            default: 20
        }
    },
    data() {
        return {
            list: [],          // 所有弹幕数据
            containerWidth: 0, // 容器宽度
            containerHeight: 0,// 容器高度
            lineSchedules: [], // 行调度信息（记录每行最后弹幕的结束时间和宽度）
            waitingQueue: [],  // 等待队列（当没有合适行时暂存弹幕）
            animationFrame: null // 动画帧ID
        }
    },
    computed: {
        // 过滤已移除的弹幕
        visibleList() {
            return this.list.filter(item => !item.removed)
        }
    },
    watch: {
        // 当最大行数变化时初始化行调度器
        maxLine: {
            immediate: true,
            handler(val) {
                this.lineSchedules = Array(val).fill({
                    endTime: 0,     // 行最后弹幕的结束时间
                    lastWidth: 0    // 行最后弹幕的宽度
                })
            }
        }
    },
    mounted() {
        this.initContainerSize()
        window.addEventListener('resize', this.initContainerSize)
        this.startAnimationLoop()
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.initContainerSize)
        cancelAnimationFrame(this.animationFrame)
    },
    methods: {
        // 初始化容器尺寸
        initContainerSize() {
            const container = this.$el.parentElement
            this.containerWidth = container.offsetWidth
            this.containerHeight = container.offsetHeight
        },
        
        // 添加新弹幕
        addMessage(data) {
            const textWidth = this.calcTextWidth(data.text)
            // 计算弹幕持续时间：总移动距离/速度
            const duration = (this.containerWidth + textWidth) / this.speed
            const now = Date.now()
            
            // 寻找最佳显示行
            const line = this.findBestLine(now, textWidth, duration)
            
            if (line !== -1) {
                this.createBarrage(data, line, duration, textWidth, now)
            } else {
                // 无合适行则加入等待队列
                this.waitingQueue.push({ data, duration, textWidth })
            }
        },

        // 寻找最佳显示行算法
        findBestLine(now, textWidth, duration) {
            let bestLine = -1
            let minConflict = Infinity
            const requiredSpace = textWidth + this.minHorizontalGap

            // 优先选择空闲行
            for (let i = 0; i < this.maxLine; i++) {
                const schedule = this.lineSchedules[i]
                if (now >= schedule.endTime) {
                    bestLine = i
                    break
                }
            }

            // 如果没有完全空闲行，选择冲突最小的行
            if (bestLine === -1) {
                for (let i = 0; i < this.maxLine; i++) {
                    const schedule = this.lineSchedules[i]
                    const spaceAvailable = (this.containerWidth - schedule.lastWidth) > requiredSpace
                    if (spaceAvailable) {
                        const conflictScore = schedule.endTime - now
                        if (conflictScore < minConflict) {
                            minConflict = conflictScore
                            bestLine = i
                        }
                    }
                }
            }

            return bestLine
        },

        // 创建弹幕实例
        createBarrage(data, line, duration, textWidth, now) {
            const schedule = this.lineSchedules[line]
            // 计算弹幕开始时间（取当前时间和行最后结束时间的较大值）
            const startTime = Math.max(now, schedule.endTime)
            const endTime = startTime + duration * 1000
            
            // 弹幕数据对象
            const item = {
                id: Date.now() + Math.random(), // 唯一ID
                text: data.text,     // 显示文本
                color: data.color,   // 颜色配置
                size: data.size,     // 字体大小
                line,                // 所在行号
                duration,            // 动画持续时间
                textWidth,           // 文本计算宽度
                removed: false,      // 移除标记
                startTime,           // 开始时间戳
                endTime,             // 预计结束时间
                totalDistance: this.containerWidth + textWidth // 总移动距离
            }

            // 更新行调度信息
            this.lineSchedules[line] = {
                endTime,
                lastWidth: textWidth
            }

            this.list.push(item)
        },

        // 启动动画循环
        startAnimationLoop() {
            const animate = () => {
                const now = Date.now()
                // 更新所有弹幕位置
                this.list.forEach(item => {
                    if (now >= item.endTime) {
                        item.removed = true
                    }
                })
                // 处理移除和等待队列
                this.list = this.list.filter(item => !item.removed)
                this.processWaitingQueue()
                this.animationFrame = requestAnimationFrame(animate)
            }
            this.animationFrame = requestAnimationFrame(animate)
        },

        // 处理等待队列
        processWaitingQueue() {
            const queue = [...this.waitingQueue]
            this.waitingQueue = []
            
            queue.forEach(item => {
                const line = this.findBestLine(Date.now(), item.textWidth, item.duration)
                if (line !== -1) {
                    this.createBarrage(item.data, line, item.duration, item.textWidth, Date.now())
                } else {
                    this.waitingQueue.push(item)
                }
            })
        },

        // 生成弹幕样式
        getBarrageStyle(item) {
            const elapsed = Date.now() - item.startTime
            const progress = Math.min(elapsed / (item.duration * 1000), 1)
            const translateX = -(progress * item.totalDistance)
            
            return {
                top: `${item.line * this.lineHeight}px`, // 使用px单位更精确
                color: item.color || '#fff',
                fontSize: `${item.size || 14}px`, // 使用px单位更精确
                transform: `translateX(${translateX}px)`,
                opacity: 1,
                position: 'absolute',
                left: '100%',
                whiteSpace: 'nowrap',
                willChange: 'transform',
                transition: 'none', // 移除transition，使用requestAnimationFrame控制动画
                pointerEvents: 'none' // 防止弹幕干扰点击事件
            }
        },

        // 计算文本实际宽度
        calcTextWidth(text) {
            const span = document.createElement('span')
            span.style.visibility = 'hidden'
            span.style.whiteSpace = 'nowrap'
            span.style.position = 'absolute'
            span.style.fontFamily = 'SourceHanSansCN'
            span.style.fontWeight = '600'
            span.innerText = text
            document.body.appendChild(span)
            const width = span.offsetWidth // 获取渲染后的实际宽度
            document.body.removeChild(span)
            return width
        }
    }
}
</script>

<style scoped>
/* 优化后的样式 */
.barrage-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 9999;
    overflow: hidden;
    contain: strict; /* 性能优化 */
}

.barrage-item {
    position: absolute;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-family: SourceHanSansCN;
    font-weight: 600;
    will-change: transform; /* 性能优化 */
    backface-visibility: hidden; /* 防止闪烁 */
    transform: translateZ(0); /* 硬件加速 */
}

/* 移除过渡动画，改为JS控制 */
.barrage-leave-active {
    display: none;
}
</style>