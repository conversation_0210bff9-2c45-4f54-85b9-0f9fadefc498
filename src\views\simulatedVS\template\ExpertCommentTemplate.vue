<template>
  <div class="templateZJ">
    <div class="title" style="#EB7350">专家点评</div>
    <div class="text">{{ content }}</div>
  </div>
</template>

<script>
export default {
  name: 'ExpertCommentTemplate',
  props: {
    content: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
.templateZJ {
  background-color: #FFFFFF;
  padding: 10em;

  .title {
    font-size: 12em;
    font-weight: 600;
    color: #EB7350;
  }

  .text {
    font-size: 14em;
    // text-indent: 2ch;
    // /* 首行缩进2个字符 */
    // line-height: 1.5em;
    text-align: start;
    word-wrap: break-word;
    white-space: pre-wrap;
    margin-top: 0.5em;
    color: #000;
  }
}
</style>
