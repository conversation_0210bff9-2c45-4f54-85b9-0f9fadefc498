<template>
  <div class="similarArticles">
    <div class="nav">
      <div class="tableItemTitle">
        <div class="tableTitle">
          <img class="tableItemImg" :src="transImage(articleInfo.type,articleInfo.host||'')" alt="无图片"/>
          <el-tooltip placement="top" effect="light" raw-content>
            <div slot="content">
              <div v-html="articleInfo.title"></div>
            </div>
            <div class="tableTitleSpan">
              <span v-html="articleInfo.title"></span>
            </div>
          </el-tooltip>
        </div>
        <div class="tableMain" v-html="articleInfo.text"></div>
        <!-- <el-tooltip placement="top" effect="light" raw-content>
            <div slot="content">
                <div v-html="articleInfo.text"></div>
            </div>
            <div class="tableMain" v-html="articleInfo.text"></div>
        </el-tooltip> -->
      </div>
      <div class="tableRight">
        <div class="tableRightItem">
          <img src="@/assets/images/similarArticles.png" alt="">
          <div class="tableRightItemInfo">
            <div class="infoTitle">相似文章数</div>
            <div class="infoCount" style="color: #6CDBED;">{{total||0}}</div>
          </div>
        </div>
        <div class="tableRightItem">
          <img src="@/assets/images/infoSource.png" alt="">
          <div class="tableRightItemInfo">
            <div class="infoTitle">信息来源</div>
            <div class="infoCount" style="color: #4FC196;">{{articleInfo.hostName}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="search-wrap">
      <div class="title"><span>筛选条件</span></div>
      <el-form :inline="true" :model="queryForm" size="small" class="demo-form-inline">
        <el-form-item label="监测时间:">
          <span style="color:#666">{{startTimeFormatted}} - {{endTimeFormatted}}</span>
        </el-form-item>
        <el-form-item label="信息属性:" label-width="100px">
          <el-select v-model="queryForm.selectedValues" collapse-tags multiple @remove-tag="removeTag"
                     @change="handleSelectChange">
            <el-option label="全部" value="all" @click.native="selectAll"></el-option>
            <el-option v-for="(item,index) in emotionData" :key="index" :label="item.name"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="信息排序:" label-width="100px">
          <el-select v-model="queryForm.sort">
            <el-option v-for="(item,index) in msgData" :key="index" :label="item.name"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="是否去重:" label-width="100px">
            <el-select v-model="queryForm.isOriginal">
                <el-option v-for="(item,index) in isOriginalList" :key="index" :label="item.name"
                    :value="item.value"></el-option>
            </el-select>
        </el-form-item> -->

        <!-- <el-form-item label="媒体类型:" label-width="100px">
            <el-select v-model="queryForm.type">
              <el-option
                v-for="(item,index) in mediaList"
                :key="index"
                :label="item.name"
                :value="item.value"></el-option>
            </el-select>
        </el-form-item> -->
      </el-form>
      <div class="search-btn">
        <el-button type="primary" @click="submitSearch('query')">查询</el-button>
      </div>
    </div>
    <div class="wrap-content">
      <div class="data-table" ref="data_table">
        <div :class="titleFixed?'dataTot is_data_fixed':'dataTot'">

          <div class="mt-4 dataSel">
                        <span v-show="total>0">
                            <el-select size="small" v-model="exportNum" placeholder="选择导出条数" style="width: 125px"
                                       clearable @change="exportNumChange">
                                <el-option label="选择当前页" value="0"/>
                                <el-option label="前500条" value="500"/>
                                <el-option label="前1000条" value="1000"/>
                                <el-option label="前5000条" value="5000"/>
                            </el-select>
                            <img src="@/assets/images/exportIcon.png" v-if="!downloadLoading" class="exportImg"
                                 @click="exportExcel()" alt="">
                            <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;"
                               class="el-icon-loading"></i>
                        </span>
            <span></span>
            <div class="dataSel-left">
              <div class="jump-page">
                <i class="el-icon-arrow-left" @click="goLeft"></i>
                <el-input-number size="mini" @change="submitSearch()" v-model="queryForm.pageNum"
                                 :max="totalPage" :min="1" placeholder="请输入内容"></el-input-number>
                <span class="jump-line">/</span>
                <span>{{totalPage}}</span>
                <i class="el-icon-arrow-right" @click="goRight"></i>
              </div>
              <el-input v-model="queryForm.keyWord2" size="small" clearable placeholder="在结果中搜索，支持单个词组"
                        class="input-with-select">
                <template #prepend>
                  <el-select size="small" v-model="queryForm.searchPosition" placeholder="Select"
                             style="width: 90px">
                    <el-option label="按全文" :value="0"/>
                    <el-option label="按标题" :value="1"/>
                    <el-option label="按正文" :value="2"/>
                    <el-option label="按作者" :value="3"/>
                  </el-select>
                </template>
              </el-input>
              <el-button size="small" :loading="searchLoading" icon="el-icon-search" type="primary"
                         class="search-btns" @click="submitSearch('search')">
                搜索
              </el-button>
            </div>
          </div>
        </div>
        <div class="dataTable">
          <el-table ref="tableRef" v-loading="tableLoading" :data="tableData" border style="width: 100%"
                    :header-cell-style="{background:'#fcfcfd'}" :class="titleFixed?'result-fiexd':''"
                    @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center"></el-table-column>
            <el-table-column prop="title" label="标题" align="left" header-align="center">
              <template #default="scope">
                <div :class="scope.row.isRead==1?'tableItemTitle cover-column':'tableItemTitle'">
                  <div class="tableTitle" @click="goDetail(scope.row)">
                    <img class="tableItemImg" :src="transImage(scope.row.type,scope.row.host||'')" alt="无图片"/>
                    <el-tooltip placement="top" effect="light" raw-content>
                      <div slot="content">
                        <div v-html="scope.row.title"></div>
                      </div>
                      <div class="tableTitleSpan">
                                                <span>{{(queryForm.pageNum - 1) * queryForm.pageSize + scope.$index +
                                                    1}}. </span>
                        <span v-html="scope.row.title"></span>
                      </div>
                    </el-tooltip>
                    <el-select v-model="scope.row.emotionFlag"
                               :class="scope.row.emotionFlag==2?'emotionSelect table-nosense':scope.row.emotionFlag==1?'emotionSelect table-sense':'emotionSelect table-neutral'"
                               size="mini" placeholder="请选择"
                               @change="(val)=>{changeSensitive(val,scope.row)}">
                      <el-option :key="2" label="非敏感" :value="2"></el-option>
                      <el-option :key="1" label="敏感" :value="1"></el-option>
                      <el-option :key="0" label="中性" :value="0"></el-option>
                    </el-select>
                    <p class="article-type" style="background-color: #339593"
                       v-show="scope.row.isOriginal">原创</p>
                    <p class="article-type" v-show="!scope.row.isOriginal">转载</p>
                    <p class="article-type" style="background-color: #F7B8B3;color: #FA2C1C;"
                       v-show="scope.row.warned==1">流转中</p>
                    <p class="article-type" style="background-color: #B2E8F3;color: #00B4D8;"
                       v-show="scope.row.deal==1">已处置</p>
                    <p class="article-type" style="background-color: #F9D0AC;color: #F87500;"
                       v-show="scope.row.follow==1">已重点关注</p>
                    <p class="article-type" style="background-color: #D8D8D8;color: #999999;"
                       v-if="scope.row.urlAccessStatus==0">已删除</p>
                    <p class="article-type" style="background-color: #D5F8D1;color: #3EC82F;"
                       v-else>可访问</p>
                    <p v-for="item in scope.row.contentMeta" class="article-type" style="background-color: #ECF5FF;color: #409EFF;" >
                      {{item}}
                    </p>
                  </div>
                  <div class="tableMain" v-html="scope.row.text" @click="goDetail(scope.row)"></div>
                  <div class="tableFoot">
                    <div class="footInfo">
                      <el-tooltip effect="light" content="评论数" placement="top">
                        <img src="@/assets/images/message.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <span>{{scope.row.commentNum || 0}}</span>
                    </div>
                    <div class="footInfo">
                      <el-tooltip effect="light" content="阅读数" placement="top">
                        <img src="@/assets/images/book.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <span>{{scope.row.readNum || 0}}</span>
                    </div>
                    <div class="footInfo">
                      <el-tooltip effect="light" content="点赞数" placement="top">
                        <img src="@/assets/images/good.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <span>{{scope.row.likeNum || 0}}</span>
                    </div>
                    <div class="footInfo">
                      <el-tooltip effect="light" content="转发数" placement="top">
                        <img src="@/assets/images/share.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <span>{{scope.row.reprintNum || 0}}</span>
                    </div>
                    <div class="footButtonGroup">
                      <div>
                        <div class="footButonItem" v-show="scope.row.hitWords">
                          <el-tooltip effect="light" content="涉及词" placement="top">
                            <img src="@/assets/images/keyword.png" alt=""
                                 class="footIcon"/>
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.hitWords"
                                      placement="top">
                            <span class="keyword">{{ scope.row.hitWords || '' }}</span>
                          </el-tooltip>
                        </div>
                        <div class="footButonItem" v-show="scope.row.hitCourtNames">
                          <el-tooltip effect="light" content="涉及法院" placement="top">
                            <img src="@/assets/images/court.png" alt="" class="footIcon"/>
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.hitCourtNames"
                                      placement="top">
                                                        <span class="keyword" style="color: #247CFF;">{{
                                                            scope.row.hitCourtNames || '' }}</span>
                          </el-tooltip>
                        </div>
                        <div class="footButonItem" v-show="scope.row.contentAreaCodeName">
                          <el-tooltip effect="light" content="精准地域" placement="top">
                            <img src="@/assets/images/areaDetail.png" alt=""
                                 class="footIcon"/>
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.contentAreaCodeName"
                                      placement="top">
                                                        <span class="keyword" style="color: #356391;">{{
                                                            scope.row.contentAreaCodeName || '' }}</span>
                          </el-tooltip>
                        </div>
                      </div>

                      <div style="white-space: nowrap;">
                        <div class="footButonItem" @click="copyAritical(scope.row)">
                          <img src="@/assets/images/copy.png" alt="" class="footIcon"/>
                          <span>复制</span>
                        </div>

                        <div class="footButonItem" @click="openSendMsg(scope.row)">
                          <img src="@/assets/images/send.png" alt="" class="footIcon"/>
                          <span>报送</span>
                        </div>
                        <div class="footButonItem" @click="goOrigin(scope.row.url)">
                          <img src="@/assets/images/goOrigin.png" alt="" class="footIcon"/>
                          <span>查看原文</span>
                        </div>
                        <div class="footButonItem" @click="copyText(scope.row.url,true)">
                          <img src="@/assets/images/copyLink.png" alt="" class="footIcon"/>
                          <span>拷贝地址</span>
                        </div>
                        <!-- <el-dropdown>
                        <div  class="footButonItem">
                            <img src="@/assets/images/filterInfo.png" alt="" class="footIcon" />
                            <span>过滤信息</span>
                        </div>
                        <el-dropdown-menu  slot="dropdown">
                            <el-dropdown-item @click.native="filterOne(scope.row)">过滤单条</el-dropdown-item>
                            <el-dropdown-item @click.native="filterWeb(scope.row)">过滤站点</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown> -->
                        <el-dropdown>
                          <div class="footButonItem">
                            <img src="@/assets/images/markIcon.png" alt=""
                                 class="footIcon"/>
                            <span>标记</span>
                          </div>
                          <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item
                              @click.native="markDisposition(scope.row)">{{scope.row.deal==1?'取消处置':'处置'}}
                            </el-dropdown-item>
                            <el-dropdown-item
                              @click.native="markKeyFocus(scope.row)">{{scope.row.follow==1?'取消重点关注':'重点关注'}}
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                    </div>
                  </div>
                </div>
                <img class="read-img" v-if="scope.row.isRead==1" src="@/assets/images/read.png" alt="">
                <img class="follow-img" v-if="scope.row.follow==1" src="@/assets/images/follow.png" alt="">
              </template>
            </el-table-column>
            <el-table-column prop="count" label="相似信息" align="center" width="100px">
            </el-table-column>
            <el-table-column prop="nickname" label="来源" align="center" width="100px">
              <template #default="scope">
                <div style="cursor: pointer;" @click="goHomepage(scope.row)">
                  <div v-show="scope.row.typeName!='短视频'">{{ scope.row.typeName }}</div>
                  <div>{{ scope.row.host }}</div>
                  <div v-show="scope.row.typeName=='短视频'">{{ scope.row.author }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="publishTime" align="center" width="100px">
              <template slot="header">
                时间
                <span class="sortIconGroup">
                                    <i
                                      :class="`el-icon-caret-top ${(queryForm.sort && queryForm.sort)=='4'?'active':''}`"
                                      @click="sortChange('4')"></i>
                                    <i
                                      :class="`el-icon-caret-bottom ${(queryForm.sort && queryForm.sort)=='3'?'active':''}`"
                                      @click="sortChange('3')"></i>
                                </span>
              </template>
              <template slot-scope="scope">
                <div>{{ scope.row.publishTime.substring(0,10) }}</div>
                <div>{{ scope.row.publishTime.substring(11,19) }}</div>
              </template>
            </el-table-column>
          </el-table>
          <div v-show="total>0"
               style="width:100%;min-height: 20px;display:flex;align-items:center;justify-content: space-between;">
            <div style="margin-top:10px;">
              <el-select v-model="exportNum" size="small" placeholder="选择导出条数" style="width: 125px"
                         clearable @change="exportNumChange">
                <el-option label="选择当前页" value="0"/>
                <el-option label="前500条" value="500"/>
                <el-option label="前1000条" value="1000"/>
                <el-option label="前5000条" value="5000"/>
              </el-select>
              <img src="@/assets/images/exportIcon.png" v-if="!downloadLoading" class="exportImg"
                   @click="exportExcel()" alt="">
              <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;"
                 class="el-icon-loading"></i>
            </div>
            <pagination :total="total" :page.sync="queryForm.pageNum" :limit.sync="queryForm.pageSize"
                        @pagination="pagination"/>

          </div>
        </div>
      </div>
    </div>
    <!-- 短信报送 -->
    <SendMsg :visible.sync="sendMsgDialog" :sendMsgRow="sendMsgRow" @visibleChange="visibleChange"></SendMsg>
  </div>
</template>

<script>
import {copyText, replaceHtml, copyAritical, goHomepage} from "@/utils/index"
import {resetTag, transImage} from '@/utils/index';
import SendMsg from './components/sendMsg.vue';
import {
  infoOne,
  similarityApi,
  similarityExportApi,
  updateEmotion,
  searchRead,
  getUrlAccessStatusApi,
  updateDeal,
  updateFollow
} from "@/api/search/index";

export default {
  components: {SendMsg},
  data() {
    return {
      searchNum: 0,
      titleFixed: false,
      total: 0,
      downloadLoading: false,
      exportNum: null,
      sendMsgDialog: false,
      sendMsgRow: {},//短信报送对象的信息
      tableLoading: false,
      tableData: [],
      multipleSelection: {selectedRows: []},
      queryLoading: false,
      searchLoading: false,
      totalPage: 0,
      transImage,
      goHomepage,

      queryForm: {
        pageNum: 1,
        pageSize: 10,
        keyWord1: '',
        keyWord2: '',
        startTime: undefined,
        endTime: undefined,
        isOriginal: false,
        sort: 3,
        emotionFlag: '',
        searchPosition: 0,
        timeIndex: undefined,
        planId: undefined,
        selectedValues: ['all', 0, 1, 2],// 存放选中值的数组
      },

      articleInfo: {},

      mediaList: [
        {name: '新闻', value: 1},
        {name: '论坛', value: 0},
        {name: '微博', value: 3},
        {name: '微信', value: 5},
      ],
      isOriginalList: [
        {name: '是', value: true},
        {name: '否', value: false},
      ],
      msgData: [
        {name: '时间降序', value: 3},
        {name: '时间升序', value: 4},
      ],
      emotionData: [
        {name: '中性', value: 0},
        {name: '敏感', value: 1},
        {name: '非敏感', value: 2}
      ],
      navData: {},
      startTimeFormatted: '',
      endTimeFormatted: '',


    }
  },
  async created() {
    this.navData = this.$route.query
    this.formatDatesFromQuery()
    this.getArticleInfo()
    await this.querySysList()
    await this.submitSearch()
  },
  mounted() {
    this.titleFixed = false;
    this.$nextTick(() => {
      document.addEventListener('scroll', this.handleScroll, true);
    })
    document.addEventListener("mouseup", (e) => {
      let treeDom = this.$refs.addInput
      if (treeDom) {
        if (!treeDom.contains(e.target)) {
          this.slideFlag = false;
        }
      }
    });
  },
  beforeDestroy() {
    // 当组件即将被销毁时停止间隔
    document.removeEventListener('mouseup', this.showSlide);
    document.removeEventListener('scroll', this.handleScroll, true);
  },
  methods: {
    async querySysList() {
      // // 获取排序方式
      // try{
      //     let res = await this.getDicts('sys_search_sort')
      //     this.msgData = res.data
      //     console.log('this.msgData :>> ', this.msgData);
      //     this.queryForm.sort = this.sortList[0].dictValue
      // }catch(error){
      //     this.$message.error(error)
      // }
    },


    getArticleInfo() {
      let params = {
        id: this.navData.id,
        keyWords: '',
      }
      if (this.navData?.time) {
        params.time = this.navData.time
      }
      infoOne(params).then(res => {
        if (res.code == 200) {
          this.articleInfo = res.data.detail
        }
      })
    },
    formatDatesFromQuery() {
      const startDate = new Date(this.navData.startTime);
      const endDate = new Date(this.navData.endTime);
      // 格式化日期为 "年-月-日"
      this.startTimeFormatted = this.formatDate(startDate);
      this.endTimeFormatted = this.formatDate(endDate);
    },
    formatDate(date) {
      // 格式化日期为 "年-月-日"
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以需要+1
      const day = String(date.getDate()).padStart(2, '0'); // 日期可能需要补零
      return `${year}年${month}月${day}日`;
    },
    selectAll() {
      // 用于点击全选时，判断是该全选还是清除选项
      if (this.queryForm.selectedValues.length < this.emotionData.length) {
        this.queryForm.selectedValues = this.emotionData.map(item => item.value)
        this.queryForm.selectedValues.unshift('all')
      } else {
        this.queryForm.selectedValues = []
      }
    },
    // 移除select中的标签
    removeTag(val) {
      if (val === 'all') this.queryForm.selectedValues = []
    },
    handleSelectChange(val) {
      // val数组不包含'全部'项 且 其他项全部选中
      if (!val.includes('all') && val.length === this.emotionData.length) {
        this.queryForm.selectedValues = val
        this.queryForm.selectedValues.unshift('all')
      }
      // val数组包含'全部'项 且 其他项有未选中的
      if (val.includes('all') && val.length === this.emotionData.length) {
        this.queryForm.selectedValues = val
        this.queryForm.selectedValues.splice(0, 1)
      }
    },
    //滚动监听，头部固定
    handleScroll(val) {
      // if (this.tableData.length > 0) {
      //     this.$nextTick(() => {
      //         const tableFixed = this.$refs.data_table
      //         let offsetTop = tableFixed.getBoundingClientRect().top;
      //         this.titleFixed = offsetTop < 0;
      //         if (this.titleFixed) {
      //             setTimeout(() => {
      //                 const dom = document.querySelector('.is_data_fixed')
      //                 const tableDom = document.querySelector('.el-table__header-wrapper')
      //                 if (dom) {
      //                     if (tableFixed.offsetWidth - dom.offsetWidth != 20 || val) {
      //                         dom.style.width = `${tableFixed.offsetWidth - 20}px`
      //                         tableDom.style.width = `${tableFixed.offsetWidth - 40}px`
      //                     }
      //                 }
      //             }, 200);

      //         }
      //     })
      // } else {
      //     this.titleFixed = false;
      // }

      if (this.tableData.length > 0) {
        const tabFixed = this.$refs.data_table
        let offsetTop = tabFixed.getBoundingClientRect().top;

        this.titleFixed = offsetTop < document.querySelector('.fixed-header').offsetHeight;

        const marginDom = document.querySelector('.dataTable')

        if (this.titleFixed) {
          // this.$nextTick(() => {
          const dom = document.querySelector('.is_data_fixed')
          const tableDom = document.querySelector('.el-table__header-wrapper')
          const dataTotDom = document.querySelector('.dataTot')
          setTimeout(() => {
            if (dom) {
              if (tabFixed.offsetWidth - dom.offsetWidth != 20 || val) {
                dom.style.width = `${tabFixed.offsetWidth - 40}px`
                tableDom.style.width = `${tabFixed.offsetWidth - 40}px`
              }
            }
          }, 10);
          if (marginDom && dataTotDom) {
            marginDom.style.marginTop = `${tableDom.offsetHeight + dataTotDom.offsetHeight}px`
          }
          // })
        } else {
          marginDom.style.marginTop = 0
        }
      } else {
        this.titleFixed = false
      }
    },
    // 分页查询
    pagination(page) {
      this.queryForm.pageNum = page.page
      this.queryForm.pageSize = page.limit
      this.submitSearch()
      document.querySelector('.app-main').scrollTop = 430
    },
    // 列表导出
    exportExcel(id) {
      let params = JSON.parse(JSON.stringify(this.queryForm))
      params.id = this.navData.id
      params.timeIndex = this.navData.timeIndex
      params.startTime = this.navData.startTime
      params.endTime = this.navData.endTime
      params.emotionFlag = params.selectedValues.filter(value => value !== 'all').join(',')
      delete params.selectedValues

      console.log('params :>> ', params);


      const req = {...params}
      if (this.exportNum) { //选项导出
        if (this.exportNum !== '0') {
          req.pageNum = 1
          req.pageSize = parseInt(this.exportNum)
        }
      } else if (this.multipleSelection.selectedRows.length > 0) { //勾选导出
        const ids = this.multipleSelection.selectedRows.map(item => item.id)
        req.ids = ids
      } else {
        this.$message.warning('请选择导出条数或导出项')
        return
      }
      this.downloadLoading = true
      this.$confirm('是否确认导出所有类型数据项?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return similarityExportApi(req);
      }).then(response => {
        this.downloadLoading = false
        this.$message.success('导出成功')
        this.download(response.msg);
      }).catch(() => {
        this.downloadLoading = false
      })
    },
    //导出条数变动后的table选中项改变
    exportNumChange(val) {
      this.$refs.tableRef.clearSelection()
      if (val) {
        this.$refs.tableRef.toggleAllSelection()
      }
    },
    sortChange(sort) {
      this.queryForm.sort = sort
      this.queryForm.sort = Number(sort)
      this.submitSearch()
    },
    //处置||移除
    async markDisposition(row) {
      let val = row.deal ? 0 : 1;
      let res = await updateDeal({md5: row.md5, deal: val, indexId: row?.id, createTime: row?.publishTime})
      if (res.code == 200) {
        this.$set(row, 'deal', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'deal', row.deal);
        this.$message.error(res.msg)
      }
    },
    //重点关注||移除
    async markKeyFocus(row) {
      let val = row.follow ? 0 : 1;
      let res = await updateFollow({md5: row.md5, follow: val, indexId: row?.id, createTime: row?.publishTime})
      if (res.code == 200) {
        this.$set(row, 'follow', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'follow', row.follow);
        this.$message.error(res.msg)
      }
    },
    // 复制文章
    copyAritical(row, tips) {
      copyAritical(row)
    },
    // 复制
    copyText(content, tips) {
      copyText(content, tips)
    },
    // 跳转详情页
    goOrigin(url) {
      window.open(url, '_blank')
    },
    //开启发送短信弹窗
    openSendMsg(row) {
      this.sendMsgDialog = true
      this.sendMsgRow = row
      this.sendMsgRow.keyWord1 = ''
      this.sendMsgRow.planId = ''
    },
    //同步sendMsgDialog值
    visibleChange(value) {
      this.sendMsgDialog = value
    },
    // 跳转详情页
    async goDetail(row) {
      // beforeTime.momentDay beforeTime.oneDay beforeTime.twoDay beforeTime.threeDay beforeTime.sevenDay
      const fullPath = this.$router.resolve({
        path: '/fullSearch/dataDetail',
        query: {id: row.id, planId: row.planId, keyWords: row.hitWords, time: row.publishTime, md5: row.md5}
      })
      window.open(fullPath.href, '_blank')
      if (row.isRead != 1) {
        this.updateIsRead(row.id)
        await searchRead({id: row.id})
      }
    },
    //更新阅读状态
    updateIsRead(id) {
      const foundItem = this.tableData.find(item => item.id === id);
      if (foundItem) {
        this.$set(foundItem, 'isRead', 1);
      }
    },
    // 切换敏感类型
    async changeSensitive(val, row) {
      let res = await updateEmotion({md5: row.md5, emotionFlag: val})
      this.$set(row, 'emotionFlag', val);
      if (res.code == 200) {
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'emotionFlag', row.originFlag);
        this.$message.error(res.msg)
      }
    },
    //table选中项改变
    handleSelectionChange(val) {
      this.multipleSelection.selectedRows = val
    },
    // 查询列表
    async submitSearch(type) {
      this.searchNum++
      this.slideFlag = false
      if (type == 'search') {
        this.queryForm.pageNum = 1
      }
      this.exportNum = ''//翻页时将导出页数选择器重置
      try {
        this.tableLoading = true
        if (type == 'search') { //点击翻页触发该方法
          this.searchLoading = true
        } else if (type == 'query') {
          this.queryLoading = true
        }
        let params = JSON.parse(JSON.stringify(this.queryForm))

        params.id = this.navData.id
        params.timeIndex = this.navData.timeIndex
        params.startTime = this.navData.startTime
        params.endTime = this.navData.endTime
        params.keyWord1 = this.navData.keyWord1 || ''
        params.planId = this.navData.planId || ''
        params.emotionFlag = params.selectedValues.filter(value => value !== 'all').join(',')
        delete params.selectedValues

        console.log('params :>> ', params);

        const res = await similarityApi(params)


        this.tableData = res.rows.map(item => {
          item.originFlag = item.emotionFlag;
          // 初始化 `urlAccessStatus` 属性
          this.$set(item, 'urlAccessStatus', null);
          return item;
        });
        this.tableData = res.rows
        this.tableData.map((item) => {
          item.originFlag = item.emotionFlag
        })
        this.total = Number(res.total)

        this.totalPage = Math.ceil(this.total / this.queryForm.pageSize)
        this.searchLoading = false
        this.queryLoading = false
        this.tableLoading = false
        this.tableData = await this.enrichArrayWithDetails(this.tableData)
        await this.checkUrlAlive(this.tableData, this.searchNum)
      } catch (error) {
        console.log(error)
        this.searchLoading = false
        this.queryLoading = false
        this.tableLoading = false
      }
    },
    //校验原文是否删除
    async checkUrlAlive(data, number) {
      const urls = data.map(item => {
        return item.url
      })
      const newArray = data
      try {
        let res = await getUrlAccessStatusApi(urls)
        res.data.map((item, index) => {
          newArray[index].urlAccessStatus = item
        })
      } catch (err) {
        this.$message.error(err)
      }
      if (this.searchNum == number) {
        this.tableData = newArray;
      }
    },
    async enrichArrayWithDetails(arrayA) {
      // 使用map创建一个Promise数组
      const promises = arrayA.map(async item => {
        return {...item, count: 1};
      });
      const enrichedArray = await Promise.all(promises);
      return enrichedArray;
    },
    goLeft() {
      if (this.totalPage >= this.queryForm.pageNum && this.queryForm.pageNum > 1) {
        this.queryForm.pageNum--
        this.submitSearch()
      }
    },
    goRight() {
      if (this.totalPage > this.queryForm.pageNum && this.totalPage > 0) {
        this.queryForm.pageNum++
        this.submitSearch()
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.similarArticles {
  background: #F4F7FB;
  padding: 20px 40px;
  min-height: 100vh;

  ::v-deep.result-fiexd {
    .el-table__header-wrapper {
      top: 140px;
    }
  }

  .nav {
    background: #fff;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;

    .tableItemTitle {
      flex: 1;
      .tableTitle {
        display: flex;
        align-items: center;

        ::v-deep em {
          color: #f46263;
        }

        .tableItemImg {
          width: 29px;
        }

        .tableItemImg,
        .tableTitleSpan {
          font-size: 16px;
          color: #333333;
          line-height: 22px;
          vertical-align: middle;
          margin-right: 14px;
        }

        .tableTitleSpan {
          display: inline-block;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          // max-width: calc(100% - 190px);
          font-size: 16px;
          line-height: 22px;
          color: #333;
          font-weight: 600;
          width: 700px;
          flex: 1;
        }

        .article-type {
          margin: 0 0 0 10px;
          display: inline-block;
          padding: 2px 6px;
          background: #247CFF;
          font-size: 12px;
          color: #FFFFFF;
          line-height: 17px;
          border-radius: 2px;
          // width: 36px !important;
          width: fit-content;
          white-space: nowrap;
        }
      }

      .tableMain {
        margin: 9px 0 7px 0;
        position: relative;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        /* 控制显示的行数 */
        -webkit-box-orient: vertical;
        max-height: calc(2em * 2);
        /* 控制显示的高度 */
        overflow: hidden;
        font-size: 14px;
        line-height: 20px;
        color: #666;
        // width: 80%;

        ::v-deep em {
          color: #f46263;
        }
      }

      .tableFoot {
        font-size: 12px;
        color: #666;

        .footInfo {
          display: inline-block;
          margin-right: 40px;
          font-size: 14px;
          color: #999;

          img {
            width: 16px;
            margin-right: 6px;
          }
        }

        .footButtonGroup {
          margin: 13px 0 27px 0;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .keyword {
            font-size: 12px;
            color: #F46263;
            line-height: 17px;
            max-width: 12em;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }

          .footButonItem {
            font-size: 12px;
            display: inline-block;
            margin-right: 20px;
            cursor: pointer;
            white-space: nowrap;

            &:last-child {
              // margin-right: 0;
            }
          }
        }

        img {
          width: 14px;
          margin-right: 3px;
        }

        img,
        span {
          display: inline-block;
          vertical-align: middle;
        }
      }
    }


    .tableRight {
      display: flex;
      margin-left: 20px;

      .tableRightItem {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 30px;

        img {
          height: 40px;
          margin-right: 10px;
        }

        .tableRightItemInfo {
          white-space: nowrap;

          .infoTitle {
            font-size: 14px;
            color: #333333;
          }

          .infoCount {
            font-weight: 600;
            font-size: 20px;
            margin-top: 3px;
          }
        }
      }
    }

  }

  .search-wrap {
    // height: 210px;
    background: #FFFFFF;
    margin-top: 30px;
    margin-bottom: 20px;
    padding-bottom: 20px;

    .title {
      height: 60px;
      line-height: 60px;
      border-bottom: 1px solid #DCDEE0;

      span {
        border-left: 6px solid #247CFF;
        padding-left: 28px;
        font-weight: bold;
        font-size: 18px;
        color: #333333;
      }
    }

    .demo-form-inline {
      padding-top: 30px;
      padding-left: 36px;

      ::v-deep .el-form-item__label {
        font-size: 14px;
        color: #666666;
        font-weight: normal;
      }
    }

    .search-btn {
      text-align: center;
    }
  }

  .wrap-content {
    background: #F4F7FB;
  }
}
</style>
<style scoped lang="scss">
@import "./similarArticles.scss";
</style>
