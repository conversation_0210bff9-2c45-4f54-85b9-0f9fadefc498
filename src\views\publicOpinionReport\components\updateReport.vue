<template>
  <div class="custom-template">
    <div class="header">
      <div><span>修改报告</span></div>
      <div>
        <el-button @click="goBack">返回上级</el-button>
        <el-button type="primary" @click="submitTemplate">保存报告</el-button>
      </div>
    </div>
    <div class="contain">
      <div class="side-menu">
        <div class="menu-title">维度</div>
        <div>
          <draggable
            class="components-draggable"
            :list="inputComponents"
            :group="{ name: 'componentsGroup', pull: 'clone'}"
            :clone="cloneComponent"
            @start="dragStart"
            @end="dragEnd"
            :animation="340"
            :sort="false"
          >
            <div v-for="(element, index) in inputComponents" :key="index" class="components-item">
              <div class="components-body">
                <img :src="element.icon" alt="">{{ element.name }}
              </div>
            </div>
          </draggable>

        </div>
      </div>
      <div class="rg">
        <div class="report-header">
          <el-input class="input-one" placeholder="" v-model="queryParmas.title" @blur="moreWord"></el-input>
          <div class="star-insert">第 ( {{ queryParmas.issue }} ) 期</div>
          <div>
            <el-input class="input-two" placeholder="" v-model="queryParmas.head"></el-input>
            <span>{{ queryParmas.createTime }}</span>
          </div>
          <div class="line"></div>
        </div>
        <div class="center-scrollbar">
          <draggable
            class="drawing-board"
            :list="drawingList"
            :animation="340"
            :scroll="true"
            :move="onMove"
            :group="{ name: 'componentsGroup', pull: ''}">
            <div v-for="(element, index) in drawingList" :key="element.id" class="components-item">
              <div v-if="element.id == 1" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">报告导读</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <el-input type="textarea" :rows="5" v-model="queryParmas.reportIntro"></el-input>
                </div>
              </div>
              <div v-if="element.id == 2" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">处置建议</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <el-input type="textarea" :rows="5" v-model="queryParmas.suggest"></el-input>
                </div>
              </div>
              <div v-if="element.id == 3" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">监测概述</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <el-input type="textarea" :rows="5" v-model="queryParmas.overview"></el-input>
                </div>
              </div>
              <div v-if="element.id == 4" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">媒体来源统计</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <pieChart
                    ref="pieChartOneRef"
                    style="width:100%;height:300px;"
                    :data="queryParmas.mediaStatistics"
                    :showLoading="sourceLoading"
                    :isDataView="isChartEdit"
                    :chartStyle="'pie'"
                    @modifiedData="modifiedData"
                    :radius="['30%', '50%']"/>
                </div>
              </div>
              <div v-if="element.id == 5" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">信息情感分析</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <pieChart
                    ref="pieChartOneRef"
                    style="width:100%;height:300px;"
                    :data="queryParmas.emotionAnalysis"
                    :showLoading="sourceLoading"
                    :isDataView="isChartEdit"
                    @modifiedData="modifiedData"
                    :chartStyle="'pie1'"
                    :radius="['30%', '50%']"/>
                </div>
              </div>
              <div v-if="element.id == 6" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">媒体来源明细</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <barChart
                    ref="barChartRef"
                    style="width:100%;height:300px;"
                    :data="queryParmas.mediaDetails"
                    :showLoading="sourceLoading"
                    :chartStyle="'bar'"
                    :isDataView="isChartEdit"
                    @modifiedData="modifiedData"/>
                </div>
              </div>
              <div v-if="element.id == 7" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">信息字符云</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <cloudChart
                    v-show="queryParmas.charCloud.length"
                    ref="cloudChartRef"
                    style="width:100%;height:300px;"
                    :showLoading="sourceLoading"
                    :data="queryParmas.charCloud"
                    :isDataView="isChartEdit"
                    :chartStyle="'cloud'"
                    @modifiedData="modifiedData"
                  />
                </div>
              </div>
              <div v-if="element.id == 8" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">主要舆情</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <el-table
                    ref="tableRef"
                    border
                    v-loading="sourceLoading"
                    :data="queryParmas.mainInfo"
                    :header-cell-style="{background:'#F8FAFF'}">
                    <el-table-column label="序号" type="index" align="center"></el-table-column>
                    <el-table-column label="标题" prop="title" align="center">
                      <template slot-scope="scope">
                        <div v-html="scope.row.title"></div>
                      </template>
                    </el-table-column>
                    <el-table-column label="日期与来源" prop="sourceAndTime" align="center"></el-table-column>
                    <el-table-column label="属性" prop="emotion" align="center"/>
                  </el-table>
                </div>
              </div>
              <div v-if="element.id == 9" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">舆情导读</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <el-descriptions class="margin-top" :column="2" border ref="descriptionsRef">
                    <el-descriptions-item label="性质" label-class-name="my-label">{{ queryParmas.infoIntro.emotion}}
                    </el-descriptions-item>
                    <el-descriptions-item label="文章来源" label-class-name="my-label">{{
                      queryParmas.infoIntro.source}}
                    </el-descriptions-item>
                    <el-descriptions-item label="时间" label-class-name="my-label">{{ queryParmas.infoIntro.time}}
                    </el-descriptions-item>
                    <el-descriptions-item label="作者" label-class-name="my-label">{{ queryParmas.infoIntro.author}}
                    </el-descriptions-item>
                    <el-descriptions-item label="内容" label-class-name="my-label" :span="2">{{
                      queryParmas.infoIntro.text}}
                    </el-descriptions-item>
                    <el-descriptions-item label="原文" label-class-name="my-label" :span="2">
                      <a :href="queryParmas.infoIntro.url" target="_blank" style="color:#247CFF;">{{
                        queryParmas.infoIntro.url}}</a>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
              <div v-if="element.id == 10" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">媒体信息走势图</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <lineChart
                    style="width:100%;height:300px;"
                    :data="queryParmas.mediaTrendChart"
                    :isShow="true"
                    :isDataView="isChartEdit"
                    :showLoading="sourceLoading"
                    :chartStyle="'line'"
                    @modifiedData="modifiedData"/>
                </div>
              </div>


            </div>
          </draggable>
          <div v-show="!drawingList.length" class="empty-info">
            <img src="@/assets/images/selected.png" alt="">
            从左侧栏选中维度，拖放到这里
          </div>
        </div>

      </div>
    </div>

  </div>
</template>

<script>
import draggable from 'vuedraggable'
import dayjs from "dayjs";
import {createTemplate, updateTemplate, detailTemplate} from '@/api/report/template.js'
import {detailReport, updateReport} from '@/api/report/list'
import pieChart from '@/views/fullSearch/components/pieChart.vue'
import barChart from '@/views/fullSearch/components/barChart.vue'
import cloudChart from '@/views/fullSearch/components/cloudChart.vue'
import lineChart from '@/views/fullSearch/components/lineChart.vue'

export default {
  components: {
    draggable, pieChart, barChart, cloudChart, lineChart
  },
  data() {
    return {
      isChartEdit: true,
      timer: null, // 计时器 ID，用于清除计时器
      // currentTime: `${dayjs().format("YYYY-MM-DD")}`,
      inputComponents: [
        {id: 1, params: 'reportIntro', name: '报告导读', icon: require('@/assets/images/report-read.png')},
        {id: 2, params: 'suggest', name: '处置建议', icon: require('@/assets/images/deal-advise.png')},
        {id: 3, params: 'overview', name: '监测概述', icon: require('@/assets/images/monitor-survey.png')},
        {id: 4, params: 'mediaStatistics', name: '媒体来源统计', icon: require('@/assets/images/media-count.png')},
        {id: 5, params: 'emotionAnalysis', name: '信息情感分析', icon: require('@/assets/images/emtion-analyse.png')},
        {id: 6, params: 'mediaDetails', name: '媒体来源明细', icon: require('@/assets/images/source-detail.png')},
        {id: 7, params: 'charCloud', name: '信息字符云', icon: require('@/assets/images/word-cloud.png')},
        {id: 8, params: 'mainInfo', name: '主要舆情', icon: require('@/assets/images/main-public.png')},
        {id: 9, params: 'infoIntro', name: '舆情导读', icon: require('@/assets/images/public-read.png')},
        {id: 10, params: 'mediaTrendChart', name: '媒体信息走势图', icon: require('@/assets/images/info-trend.png')},
      ],
      drawingList: [],
      draggedItem: [],
      queryParmas: {
        title: '舆情简报',
        issue: 1,
        head: '网络舆情中心',
        createTime: '',
        reportIntro: '   本报告就加入素材的文章进行分析，共有49篇相关内容，1篇相关评论。其中微博19篇，占比38%，客户端9篇，占比18%，微信9篇，占比18%，论坛4篇，占比8%，网站4篇，占比8%，新闻4篇，占比8%，外媒1篇，占比2%，而微博的比重最大，共有19篇，达到信息总量的38%。目前主要的报道集中在微博、客户端、微信、论坛、网站等几大站点。详细报告请继续浏览。',
        suggest: '   对于舆情信息中具有潜在危害的事件及情况应给予关注并积极处理，防止不良影响产生及扩散。此外，密切关注此前敏感预警事件的发展情况，及时制定有效应对措施。鉴于监测结果中负面舆情时有发生， 应吸取相关经验教训，做好预防和处理工作。',
        overview: '   监测主题相关信息内容48条，1篇相关评论。其中敏感5条，敏感占比10.2%，非敏感40条，非敏感占比81.6%，中性4条，中性占比8.1%。',
        mediaStatistics: undefined,
        emotionAnalysis: undefined,
        mediaDetails: undefined,
        charCloud: undefined,
        mainInfo: undefined,
        infoIntro: undefined,
        mediaTrendChart: undefined
      },
      reportId: null,
      paramsTemp: {},
      sourceLoading: false,
      originParams: {},
      dataParma: {},
    }
  },
  created() {
    this.reportId = this.$route.query?.reportId
    this.getDetail()
  },
  watch: {
    'queryParmas.charCloud': {
      deep: true,
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.$refs?.cloudChartRef?.[0]?.initChart();
        })
      }
    },
  },
  methods: {
    moreWord() {
      if (this.queryParmas.title.length > 20) {
        this.$message.error('长度不能超过20个字符')
        this.queryParmas.title = this.originParams.title
      }
    },
    // 删除
    drawingDelete(index) {
      this.drawingList.splice(index, 1)
    },
    // 向下移动
    drawingMove(index) {
      this.drawingList.splice(index, 1,
        ...this.drawingList.splice(index + 1, 1, this.drawingList[index]));
    },
    dragStart(event) {
    },
    dragEnd(end) {
    },
    cloneComponent(origin) {
      console.log('this.drawingList :>> ', this.drawingList);
      if (this.drawingList.length > 1) {
        if (!this.drawingList.find(item => item.id == origin.id)) {
          return JSON.parse(JSON.stringify(origin))
        } else {
          this.$message.error('该维度已存在，请勿重复添加！');
        }
      } else {
        return JSON.parse(JSON.stringify(origin))
      }

    },
    onMove(val) {
      console.log('val :>> ', val);
    },
    // 上级
    goBack() {
      this.$router.push({name: 'PublicOpinionReport/report', params: {msg: 3}})
    },
    modifiedData(val, data) {
      let newData = {};
      switch (val) {
        case 'pie':
          newData.mediaStatistics = data || this.originParams.data.mediaStatistics;
          break;
        case 'pie1':
          newData.emotionAnalysis = data || this.originParams.data.emotionAnalysis;
          break;
        case 'bar':
          newData.mediaDetails = data || this.originParams.data.mediaDetails;
          break;
        case 'line':
          newData.mediaTrendChart = data || this.originParams.data.mediaTrendChart;
          break;
        default:
          newData.charCloud = data || this.originParams.data.charCloud;
          break;
      }
      this.dataParma = {
        ...this.dataParma,
        ...newData
      };
    },
    // 保存
    submitTemplate() {
      console.log('submit:>> ', this.drawingList);
      const newArr = JSON.parse(JSON.stringify(this.drawingList))
      let paramsArray = newArr
        .filter(component => component.hasOwnProperty('params')) // 确保对象有 params 属性
        .map(component => component.params);
      console.log('paramsArray :>> ', paramsArray);

      const newPrams = JSON.parse(JSON.stringify(this.queryParmas))
      newPrams.createTime = this.originParams.createTime
      newPrams.mediaStatistics = this.originParams.data.mediaStatistics
      newPrams.emotionAnalysis = this.originParams.data.emotionAnalysis
      newPrams.mediaDetails = this.originParams.data.mediaDetails
      newPrams.infoIntro = this.originParams.data.infoIntro

      const params = {
        head: newPrams.head,
        title: newPrams.title,
        createTime: newPrams.createTime,
        issue: newPrams.issue,
        reportId: this.reportId,
        data: {
          inputComponents: paramsArray,
          mediaStatistics: this.dataParma.mediaStatistics || newPrams.mediaStatistics,
          emotionAnalysis: this.dataParma.emotionAnalysis || newPrams.emotionAnalysis,
          mediaDetails: this.dataParma.mediaDetails || newPrams.mediaDetails,
          charCloud: this.dataParma.charCloud || newPrams.charCloud,
          mediaTrendChart: this.dataParma.mediaTrendChart || newPrams.mediaTrendChart,
          mainInfo: newPrams.mainInfo,
          infoIntro: newPrams.infoIntro,
          overview: newPrams.overview,
          suggest: newPrams.suggest,
          reportIntro: newPrams.reportIntro
        }
      }
      console.log('params :>> ', params);

      if (paramsArray.length) {
        updateReport(params).then(res => {
          if (res.code == 200) {
            this.$message.success('保存成功')
            this.$router.push({name: 'PublicOpinionReport/report', params: {msg: 3}})
            this.drawingList = []

          }
        })
      } else {
        this.$message.error('请至少选择一个维度')
      }
    },
    async getDetail() {
      if (this.reportId) {
        this.sourceLoading = true
        const res = await detailReport(this.reportId)
        this.originParams = res.data
        let items = res.data.data.inputComponents
        const dimensionMap = this.inputComponents.reduce((acc, item) => {
          acc[item.params] = item;
          return acc;
        }, {});

        this.drawingList = items.map(param => dimensionMap[param]);

        this.queryParmas.title = res.data.title
        this.queryParmas.issue = res.data.issue
        this.queryParmas.head = res.data.head
        this.queryParmas.createTime = res.data.createTime.slice(0, 10);
        this.queryParmas.reportIntro = res.data.data.reportIntro
        this.queryParmas.suggest = res.data.data.suggest
        this.queryParmas.overview = res.data.data.overview

        this.queryParmas.mediaStatistics = {data: res.data.data.mediaStatistics}
        this.queryParmas.emotionAnalysis = {data: res.data.data.emotionAnalysis}

        const yyData = res.data.data?.mediaDetails?.xList
        const xxData = res.data.data?.mediaDetails?.yList
        this.queryParmas.mediaDetails = {xxData, yyData}

        this.queryParmas.charCloud = res.data.data.charCloud
        this.queryParmas.mainInfo = res.data.data.mainInfo
        this.queryParmas.infoIntro = res.data.data.infoIntro[0]
        this.queryParmas.mediaTrendChart = res.data.data.mediaTrendChart
        this.sourceLoading = false


      }

    },
  }
}
</script>
<style lang="scss" scoped>
.custom-template {
  background: #F4F7FB;
  height: calc(100vh - 80px);
  padding: 20px;

  .header {
    height: 54px;
    line-height: 54px;
    background: #FFFFFF;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    padding-right: 20px;

    span {
      border-left: 6px solid #247CFF;
      padding-left: 30px;
      font-size: 18px;
      color: #333333;
      font-weight: bold;
    }

  }

  .contain {
    display: flex;
    justify-content: space-between;
    height: calc(100vh - 184px);

    .side-menu {
      width: 300px;
      margin-right: 20px;
      background: #FFFFFF;
      padding: 16px;
      overflow-y: auto;

      .menu-title {
        font-size: 16px;
        color: #000000;
        font-weight: bold;
        text-align: center;
        border-bottom: 2px solid #f1eeee;
        padding-bottom: 10px;
        margin-bottom: 20px;
      }
    }

    .rg {
      flex: 1;
      background: #FFFFFF;
      padding: 28px 40px;
      overflow-y: auto;

      .report-header {
        text-align: center;
        font-weight: bold;

        .input-one {
          width: 416px;

          ::v-deep .el-input__inner {
            height: 50px;
            line-height: 50px;
            border: 1px solid #979797;
            padding: 0px;
            font-weight: bold;
            font-size: 24px;
            color: #FF0D0D;
            text-align: center;
          }
        }

        .star-insert {
          margin: 10px;
          font-size: 14px;
          line-height: 20px;
        }

        .line {
          height: 1px;
          border: 2px solid #FF0D0D;
          margin-top: 26px;
          margin-bottom: 35px;
        }

        .input-two {
          width: 106px;
          margin-right: 10px;

          ::v-deep .el-input__inner {
            height: 28px;
            line-height: 28px;
            border: 1px solid #979797;
            padding: 0px;
            font-size: 14px;
            font-weight: bold;
            color: #333333;
            text-align: center;
          }

        }
      }
    }

  }
}

.components-draggable {
  padding-bottom: 20px;
}

.components-item {
  display: inline-block;
  width: 100%;
  margin-bottom: 20px;
  transition: transform 0ms !important;

  .div-box {
    .nav-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 44px;
      background: #F0F5FF;
      padding-left: 30px;

      .nav-title {
        font-size: 18px;
        color: #000000;
        font-weight: bold;
      }

      .images {
        img {
          width: 20px;
          height: 20px;
          margin-right: 20px;
          cursor: pointer;
        }
      }
    }

    .content-textarea {
      text-align: center;
      margin-top: 20px;

      ::v-deep .el-textarea__inner {
        color: #333;
        font-weight: bold;
        font-size: 18px;
      }

      img {
        height: 100%;
        width: 100%;
      }
    }
  }
}

.center-scrollbar {
  height: calc(100vh - 426px);
  overflow-y: auto;
  box-sizing: border-box;
  border: 1px solid #CCCCCC;
  padding: 20px 40px;
  position: relative;
}

.components-body {
  padding: 12px 0px;
  padding-left: 44px;
  font-size: 16px;
  color: #333333;
  cursor: move;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;

  img {
    width: 22px;
    height: 22px;
    margin-right: 6px;
  }
}

.center-board-row {
  padding: 12px 12px 15px 12px;
  box-sizing: border-box;

  & > .el-form {
    // 69 = 12+15+42
    // height: calc(100vh - 69px);
    height: 100%;
    overflow-y: auto;
  }
}

.drawing-board {
  height: 100%;
}

.empty-info {
  font-size: 16px;
  line-height: 22px;
  color: #333333;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  img {
    width: 50px;
    height: 50px;
    margin-bottom: 28px;
  }
}

::v-deep .my-label {
  width: 80px;
}
</style>
