<template>
  <div :class="classObj" class="app-wrapper" :style="{'--current-color': theme}">
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside"/>
    <sidebar v-if="menuView==0" class="sidebar-container"
             :style="{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBg : variables.menuLightBg }"/>
    <div :class="{hasTagsView:needTagsView}" class="main-container" v-if="menuView==0">
      <div :class="{'fixed-header':fixedHeader}">
        <navbar/>
        <tags-view v-if="needTagsView"/>
      </div>
      <app-main/>
      <right-panel v-if="showSettings">
        <settings/>
      </right-panel>
    </div>

    <div :class="{hasTagsView:needTagsView}" class="main-container" style="margin-left:0px" v-else>
      <div :class="{'fixed-header':fixedHeader}" style="width:100%">
        <navbar/>
        <tags-view v-if="needTagsView"/>
      </div>
      <app-main/>
      <right-panel v-if="showSettings">
        <settings/>
      </right-panel>
    </div>
  </div>
</template>

<script>
import RightPanel from '@/components/RightPanel'
import {AppMain, Navbar, Settings, Sidebar, TagsView} from './components'
import ResizeMixin from './mixin/ResizeHandler'
import {mapState} from 'vuex'
import variables from '@/assets/styles/variables.scss'

export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: state => state.settings.theme,
      sideTheme: state => state.settings.sideTheme,
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      showSettings: state => state.settings.showSettings,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    variables() {
      return variables;
    },
    menuView() {
      return this.$store.state.settings.menuView
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', {withoutAnimation: false})
    }
  },
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
@import "~@/assets/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  // position: fixed;
  top: 0;
  right: 0;
  z-index: 2001;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
  display: flex;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px)
}

.mobile .fixed-header {
  width: 100%;
}

/* 手机横屏模式优化 */
@media screen and (max-width: 992px) and (orientation: landscape) {
  .app-wrapper {
    overflow: hidden;
  }

  .fixed-header {
    height: auto;
    flex-wrap: wrap;
    position: fixed; /* 确保固定在顶部 */

    /* 减小内部元素的间距和大小 */
    ::v-deep .navbar {
      padding: 0 10px;
      height: 40px;
      line-height: 40px;


      .sidebar-title {
        font-size: 24px;
      }
      .sidebar-logo{
        width: 30px;
      }

      .hamburger-container {
        line-height: 40px;
      }

      .right-menu {
        line-height: 40px;

        .avatar-container {
          margin-right: 15px;

          .avatar-wrapper {
            margin-top: 5px;

            .user-avatar {
              width: 30px;
              height: 30px;
            }

            .el-icon-caret-bottom {
              top: 15px;
            }
          }
        }
      }
    }

    ::v-deep .tags-view-container {
      height: 30px;

      .tags-view-wrapper {
        .tags-view-item {
          height: 22px;
          line-height: 22px;
          padding: 0 6px;
          margin-top: 3px;
          font-size: 11px;

          &:first-of-type {
            margin-left: 10px;
          }

          &:last-of-type {
            margin-right: 10px;
          }
        }
      }
    }
  }

  /* 调整主容器的内边距，为内容留出更多空间 */
  .main-container {
    padding-top: 40px;  /* 根据减小后的header高度调整 */

    ::v-deep .app-container {
      padding: 10px;  /* 减小内容区域的内边距 */
    }
  }

  /* 确保内容区域可以滚动 */
  ::v-deep .app-main {
    min-height: calc(100vh - 40px);
    overflow-y: auto;
  }
}
</style>
