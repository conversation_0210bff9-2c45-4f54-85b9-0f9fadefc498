.search-wrap-result {
  background: #F4F7FB;
  padding-bottom: 20px;
  // height: calc(100vh - 400px);
  height: 100vh;
  overflow-y: auto;

}

.search-wrap-result::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}

.search-head {
  width: 100%;
  height: 400px;
  position: relative;
  border-radius: 8px;

  .head-img {
    width: 100%;
    height: 400px;
  }

  .head-wrap {
    width: 830px;
    text-align: center;
    position: absolute;
    top: 88px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 8;
    color: #fff;
    font-size: 16px;

    .append-head {
      display: flex;
      align-items: center;
    }

    .line {
      display: inline-block;
      margin-left: 26px;
      width: 1px;
      height: 35px;
      background: #EEEEEE;
    }
  }

  .head-input {
    margin-bottom: 7px;
    position: relative;
    display: flex;
    align-items: center;
  }

  .slide-box {
    position: absolute;
    top: 60px;
    left: 96px;
    right: 0;
    width: 610px;
    z-index: 100;
    margin-top: 10px;
    padding: 20px 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    text-align: left;
  }

  .slide-item {
    margin-right: 15px;
    padding: 4px 10px 6px 10px;
    border-radius: 5px;
    background: #1890ff;
    color: #fff;
    cursor: pointer;
    line-height: 24px;
  }

  .name-question {
    margin-left: 10px;
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  .head-title {
    margin-bottom: 45px;
    font-size: 48px;
    font-weight: 500;
    line-height: 67px;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .hot-words {
    width: 830px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    line-height: 22px;

    .hot-title {
      display: block;
      line-height: 33px;
      padding-top: 7px;
    }

    .word-list {
      padding-top: 7px;
      max-width: 750px;
      max-height: 120px;
      overflow-y: auto;
      margin-left: 14px;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;

      .el-icon-error {
        display: none;
        position: absolute;
        right: -6px;
        top: -8px;
        font-size: 20px;
        color: #FF0000;
        cursor: pointer;
      }

      .words-main {
        position: relative;
        display: flex;
        align-items: center;
        margin-right: 6px;
        margin-bottom: 13px;
        padding: 5px 6px;
        border-radius: 3px;
        background: #798af6;

        &:hover {
          .el-icon-error {
            display: block;
          }
        }

        img {
          display: inline-block;
          margin-right: 6px;
          width: 18px;
          height: 23px;
        }

        .keywords-content {
          cursor: pointer;
        }
      }
    }
  }
}

.result-wrap {
  width: calc(100% - 80px);
  margin: 0 auto;
  margin-top: 10px;

  .collapseButton {
    position: absolute;
    z-index: 9;
    right: 20px;
    top: 20px;
  }

  .retract {
    display: flex;
    align-items: center;
    cursor: pointer;

    span {
      display: inline-block;
      margin-left: 6px;
    }
  }
}

// 舆情数据
.data-wrap {
  .pagination-container {
    text-align: right;
    margin-top: 10px;
    padding: 0 20px !important;

    .el-pagination {
      position: relative;
    }
  }

  .condition-head {
    margin-bottom: 30px;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    line-height: 22px;
    color: #333;
    border-bottom: 1px dashed #DCDEE0;

    .name {
      &::before {
        content: '';
        display: inline-block;
        height: 16px;
        width: 6px;
        background: #247CFF;
        margin-right: 28px;
      }
    }
  }

  .data-condition {
    background: #fff;
    padding-bottom: 20px;

    .name-list .left-content {
      width: 50%;
      font-size: 14px;
      color: #666;
    }
  }

  .data-table {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
  }

  .name-list {
    display: flex;
    align-items: baseline;
    justify-content: flex-start;
    margin: 25px 0 0 36px;
    font-size: 14px;
    min-height: 28px;
    color: #666;

    .time-content {
      width: 50%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      color: #666;
      font-size: 14px;

    }

    .list-content {
      width: 50%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      color: #666;
      font-size: 14px;

      .list-img {
        cursor: pointer;
        width: 16px;
        margin-left: 3px;
      }
    }

    .desc {
      color: #999;
    }
  }

  .area-select {
    margin-right: 10px;
  }
}

.devide {
  width: 100%;
  height: 20px;
  background: #F4F7FB;
}

.name-search {
  display: flex;
  justify-content: center;
  // margin-top: 34px;
}

.moreButton {
  color: #247CFF !important;
  cursor: pointer;

  &:hover {
    color: #46a6ff !important;
  }
}

.dataTot {
  width: 100%;
  text-align: center;
  margin: 0;
  font-size: 14px;
  color: #333;

  .totalNum {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 40px;
    border-right: 20px solid #fff;
    border-left: 20px solid #fff;
    background-color: #fcfcfd;
  }

  .data-number {
    color: #247CFF;
    margin: 0 3px;
  }

  i.el-icon-refresh {
    margin-left: 6px;
    font-size: 16px;
    color: #247CFF;
  }

  .dataSel-left {
    display: flex;
    align-items: center;
  }

  .jump-page {
    margin-right: 50px;
    font-size: 14px;
    line-height: 22px;

    span {
      color: #000;

      &.jump-line {
        margin: 0 16px;
      }
    }

    i {
      color: #000;

      &.el-icon-arrow-right {
        margin-left: 18px;
      }

      &.el-icon-arrow-left {
        margin-right: 18px;
      }
    }
  }

  .dataSel {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 13px 20px 20px 20px;

    .input-with-select {
      width: 400px;
    }
  }

  .el-icon-refresh {
    color: #247CFF;
    cursor: pointer;
  }

  .search-btns {
    margin-left: 15px;
  }
}

.dataTable {
  width: 100%;
  //overflow: hidden;
  padding: 20px;
  padding-top: 0;
  box-sizing: border-box;
  background: #fff;

  .tableItemTitle {
    .tableTitle {
      margin-top: 25px;
      display: flex;
      align-items: center;

      ::v-deep em {
        color: #f46263;
      }

      .tableItemImg {
        width: 29px;
      }

      .tableItemImg,
      .tableTitleSpan {
        font-size: 16px;
        color: #333333;
        line-height: 22px;
        vertical-align: middle;
        margin-right: 14px;
      }

      .tableTitleSpan {
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        max-width: calc(100% - 190px);
        font-size: 16px;
        line-height: 22px;
        color: #333;
        cursor: pointer;
        font-weight: 600;
      }

      .article-type {
        margin: 0 0 0 10px;
        display: inline-block;
        padding: 2px 6px;
        background: #247CFF;
        font-size: 12px;
        color: #FFFFFF;
        line-height: 17px;
        border-radius: 2px;
        // width: 36px !important;
        width: fit-content;
        white-space: nowrap;
      }
    }

    .tableMain {
      margin: 9px 0 7px 0;
      position: relative;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      /* 控制显示的行数 */
      -webkit-box-orient: vertical;
      max-height: calc(2em * 2);
      /* 控制显示的高度 */
      overflow: hidden;
      font-size: 14px;
      line-height: 20px;
      color: #666;
      cursor: pointer;

      ::v-deep em {
        color: #f46263;
      }
    }

    .tableFoot {
      font-size: 12px;
      color: #666;

      .footInfo {
        display: inline-block;
        margin-right: 40px;
        font-size: 14px;
        color: #999;

        img {
          width: 16px;
          margin-right: 6px;
        }
      }

      .footButtonGroup {
        margin: 13px 0 27px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .keyword {
          font-size: 12px;
          color: #F46263;
          line-height: 17px;
          max-width: 12em;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }

        .footButonItem {
          font-size: 12px;
          display: inline-block;
          margin-right: 20px;
          cursor: pointer;
          white-space: nowrap;

          &:last-child {
            // margin-right: 0;
          }
        }
      }

      img {
        width: 14px;
        margin-right: 3px;
      }

      img,
      span {
        display: inline-block;
        vertical-align: middle;
      }
    }
  }

  .cover-column {
    opacity: 0.5;
  }

  .follow-img {
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
  }

  .read-img {
    position: absolute;
    top: 30%;
    right: 20%;
    width: 80px;
  }
}

.export-download {
  margin-left: 15px;
  font-size: 16px;
  vertical-align: middle;
  cursor: pointer;
  color: #247CFF;
}

.exportImg {
  width: 16px;
  vertical-align: middle;
  margin-left: 20px;
}

.sortIconGroup {
  i {
    cursor: pointer;
  }

  .active {
    color: #247CFF;
  }
}

.is_data_fixed {
  width: inherit;
  position: fixed;
  top: 80px;
  background: #fff;
  z-index: 299;
}

::v-deep {
  .el-backtop {
    background-color: rgba(255, 255, 255, 0.6);
  }
}

.emotionSelect {
  ::v-deep .el-input.el-input--mini.el-input--suffix {
    width: 72px;
  }
}

.sort-num {
  display: flex;
}

.footIcon {
  cursor: pointer;
}

.subClass {
  .el-popper[x-placement^=right] .popper__arrow {
    border: none;
  }

  .el-popper[x-placement^=right] .popper__arrow::after {
    border: none;
  }
}