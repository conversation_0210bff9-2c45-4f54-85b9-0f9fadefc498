<template>
  <div class="phone-wrap">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="phone-tabs">
      <el-tab-pane label="信息详情" name="first">
        <div class="phone-detail" v-loading="loading">
          <el-backtop target=".phone-detail" :right="10"></el-backtop>
          <h3>标题: <span v-html="detailParams.title"></span></h3>
          <div class="phone-time">
            <span>{{ detailParams.publishTime }}</span>
            <div @click="activeName='second'">
              <img src="@/assets/images/origin.png" alt="">
              <span>{{ detailParams.typeName }}-{{ detailParams.author }}</span>
            </div>
          </div>
          <div class="phone-type">
            <span>风险等级：</span>
            <span class="phone-lower">低</span>
          </div>
          <div class="phone-type">
            <span>关键词：</span>
            <span class="phone-emotion">{{ detailParams.hitWords }}</span>
          </div>
          <div class="phone-type">
            <span>单位：</span>
            <span class="phone-unit">{{ detailParams.hitCourtNames || '暂无' }}</span>
          </div>
          <div class="phone-content">
            <div class="phone-head">
              <span class="phone-left">内容提取：</span>
              <div class="phone-right">
                <div class="phone-share" @click="copyAritical">
                  <img src="@/assets/images/share-phone.png" alt="">
                  <span>分享信息</span>
                </div>
                <div class="phone-origin" @click="goOriginal(detailParams.url)">
                  <img src="@/assets/images/origin-active.png" alt="">
                  <span>原文</span>
                </div>
              </div>
            </div>
            <div class="content-detail">
              <div>【正文内容】</div>
              <div v-html="detailParams.text">

              </div>
            </div>
          </div>
          <div class="down-wrap" v-if="detailParams.submit">
            <div class="down-title">
              处置时效：
              <TimeDown ref="detailSubmit" v-if="targetDate" :endTime="endTime" :targetDate="targetDate"
                        @clearTime="closeDown"></TimeDown>
              <span class="no-deal" v-if="detailParams.submit.processStatus==2">未处置</span>
            </div>
            <div class="down-title">
              处置建议：{{ detailParams.submit.suggest || '暂无' }}
            </div>
            <div class="down-title" v-if="detailParams.submit.processStatus==0">
              <span class="down-icon">处置：</span>
              <el-input style="width: 70%;" type="textarea" :rows="4" v-model="dealTag"
                        placeholder="请输入处置描述"></el-input>
            </div>
            <div class="down-button" v-if="detailParams.submit.processStatus==0">
              <el-button type="primary" @click="handleDeal" :loading="dealLoad">确定</el-button>
            </div>
            <div class="down-title" v-if="detailParams.submit.processStatus==1">
              <span>处置详情：</span>{{ detailParams.submit.processText }}
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="账号信息" name="second">
        <div class="account-detail" v-loading="articleLoad">
          <el-backtop target=".account-detail" :right="10"></el-backtop>
          <div class="account-wrap">
            <img :src="personParams.avatar||defaultAvatar" alt="">
            <h3>{{ detailParams.author }}</h3>
            <div>账号地址：{{ personParams.province }}{{ personParams.city ? '-' + personParams.city : '暂无' }}</div>
            <div>类型：{{ detailParams.typeName }}</div>
          </div>
          <h2>该账户近30天敏感信息：</h2>
          <div class="account-content" v-for="item in items">
            <div class="content-head">
              <h4>{{ item.title }}</h4>
              <p>{{ item.publishTime }}</p>
            </div>
            <div class="account-content-detail">
              <div>【正文内容】</div>
              <div @click="goOriginal(item.url)" v-html="item.text" class="detail-msg">
              </div>
              <div class="detail-icon">
                <div>平台：{{ item.typeName }}</div>
                <div class="detail-origin" @click="goOriginal(item.url)">
                  <img src="@/assets/images/origin-active.png" alt="">
                  <span>原文</span>
                </div>
              </div>
            </div>
          </div>
          <infinite-loading @infinite="infiniteHandler">
                        <span slot="no-more">
                            我们是有底线的~~~
                        </span>
            <span slot="no-results">
                            我们是有底线的~~~
                        </span>
          </infinite-loading>
        </div>
      </el-tab-pane>
      <el-tab-pane label="传播信息" name="third">
        <div class="spread-box">
          <el-timeline>
            <el-timeline-item :timestamp="index==0?item.time+' 首发':item.time" placement="top"
                              v-for="(item,index) in similarData" :key="index">
              <el-card>
                <div class="spread-wrap">
                  <img v-if="index==0" src="@/assets/images/red-flag.png" alt="" class="spread-icon">
                  <div class="spread-type">{{ item.hostName }}</div>
                  <div @click="goOriginal(item.url)" class="spread-curp">
                    <img src="@/assets/images/origin.png" alt="" class="spread-link">
                    <span>{{ item.author }}</span>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-tab-pane>
      <!-- <el-tab-pane label="相关热搜" name="fourth">
        <div class="hot-search" v-loading="hotLoad">
          <div v-if="hotData.length>0">
            <div class="hot-wrap" v-for="item in hotData" :key="item.id">
              <div class="hot-box">
                <span>平台：</span>
                <div>{{ item.type }}</div>
              </div>
              <div class="hot-box hot-curp">
                <span>热搜词条：</span>
                <div class="search-words" @click="goOriginal(item.url)"><img class="img-link"
                                                                             src="@/assets/images/origin-active.png"
                                                                             alt=""> {{ item.title }}
                </div>
              </div>
              <div class="hot-box">
                <span>最高热度：</span>
                <div class="hot-height">{{ item.indexNum }}</div>
              </div>
              <div class="hot-box">
                <span>最高排名：</span>
                <div class="hot-height"><img src="@/assets/images/red-flag.png" alt="">{{ item.sort }}</div>
              </div>
              <div class="hot-box">
                <span>登上热搜时间：</span>
                <div>{{ item.updateTime }}</div>
              </div>
            </div>
          </div>
          <div v-else>
            <div class="hot-none">
              <img src="@/assets/images/nodata.svg" alt="">
              <div>暂无数据</div>
            </div>
          </div>
        </div>
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>
<script>
import InfiniteLoading from 'vue-infinite-loading'
import {getAccountInfo, resultArticle, resultDetail, similarHot, submitProcess} from "@/api/phone.js";
import {copyText, replaceHtml} from "@/utils/index"
import {infoSimilar} from "@/api/search/index";
import TimeDown from "@/components/TimeDown/index";

export default {
  components: {InfiniteLoading, TimeDown},
  data() {
    return {
      targetDate: '',
      dealLoad: false,
      dealTag: '',
      defaultAvatar: require("@/assets/images/default.png"),
      similarData: [],
      total: 0,
      loading: false,
      items: [], // 数据源
      queryParams: {
        pageNum: 1, // 当前页码
        pageSize: 10, // 每页数据条数
      },
      activeName: 'first',
      detailParams: {
        typeName: "",
        publishTime: "",
        title: "",
        text: "",
        author: "",
        hitWords: "",
        hitCourtNames: [],
        submit: {
          id: '',
          deadline: '',
          suggest: '',
          processText: '',
          processStatus: ''
        }
      },
      personParams: {
        city: "",
        nickname: "",
        province: "",
        avatar: ""
      },
      articleLoad: true,
      hotData: [],
      hotLoad: true,
      endTime: undefined
    };
  },
  methods: {
    // 处置
    async handleDeal() {
      if (!this.dealTag) {
        this.$message.error('处置内容不能为空')
        return
      }
      try {
        this.dealLoad = true
        let res = await submitProcess({
          id: this.detailParams.id,
          processText: this.dealTag,
          userId: this.$route.query.userId
        })
        this.queryDetail()
      } finally {
        this.dealLoad = false
      }

    },
    // 倒计时为0
    closeDown() {
      if (this.queryParams.submit) {
        this.queryDetail()
      }
    },
    // 原文
    goOriginal(url) {
      window.open(url, '_blank')
    },
    // 获取信息详情
    async queryDetail() {
      try {
        this.loading = true
        let decodedName = decodeURIComponent(this.$route.query.keyWord1);
        let res = await resultDetail({
          userId: this.$route.query.userId,
          startTime: this.$route.query.publishTime,
          id: this.$route.query.id,
          keyWord1: this.$route.query.keyWord1,
          planId: this.$route.query.planId
        })
        this.detailParams = res.data
        if (this.detailParams.submit) {
          this.targetDate = this.detailParams.submit.deadline
          if (this.detailParams.submit.processStatus == '1') {
            var dealTime = new Date(this.detailParams.submit.processTime).getTime()
            this.endTime = dealTime
          } else {
            this.endTime = undefined
          }
        }
      } finally {
        this.loading = false
      }
    },
    // 获取个人头像信息
    async queryPersonal() {
      let res = await getAccountInfo({
        type: this.detailParams.type,
        nickname: this.detailParams.author,
        domain: this.detailParams.host
      })
      this.personParams = res.data ||{
        city: "",
        nickname: "",
        province: "",
        avatar: ""
      }
    },
    //  作者发文-下拉加载
    infiniteHandler($state) {
      console.log($state, '$state');
      // 加载下一页数据
      if (this.total > this.items.length) {
        this.articleLoad = true
        this.queryParams.pageNum++
        resultArticle({author: this.detailParams.author, type: this.detailParams.type, ...this.queryParams})
          .then(response => {
            this.articleLoad = false
            // 将数据添加到items中
            this.items = this.items.concat(response.rows)
            // 如果数据已经全部加载完毕，调用$state.complete()方法
            if (response.rows.length < this.queryParams.pageSize) {
              $state.complete()
            } else {
              // 否则调用$state.loaded()方法，表示还有更多数据可加载
              // this.queryParams.pageNum++
              $state.loaded()
            }
          }).catch(error => {
          // 加载数据失败
          // $state.error()
        })
      } else {
        $state.complete()
      }
    },
    // 复制-分享信息
    copyAritical() {
      let text = replaceHtml(this.detailParams.title)
      let shareMsg = ` 标题：${text}\n 时间：${this.detailParams.publishTime} \n 单位：${this.detailParams.hitCourtNames || '暂无'}\n 风险等级：低 \n 摘要：【正文内容】\n ${replaceHtml(this.detailParams.text)} \n 链接：${this.detailParams.url}`
      copyText(shareMsg, false)
    },
    handleClick(val) {
      console.log(val, 'val');
    },
    // 获取传播信息
    async querySimilar() {
      let res = await infoSimilar({
        id: this.$route.query.id,
        md5: this.detailParams.md5,
        time: this.detailParams.publishTime,
        sort: 'asc'
      })
      this.similarData = res.data
    },
    // 获取相关热搜
    async queryHot() {
      try {
        this.hotLoad = true
        let text = replaceHtml(this.detailParams.title)
        let res = await similarHot({keyWord1: text})
        this.hotData = res.data
      } finally {
        this.hotLoad = false
      }
    }
  },
  async created() {
    document.title = '舆情预警'
    await this.queryDetail()
    await this.querySimilar()
    await this.queryPersonal()
    await resultArticle({
      author: this.detailParams.author,
      type: this.detailParams.type, ...this.queryParams
    }).then((res) => {
      this.articleLoad = false
      this.items = res.rows
      this.total = res.total
    }).catch(() => {
      this.articleLoad = false
    })
    // await this.queryHot()
  }
}
</script>
<style>
.phone-detail em {
  font-style: normal;
}
</style>
<style lang="scss" scoped>
.down-wrap {
  margin-bottom: 20px;
  padding: 16px 10px 20px 10px;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.14);
  border-radius: 4px;
  font-size: 13px;
  color: #333333;
  line-height: 20px;

  .down-title {
    margin-bottom: 16px;
    display: flex;

    .down-icon {
      &::before {
        content: '*';
        color: #FF2D26;
      }
    }
  }

  .down-button {
    text-align: center;
  }

  .no-deal {
    margin-left: 20px;
  }
}

.phone-wrap {
  height: 100%;
}

.phone-tabs {
  .phone-detail {
    height: calc(100vh - 70px);
    overflow-y: auto;
    padding: 0 20px;
  }
}

.phone-detail {
  color: #333;
  font-size: 13px;
  line-height: 20px;

  h3 {
    margin: 0 0 7px 0;
    font-size: 14px;
    line-height: 24px;
  }

  .phone-time {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;
    font-size: 12px;

    & > div {
      cursor: pointer;
    }

    img {
      width: 12px;
      margin-right: 2px;
      vertical-align: text-bottom;
    }
  }

  .phone-type {
    margin-bottom: 7px;

    .phone-lower {
      font-weight: bold;
      color: #FF9191;
    }

    .phone-emotion {
      font-weight: bold;
      color: #FF2D26;
    }

    .phone-unit {
      font-weight: bold;
      color: #247CFF;
    }
  }

  .phone-content {
    margin: 15px 0 10px 0;
    padding: 16px 10px 12px 10px;
    background: #F3FCFB;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.14);
    border-radius: 4px;
  }

  .phone-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 10px;
    border-bottom: 1px dashed #979797;
    margin-bottom: 8px;

    .phone-left {
      font-weight: bold;
      margin-left: 2px;
    }

    .phone-right {
      display: flex;

      img {
        width: 12px;
        margin-right: 1px;
        vertical-align: text-bottom;
      }

      .phone-share {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 74px;
        height: 20px;
        margin-right: 3px;
        line-height: 20px;
        border: 1px solid #439E88;
        border-radius: 2px;
        font-size: 12px;
        color: #439E88;
        cursor: pointer;
      }

      .phone-origin {
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 54px;
        height: 20px;
        line-height: 20px;
        border: 1px solid #247CFF;
        border-radius: 2px;
        font-size: 12px;
        color: #247CFF;
        cursor: pointer;
      }
    }
  }
}

.account-content-detail {
  font-weight: bold;
  line-height: 27px;

  em {
    font-style: normal !important;
  }
}

.content-detail {
  font-weight: bold;
  line-height: 27px;
  word-break: break-all;

  em {
    color: #FF2D26;
  }

  span {
    font-size: 15px;
    color: #439E88;
  }
}

.account-detail {
  padding: 0 20px;
  height: calc(100vh - 70px);
  overflow-y: auto;

  h2 {
    margin-bottom: 10px;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
  }
}

.account-wrap {
  margin-bottom: 26px;
  padding: 13px 0 16px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #A4CEFF;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.14);
  font-size: 12px;
  color: #666666;
  line-height: 24px;

  img {
    width: 78px;
    height: 78px;
    margin-bottom: 5px;
  }

  h3 {
    font-weight: bold;
    font-size: 16px;
    color: #333333;
  }

}

.account-content {
  padding: 16px 10px 30px 10px;
  box-shadow: 0px 0px 4px 1px rgba(0, 0, 0, 0.14);
  margin-bottom: 14px;

  .content-head {
    padding-bottom: 13px;
    border-bottom: 1px dashed #979797;
    font-size: 12px;
    color: #666666;
    line-height: 24px;
    text-align: right;

    h4 {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0 0 2px 2px;
      text-align: left;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
      font-weight: bold;
    }

    p {
      margin: 0 10px 0 0;
    }
  }

  .detail-icon {
    margin: 9px 0 0 7px;
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #666666;
    line-height: 20px;

    .detail-origin {
      margin-left: 10px;
      cursor: pointer;

      span {
        font-size: 12px;
        color: #247CFF;
      }

      img {
        width: 12px;
        margin-right: 2px;
        vertical-align: text-bottom;
      }
    }
  }
}

.spread-curp {
  cursor: pointer;
}

.spread-box {
  padding: 0 20px;

  .el-timeline {
    padding-left: 10px;
    font-size: 16px;
    color: #333;
  }
}

.spread-wrap {
  display: flex;
  align-items: center;

  .spread-icon {
    margin-right: 10px;
    width: 14px;
  }

  .spread-link {
    margin-right: 4px;
    width: 12px;
  }

  .spread-type {
    line-height: 14px;
    font-size: 12px;
    font-weight: 700;
    border: 1px solid #247CFF;
    background-color: #247CFF;
    color: #fff;
    padding: 5px;
    margin-right: 10px;
    border-radius: 4px;
  }
}

.hot-search {
  padding: 20px;

  .hot-wrap {
    padding: 16px 13px;
    background: #fff;
    box-shadow: 0px 0px 4px 1px rgba(0, 0, 0, 0.14);
    border-radius: 4px;
  }

  .hot-curp {
    div {
      cursor: pointer;
    }
  }

  .hot-box {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 500;
    font-size: 14px;
    color: #333;
    line-height: 20px;

    img {
      width: 13px;
      vertical-align: text-bottom;
      margin-right: 6px;

      &.img-link {
        margin-right: 2px;
      }
    }

    span {
      color: #666;
    }

    .search-words {
      color: #247CFF;
    }

    .hot-height {
      color: #FF2D26;
    }
  }
}

.hot-none {
  margin-top: 40px;
  text-align: center;

  img {
    margin-bottom: 20px;
  }
}
</style>
