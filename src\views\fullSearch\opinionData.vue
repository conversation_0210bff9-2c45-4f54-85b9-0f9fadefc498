<template>

  <div class="data-wrap">
    <div class="data-condition" v-show="showContent">
      <!-- <div class="condition-head-opinion">
          <div class="name">筛选条件 </div>
          <div class="retract" v-show="showContent" @click="showContent=!showContent"><i
                  class="el-icon-arrow-up"></i><span>条件收起</span></div>
          <div class="retract" v-show="!showContent" @click="showContent=!showContent"><i
                  class="el-icon-arrow-down"></i><span>条件展开</span></div>
      </div> -->
      <el-collapse-transition>
        <div v-show="showContent">
            <div class="name-list">
            <div class="time-content">
              时间范围：
              <DateRange ref="dataRangeRef" :isRepeat="queryForm.isOriginal" v-show="checkedNode.historyFlag=='0'"
                         :ivalue="timeList.initValue" :list="timeList.children" :isShowDate="true"
                         @date-change="dateRanges"  ></DateRange>

              <div v-show="checkedNode.historyFlag=='1'" class="date-wrap">
                <el-date-picker style="width:175px" ref="start" type="datetime" size="mini"
                                v-model="queryForm.startTime" disabled
                                :clearable="true" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="请输入开始时间"></el-date-picker>
                -
                <el-date-picker style="width:175px" ref="end" type="datetime" size="mini"
                                v-model="queryForm.endTime" disabled
                                value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss"
                                placeholder="请输入结束时间"></el-date-picker>
              </div>

            </div>
            <div class="list-content">
              排序方式：
              <el-radio-group v-model="queryForm.sort">
                <el-radio :disabled="queryForm.isOriginal==false && item.dictValue==7"
                          :label="item.dictValue" v-for="item in sortList" :key="item.dictValue">
                  <el-tooltip effect="light" v-if="queryForm.isOriginal==false && item.dictValue==7"
                              content="文章去重情况下可以点击" placement="top">
                    <span>{{item.dictLabel}}</span>
                  </el-tooltip>
                  <el-popover v-else-if="item.dictValue==10"
                              placement="bottom"
                              width="400"
                              trigger="hover">
                    <el-radio-group v-model="sortRadio" @change="changeSort" class="sort-num">
                      <el-radio :label="10">全部</el-radio>
                      <el-radio :label="11">转发数</el-radio>
                      <el-radio :label="12">评论数</el-radio>
                      <el-radio :label="13">点赞数</el-radio>
                    </el-radio-group>
                    <span slot="reference">互动数</span>
                  </el-popover>
                  <span v-else>{{item.dictLabel}}</span>
                </el-radio>
              </el-radio-group>
              <el-tooltip effect="light" content="信息内容相似的优先展示" placement="top">
                <img class="list-img" src="@/assets/images/quest.png" alt="" srcset="">
              </el-tooltip>
            </div>
          </div>
           <div v-show="showMore">
          <div class="name-list">
            <div class="list-content">
              信息属性：
              <Tabs :tabs="emotionData" ref="infoType" @checkAll="checkAll(1)"
                    v-model="queryForm.emotionFlag"/>
              <span class="desc">(可多选)</span>
            </div>

            <div class="list-content left-content">
                匹配方式：
                <el-radio-group v-model="queryForm.searchPosition">
                  <el-radio :label="0">按全文</el-radio>
                  <el-radio :label="1">按标题</el-radio>
                  <el-radio :label="2">按正文</el-radio>
                  <el-radio :label="3">按作者</el-radio>
                </el-radio-group>
              </div>
          </div>
            <div class="name-list">
              <!-- <div class="list-content left-content">
                  结果呈现： <el-radio-group v-model="queryForm.noSpam">
                      <el-radio :label="0">全部</el-radio>
                      <el-radio :label="1">正常信息</el-radio>
                      <el-radio :label="2">噪音信息</el-radio>
                  </el-radio-group>
                  <el-tooltip effect="light" content="特定的广告等数据会被判为噪音信息，例如营销账号频繁发布的小广告。" placement="top">
                      <img class="list-img" src="@/assets/images/quest.png" alt="" srcset="">
                  </el-tooltip>
              </div> -->
              <!-- 临时移动 -->
              <div class="list-content">
                定向信源：
                <el-radio-group v-model="queryForm.settingType">
                  <el-radio :label="0">不使用</el-radio>
                  <el-radio :label="1">定向信源</el-radio>
                  <el-radio :label="2">定向排除</el-radio>
                  <el-radio :label="3">仅使用</el-radio>
                  
                <el-tooltip effect="light" content="仅使用定向信源时方案设置不生效" placement="top">
                  <img class="list-img" src="@/assets/images/quest.png" alt="" srcset="">
                </el-tooltip>
                </el-radio-group>
              </div>
              <div class="list-content left-content">
                相似合并：
                <el-radio-group v-model="queryForm.isOriginal" @change="changeOriginal">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
                <el-tooltip effect="light" content="提供近一周内的信息合并" placement="top">
                  <img class="list-img" src="@/assets/images/quest.png" alt="" srcset="">
                </el-tooltip>
              </div>

            </div>

          </div>
           
            <div class="name-list">
              <div style="white-space: nowrap;">信源类型：</div>
              <check-box v-model="queryForm.type" :list="mediaList" :showNum="showNum"></check-box>
            </div>
             <div v-show="showMore">
            <div class="name-list" v-show="showVideo">
              短视频类型：
              <check-box v-model="queryForm.videoHost" :list="videoList"></check-box>
            </div>
            <div v-show="showWeiBo" style="display: flex;align-items: center;">
              <div class="name-list">
                微博内容：
                <!-- <check-box v-model="queryForm.contentForm" :list="contentList"></check-box> -->
                <el-popover
                  placement="bottom"
                  trigger="click"
                  @show="showPopover('weibo')">
                 <div style="display:flex;padding-top:10px">
                  微博内容：<check-box v-model="queryForm.contentForm" :list="contentList"></check-box>
                 </div>
                  <el-button type="text" slot="reference">{{weiboValue}}</el-button>
                </el-popover>
              </div>
              <div class="name-list">
                微博类型：
                <!-- <check-box v-model="queryForm.forward" :list="forwardList"></check-box> -->
                <el-popover
                  placement="bottom"
                  trigger="click"
                  @show="showPopover('forward')">
                 <div style="display:flex;padding-top:10px">
                  微博类型：<check-box v-model="queryForm.forward" :list="forwardList"></check-box>
                 </div>
                  <el-button type="text" slot="reference">{{forwardValue}}</el-button>
                </el-popover>
              </div>
              <div class="name-list">
                <!-- <div style="white-space: nowrap;">账号类型：</div> -->
                账号类型：
                <!-- <check-box v-model="queryForm.accountLevel" :list="accountList" :showNum="showAccount"></check-box> -->
                 <el-popover
                  placement="bottom"
                  trigger="click"
                  @show="showPopover('account')">
                 <div style="display:flex;padding-top:10px">
                  账号类型：<check-box v-model="queryForm.accountLevel" :list="accountList"></check-box>
                 </div>
                 <el-button type="text" slot="reference">{{accountValue}}</el-button>
                  <!-- <el-button type="text" slot="reference">
                    
                    <div v-for="(item,index) in accountValue" class="itemWrap">
                          {{ item.dictLabel }}
                          <span v-if="index!== accountValue.length - 1">、</span>
                          <div v-show="showAccount" class="itemNum">
                            <div>{{ item.dictLabel == '无' ?  null : item.number}}</div>
                          </div>
                    </div>
                  </el-button> -->
                </el-popover>
              </div>

              <div class="name-list-area">
                微博地域：
                <el-cascader :append-to-body="false" filterable v-model="queryForm.accountAreaCode"
                             :options="sourceAreaData" :props="optionProps" clearable
                             :show-all-levels="false" collapse-tags/>
              </div>
            </div>

          </div>
          <div class="name-list">
            <div class="moreButton" @click="showMore=!showMore">{{showMore?'收起':'更多'}}</div>
          </div>


          <div class="name-search">
            <el-button :loading="queryLoading" size="small" type="primary"
                       @click="submitSearch('query')">查询
            </el-button>
            <el-button v-show="checkedNode.historyFlag=='0'" size="small" type="primary" plain class="search-btns"
                       @click="saveFilter">
              保存条件
            </el-button>
          </div>
        </div>
      </el-collapse-transition>
    </div>
    <div class="devide"></div>
    <div class="data-table" ref="pride_tab_fixed">
      <div :class="titleFixed?'dataTot isFixed':'dataTot'">
        <!-- <div id="topAnchor" class="totalNum">
          <div v-if="totalLoading">
            刷新中
          </div>
          <div v-else>
            已有
            <span class="data-number">{{ showTotal || 0 }}</span>
            条数据更新
          </div>
          <i class="el-icon-refresh" @click="startInterval()"></i>
        </div> -->
        <div class="mt-4 dataSel">
                   
                    <span v-show="tableData.length>0">
                     <el-button v-if="!readLoading" type="text" primary style="margin-right:10px" @click="batchRead">批量已读</el-button>
                      <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;"
               class="el-icon-loading"></i>

                        <el-dropdown placement="bottom" ref="Dropdown" trigger="click">
                          <div>
                            <el-button v-if="!importLoading" type="text" style="margin-right: 10px;">批量导入至素材</el-button>
                            <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;" class="el-icon-loading"></i>
                          </div>
                          <el-dropdown-menu slot="dropdown" class="subClass">
                            <el-dropdown-item v-for="item in treeDataauto" :key="item.id" :command="item.id"
                              @mouseenter="() => {$refs.Dropdown.show()}">
                              <template v-if="!item.children">{{ item.folderName }}</template>
                              <template v-else>
                                <el-dropdown trigger="hover" placement="right-start" :show-timeout="1">
                                  <!-- 手动控制hover显示，解决鼠标移入二级菜单时一级菜单消失问题 -->
                                  <span class="el-dropdown-link" style="color: #606266;">
                                    {{ item.folderName }}<i v-if="item.children.length != 0"
                                      class="el-icon-arrow-right el-icon--right" />
                                  </span>
                                  <el-dropdown-menu slot="dropdown" class="menuClass">
                                    <el-dropdown-item v-for="subItem in item.children" :key="subItem.id" :command="subItem.id"
                                      @click.native="importToMaterials(subItem)">
                                      {{ subItem.folderName }}
                                    </el-dropdown-item>
                                  </el-dropdown-menu>
                                </el-dropdown>
                              </template>
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
               
                        <el-select size="small" v-model="exportNum" placeholder="选择导出条数" style="width: 125px"
                                   clearable
                                   @change="exportNumChange">
                            <el-option label="选择当前页" value="0"/>
                            <el-option label="前500条" value="500"/>
                            <el-option label="前1000条" value="1000"/>
                            <el-option label="前5000条" value="5000"/>
                        </el-select>
                      <!-- <i v-if="!downloadLoading" class="el-icon-bottom export-download" @click="exportExcel()"></i> -->
                        <img src="@/assets/images/exportIcon.png" v-if="!downloadLoading" class="exportImg"
                             @click="exportExcel()" alt="">
                        <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;"
                           class="el-icon-loading"></i>
                    </span>
          <span></span>
          <div class="dataSel-left">
            <div class="jump-page">
              <i class="el-icon-arrow-left" @click="goLeft"></i>
              <el-input-number size="mini" @change="submitSearch()" v-model="queryForm.pageNum"
                               :max="totalPage" :min="1" placeholder="请输入内容"></el-input-number>
              <span class="jump-line">/</span>
              <span>{{totalPage}}</span>
              <i class="el-icon-arrow-right" @click="goRight"></i>
            </div>
            <el-input v-model.trim="queryForm.quadraticWord" size="small" clearable
                      placeholder="在结果中搜索，支持单个词组"
                      class="input-with-select">
              <template #prepend>
                <el-select size="small" v-model="queryForm.quadraticPosition" placeholder="Select"
                           style="width: 90px">
                  <el-option label="按全文" :value="0"/>
                  <el-option label="按标题" :value="1"/>
                  <el-option label="按正文" :value="2"/>
                  <el-option label="按作者" :value="3"/>
                </el-select>
              </template>
            </el-input>
            <el-button size="small" :loading="searchLoading" icon="el-icon-search" type="primary"
                       class="search-btns" @click="submitSearch('search')">
              搜索
            </el-button>
          </div>
        </div>
      </div>
      <div class="dataTable">
        <el-table ref="tableRef" v-loading="tableLoading" :data="tableData" border style="width: 100%"
                  :class="titleFixed?'table-fiexd':''"
                  :header-cell-style="{background:'#fcfcfd'}" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center"></el-table-column>
          <el-table-column prop="title" label="标题" align="left" header-align="center">
            <template #default="scope">
              <div :class="scope.row.isRead==1?'tableItemTitle cover-column':'tableItemTitle'">
                <div class="tableTitle" @click="goDetail(scope.row)">
                  <img class="tableItemImg" :src="transImage(scope.row.type,scope.row.host||'')" alt="无图片"/>
                  <el-tooltip placement="top" effect="light" raw-content>
                    <div slot="content">
                      <div v-html="scope.row.title"></div>
                    </div>
                    <div class="tableTitleSpan">
                      <span>{{(queryForm.pageNum - 1) * queryForm.pageSize + scope.$index + 1}}. </span>
                      <span v-html="scope.row.title"></span>
                    </div>
                  </el-tooltip>
                  <el-select v-model="scope.row.emotionFlag"
                             :class="scope.row.emotionFlag==2?'emotionSelect table-nosense':scope.row.emotionFlag==1?'emotionSelect table-sense':'emotionSelect table-neutral'"
                             size="mini"
                             placeholder="请选择" @change="(val)=>{changeSensitive(val,scope.row)}">
                    <el-option :key="2" label="非敏感" :value="2"></el-option>
                    <el-option :key="1" label="敏感" :value="1"></el-option>
                    <el-option :key="0" label="中性" :value="0"></el-option>
                  </el-select>
                  <p class="article-type" style="background-color: #339593" v-show="scope.row.isOriginal">原创</p>
                  <p class="article-type" style="background-color: #FDED9A;color:#9C840C" v-show="scope.row.submits==1">
                    已预警</p>
                  <p class="article-type" v-show="!scope.row.isOriginal">转载</p>
                  <p class="article-type" style="background-color: #F7B8B3;color: #FA2C1C;"
                     v-show="scope.row.warned==1">流转中</p>
                  <p class="article-type" style="background-color: #B2E8F3;color: #00B4D8;" v-show="scope.row.deal==1">
                    已处置</p>
                  <p class="article-type" style="background-color: #D8D8D8;color: #999999;"
                     v-if="scope.row.urlAccessStatus==0">已删除</p>
                  <p class="article-type" style="background-color: #D5F8D1;color: #3EC82F;" v-else>可访问</p>

                  <el-select v-model="scope.row.riskGrade"
                              style="margin-left: 10px;"
                             :class="['dangerSelect',scope.row.riskGrade==2?'table-nosense':scope.row.riskGrade==1?'table-sense':'table-neutral']"
                             size="mini"
                             placeholder="暂无" @change="(val)=>{changeRiskGrade(val,scope.row)}">
                    <el-option :key="2" label="低" :value="2"></el-option>
                    <el-option :key="0" label="中" :value="0"></el-option>
                    <el-option :key="1" label="高" :value="1"></el-option>
                  </el-select>
                  <p v-for="item in scope.row.contentMeta" class="article-type" style="background-color: #ECF5FF;color: #409EFF;" >
                    {{item}}
                  </p>
                </div>
                <div class="tableMain" v-html="scope.row.text" @click="goDetail(scope.row)"></div>
                <div class="tableFoot">
                  <div class="footInfo">
                    <el-tooltip effect="light" content="评论数" placement="top">
                      <img src="@/assets/images/message.png" alt="" class="footIcon"/>
                    </el-tooltip>
                    <span>{{scope.row.commentNum || 0}}</span>
                  </div>
                  <div class="footInfo">
                    <el-tooltip effect="light" content="阅读数" placement="top">
                      <img src="@/assets/images/book.png" alt="" class="footIcon"/>
                    </el-tooltip>
                    <span>{{scope.row.readNum || 0}}</span>
                  </div>
                  <div class="footInfo">
                    <el-tooltip effect="light" content="点赞数" placement="top">
                      <img src="@/assets/images/good.png" alt="" class="footIcon"/>
                    </el-tooltip>
                    <span>{{scope.row.likeNum || 0}}</span>
                  </div>
                  <div class="footInfo">
                    <el-tooltip effect="light" content="转发数" placement="top">
                      <img src="@/assets/images/share.png" alt="" class="footIcon"/>
                    </el-tooltip>
                    <span>{{scope.row.reprintNum || 0}}</span>
                  </div>
                  <div class="footButtonGroup">
                    <div>
                      <div class="footButonItem" v-show="scope.row.hitWords">
                        <el-tooltip effect="light" content="涉及词" placement="top">
                          <img src="@/assets/images/keyword.png" alt="" class="footIcon"/>
                        </el-tooltip>
                        <el-tooltip effect="light" :content="scope.row.hitWords"
                                    placement="top">
                          <span class="keyword">{{ scope.row.hitWords || '' }}</span>
                        </el-tooltip>
                      </div>
                      <div class="footButonItem" v-show="scope.row.hitCourtNames">
                        <el-tooltip effect="light" content="涉及法院" placement="top">
                          <img src="@/assets/images/court.png" alt="" class="footIcon"/>
                        </el-tooltip>
                        <el-tooltip effect="light" :content="scope.row.hitCourtNames"
                                    placement="top">
                          <span class="keyword" style="color: #247CFF;">{{ scope.row.hitCourtNames || '' }}</span>
                        </el-tooltip>
                      </div>
                      <div class="footButonItem" v-show="scope.row.contentAreaCodeName">
                        <el-tooltip effect="light" content="精准地域" placement="top">
                          <img src="@/assets/images/areaDetail.png" alt="" class="footIcon"/>
                        </el-tooltip>
                        <el-tooltip effect="light" :content="scope.row.contentAreaCodeName"
                                    placement="top">
                          <span class="keyword" style="color: #356391;">{{ scope.row.contentAreaCodeName || '' }}</span>
                        </el-tooltip>
                      </div>
                    </div>

                    <div style="white-space: nowrap;">
                      <!-- <div  class="footButonItem">
                          <img src="@/assets/images/file.png" alt="" class="footIcon" />
                          <span>素材</span>
                      </div>
                      <div  class="footButonItem">
                          <img src="@/assets/images/translate.png" alt="" class="footIcon" />
                          <span>翻译</span>
                      </div> -->
                      <div class="footButonItem" @click="copyAritical(scope.row)">
                        <img src="@/assets/images/copy.png" alt="" class="footIcon"/>
                        <span>复制</span>
                      </div>
                      <el-dropdown placement="bottom" ref="Dropdown" trigger="click">
                        <div class="footButonItem">
                          <img src="@/assets/images/add-material.png" alt="" class="footIcon"/>
                          <span>添加</span>
                        </div>
                        <el-dropdown-menu slot="dropdown" class="subClass">
                          <el-dropdown-item v-for="item in treeDataauto" :key="item.id" :command="item.id"
                                            @mouseenter="() => {$refs.Dropdown.show()}">
                            <template v-if="!item.children">{{ item.folderName }}</template>
                            <template v-else>
                              <el-dropdown trigger="hover" placement="right-start" :show-timeout="1">
                                <!-- 手动控制hover显示，解决鼠标移入二级菜单时一级菜单消失问题 -->
                                <span class="el-dropdown-link" style="color: #606266;">
                                                                                    {{ item.folderName }}<i
                                  v-if="item.children.length != 0" class="el-icon-arrow-right el-icon--right"/>
                                                                            </span>
                                <el-dropdown-menu slot="dropdown" class="menuClass">
                                  <el-dropdown-item v-for="subItem in item.children" :key="subItem.id"
                                                    :command="subItem.id" @click.native="subFolder(subItem,scope.row)">
                                    {{ subItem.folderName }}
                                  </el-dropdown-item>
                                </el-dropdown-menu>
                              </el-dropdown>
                            </template>

                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>

                      <el-dropdown placement="bottom" ref="collectionDropdown" trigger="click">
                        <div class="footButonItem">
                          <img src="@/assets/images/heart.png" alt="" class="footIcon"/>
                          <span>收藏</span>
                        </div>
                        <el-dropdown-menu slot="dropdown" class="subClass">
                          <el-dropdown-item v-for="item in collectionTree" :key="item.id" :command="item.id"
                                            @mouseenter="() => {$refs.collectionDropdown.show()}">
                            <template v-if="!item.children">{{ item.folderName }}</template>
                            <template v-else>
                              <el-dropdown trigger="hover" placement="right-start" :show-timeout="1">
                                <!-- 手动控制hover显示，解决鼠标移入二级菜单时一级菜单消失问题 -->
                                <span class="el-dropdown-link" style="color: #606266;">
                                                                                    {{ item.folderName }}<i
                                  v-if="item.children.length != 0" class="el-icon-arrow-right el-icon--right"/>
                                                                            </span>
                                <el-dropdown-menu slot="dropdown" class="menuClass">
                                  <el-dropdown-item v-for="subItem in item.children" :key="subItem.id"
                                                    :command="subItem.id"
                                                    @click.native="subCollectionFolder(subItem,scope.row)">
                                    {{ subItem.folderName }}
                                  </el-dropdown-item>
                                </el-dropdown-menu>
                              </el-dropdown>
                            </template>

                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>

                      <el-dropdown v-if="roles.includes('guanli')||roles.includes('fenxishi')">
                        <div class="footButonItem">
                          <img src="@/assets/images/send.png" alt="" class="footIcon"/>
                          <span>报送</span>
                        </div>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item @click.native="openSendMsg(scope.row)">短信报送</el-dropdown-item>
                          <el-dropdown-item @click.native="openSendSystem(scope.row)">系统报送</el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                      <div class="footButonItem" @click="openSendMsg(scope.row)" v-else>
                        <img src="@/assets/images/send.png" alt="" class="footIcon"/>
                        <span>报送</span>
                      </div>
                      <!-- <div  class="footButonItem">
                          <img src="@/assets/images/repeat.png" alt="" class="footIcon" />
                          <span>重复</span>
                      </div> -->
                      <!-- <div  class="footButonItem">
                          <img src="@/assets/images/alert.png" alt="" class="footIcon" />
                          <span>预警</span>
                      </div> -->
                      <!-- <el-dropdown>
                          <div  class="footButonItem">
                              <img src="@/assets/images/noise.png" alt="" class="footIcon" />
                              <span>噪音</span>
                          </div>
                          <el-dropdown-menu  slot="dropdown">
                              <el-dropdown-item @click.native="markNoise(scope.row)">{{scope.row.isSpam?'取消噪音':'噪音'}}</el-dropdown-item>
                          </el-dropdown-menu>
                      </el-dropdown> -->
                      <div class="footButonItem" @click="goOrigin(scope.row.url)">
                        <img src="@/assets/images/goOrigin.png" alt="" class="footIcon"/>
                        <span>查看原文</span>
                      </div>
                      <div class="footButonItem" @click="copyText(scope.row.url,true)">
                        <img src="@/assets/images/copyLink.png" alt="" class="footIcon"/>
                        <span>拷贝地址</span>
                      </div>
                      <el-dropdown>
                        <div class="footButonItem">
                          <img src="@/assets/images/filterInfo.png" alt="" class="footIcon"/>
                          <span>过滤信息</span>
                        </div>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item @click.native="filterOne(scope.row)">过滤单条</el-dropdown-item>
                          <el-dropdown-item @click.native="filterWeb(scope.row)">过滤站点</el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                      <el-dropdown>
                        <div class="footButonItem">
                          <img src="@/assets/images/markIcon.png" alt="" class="footIcon"/>
                          <span>标记</span>
                        </div>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item @click.native="markDisposition(scope.row)">
                            {{scope.row.deal==1?'取消处置':'处置'}}
                          </el-dropdown-item>
                          <el-dropdown-item @click.native="markKeyFocus(scope.row)">
                            {{scope.row.follow==1?'取消重点关注':'重点关注'}}
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </div>
                  </div>
                </div>
              </div>
              <img class="read-img" v-if="scope.row.isRead==1" src="@/assets/images/read.png" alt="">
              <img class="follow-img" v-if="scope.row.follow==1" src="@/assets/images/follow.png" alt="">
            </template>
          </el-table-column>
          <el-table-column prop="count" label="相似信息" align="center" width="100px">
            <template #default="scope">
              <div style="cursor: pointer;" @click="goSimilar(scope.row)">{{ scope.row.count }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="nickname" label="来源" align="center" width="100px">
            <template #default="scope">
              <div style="cursor: pointer;" @click="goHomepage(scope.row)">
                <div v-show="scope.row.typeName!='短视频'">{{ scope.row.typeName }}</div>
                <div>{{ scope.row.host }}</div>
                <div v-show="scope.row.typeName=='短视频'">{{ scope.row.author }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="publishTime" align="center" width="100px">
            <template slot="header">
              时间
              <span class="sortIconGroup">
                                <i :class="`el-icon-caret-top ${queryForm.sort=='4'?'active':''}`"
                                   @click="sortChange('4')"></i>
                                <i :class="`el-icon-caret-bottom ${queryForm.sort=='3'?'active':''}`"
                                   @click="sortChange('3')"></i>
                            </span>
            </template>
            <template slot-scope="scope">
              <div>{{ scope.row.publishTime.substring(0,10) }}</div>
              <div>{{ scope.row.publishTime.substring(11,19) }}</div>
            </template>
          </el-table-column>
        </el-table>
        <div v-show="tableData.length>0"
             style="width:100%;min-height: 20px;display:flex;align-items:center;justify-content: space-between;">
          <div style="margin-top:10px;">

            <el-button v-if="!readLoading" type="text" primary style="margin-right:10px" @click="batchRead">批量已读</el-button>
            <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;"
               class="el-icon-loading"></i>

            <el-dropdown placement="bottom" ref="Dropdown" trigger="click">
              <div>
                <el-button v-if="!importLoading" type="text" style="margin-right: 10px;">批量导入至素材</el-button>
                <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;" class="el-icon-loading"></i>
              </div>
              <el-dropdown-menu slot="dropdown" class="subClass">
                <el-dropdown-item v-for="item in treeDataauto" :key="item.id" :command="item.id"
                  @mouseenter="() => {$refs.Dropdown.show()}">
                  <template v-if="!item.children">{{ item.folderName }}</template>
                  <template v-else>
                    <el-dropdown trigger="hover" placement="right-start" :show-timeout="1">
                      <!-- 手动控制hover显示，解决鼠标移入二级菜单时一级菜单消失问题 -->
                      <span class="el-dropdown-link" style="color: #606266;">
                        {{ item.folderName }}<i v-if="item.children.length != 0"
                          class="el-icon-arrow-right el-icon--right" />
                      </span>
                      <el-dropdown-menu slot="dropdown" class="menuClass">
                        <el-dropdown-item v-for="subItem in item.children" :key="subItem.id" :command="subItem.id"
                          @click.native="importToMaterials(subItem)">
                          {{ subItem.folderName }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <el-select v-model="exportNum" size="small" placeholder="选择导出条数" style="width: 125px" clearable
                       @change="exportNumChange">
              <el-option label="选择当前页" value="0"/>
              <el-option label="前500条" value="500"/>
              <el-option label="前1000条" value="1000"/>
              <el-option label="前5000条" value="5000"/>
            </el-select>
            <!-- <i v-if="!downloadLoading" class="el-icon-bottom export-download" @click="exportExcel()"></i> -->
            <img src="@/assets/images/exportIcon.png" v-if="!downloadLoading" class="exportImg"
                 @click="exportExcel()" alt="">
            <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;"
               class="el-icon-loading"></i>
          </div>
          <pagination :total="total" :page.sync="queryForm.pageNum"
                      :limit.sync="queryForm.pageSize" @pagination="pagination"/>

        </div>
      </div>
    </div>
    <el-dialog title="加入历史事件" :visible.sync="toHistoryVisible" width="30%">
      <el-form label-width="100px">
        <el-form-item label="设置方案名：">
          <el-input v-model.trim="newPlanName" placeholder="请输入方案名" style="width: 100%"/>
          <div class="formTip">
            *方案名的字数请尽量控制在50个字以内
          </div>
        </el-form-item>
      </el-form>
      <span>当前加入的方案时间范围是 {{ timeRange.startTime }} 至 {{ timeRange.endTime }} ，确认加入历史事件？</span>
      <template #footer>
        <div style="text-align: center;">
          <el-button type="primary" @click="submitToHistory">确定</el-button>
          <el-button @click="toHistoryVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <SendMsg :visible.sync="sendMsgDialog" :sendMsgRow="sendMsgRow" @visibleChange="visibleChange"></SendMsg>
  </div>

</template>
<script>
import moment from 'moment'
import DateRange from "@/components/DateRange/index";
import Tabs from "@/components/TabMulty";
import CheckBox from "@/components/CheckBox";
import {resetTag, transImage, copyText, replaceHtml, copyAritical, goHomepage,getDomain} from '@/utils/index';
import {
  hotWordAdd,
  hotWordList,
  searchData,
  searchPlan,
  areaTree,
  similarCount,
  updateEmotion,
  searchRead,
  realTimeInfoCount,
  searchExport,
  saveSearchCriteria,
  getSearchCriteria,
  getUrlAccessStatusApi,
  updateTrash,
  updateDeal,
  updateFollow,
  insertFilterInfoApi,
  getMediaTypeCount,
  accountLevelCount,
  readManyApi,
  updateRiskApi
} from "@/api/search/index";
import SendMsg from '@/views/fullSearch/components/sendMsg.vue';
import {addSourceSetting} from "@/api/publicOpinionMonitor/index.js";
import {getFolderList, addFolder, addBatchFolder} from '@/api/report/material.js'
import {getCollectList, addCollectionFolder} from '@/api/system/collect.js'

export default {
  components: {Tabs, CheckBox, DateRange, SendMsg},
  data() {
    return {
      tooltipVisible: false,
      searchNum: 0,
      sortRadio: 10,
      titleFixed: false,
      headerTop: 800,
      transImage,
      goHomepage,
      allTotal: 0,
      intervalId: null,
      optionProps: {
        multiple: false,
        value: 'id',
        label: 'name',
        children: 'children',
        emitPath: false,
        checkStrictly: true
      },
      sourceAreaData: [],
      resetTag,
      radio: '',
      list: [],
      timeType: 'fixed',
      timeList: {
        children: [{label: '今天', value: 0}, {label: '24小时', value: 1}, {label: '2天', value: 2}, {
          label: '3天',
          value: 3
        }, {label: '7天', value: 7}],
        initValue: 0
      },
      emotionData: [{name: '中性', value: '0', tag: false}, {name: '敏感', value: '1', tag: false}, {
        name: '非敏感',
        value: '2',
        tag: false
      }],
      queryLoading: false,
      searchLoading: false,
      queryForm: {
        planId: '',
        pageNum: 1,
        pageSize: 10,
        quadraticWord: '',
        startTime: undefined,
        endTime: undefined,
        isOriginal: false,
        sort: 3,
        emotionFlag: '1',
        noSpam: 0,
        type: [],
        searchPosition: 0,
        quadraticPosition: 0,
        videoHost: [],
        contentForm: [],
        forward: [],
        accountLevel: [],
        accountAreaCode: '',
        timeIndex: undefined,
        settingType: 0
      },
      sortList: [],
      mediaList: [],
      contentList: [],
      forwardList: [],
      accountList: [],
      videoList: [],
      showTotal: 0,
      totalLoading: false,
      showContent: false,
      exportNum: null,
      downloadLoading: false,
      readLoading: false,
      importLoading: false,
      total: 0,
      multipleSelection: {selectedRows: []},
      tableLoading: false,
      tableData: [],
      totalPage: 0,
      showVideo: true,//显示视频筛选项
      showWeiBo: true,//显示微博筛选项
      sendMsgDialog: false,//短信报送弹窗
      sendMsgRow: {},//短信报送对象的信息

      timeRange: {},
      toHistoryVisible: false,
      showNum: false,
      showAccount: false,
      treeDataauto: [],

      newPlanName: '',
      filterNodeId: null,//记录本次请求筛选条件的节点id，与筛选条件id不同，需要重新请求筛选条件（作用：只请求第一次筛选条件）
      showMore: false,
      collectionTree: [],// 收藏树
      weiboValue: '',
      forwardValue: '',
      accountValue: '',
    }
  },
  props: {
    treeFlag: {type: Boolean, default: true},
    checkedNode: {
      type: Object,
      default: () => {
      },
    },
    show: {
      type: Boolean,
      default: false,

    },
    fold: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    checkedNode(newVal, oldVal) {
      if (newVal && this.show) {//当前tab显示时，树节点改变才刷新
        this.$nextTick(() => {
          setTimeout(() => {
            this.init()
          }, 200);
        });
      }
    },
    queryForm: {
      handler(newVal, oldVal) {
        this.$emit('changeSearchForm', newVal)
        if (newVal.type.includes('3')) {
          this.showWeiBo = true
        } else {
          this.showWeiBo = false
        }
        if (newVal.type.includes('11')) {
          this.showVideo = true
        } else {
          this.showVideo = false
        }
      },
      immediate: true,
      deep: true // 可以深度检测到对象的属性值的变化
    },
    'queryForm.contentForm': {
      handler(newVal) {
           this.weiboValue = this.getLabelValue(newVal, this.contentList);
      },
      immediate: true
    },
    'queryForm.forward': {
      handler(newVal) {
          this.forwardValue = this.getLabelValue(newVal, this.forwardList);
      },
      immediate: true
    },
    'queryForm.accountLevel': {
      handler(newVal) {
          this.accountValue = this.getLabelValue(newVal, this.accountList);
      },
      immediate: true
    },
    treeFlag: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.handleScroll(1)
          }, 0);
        })
      },
      immediate: true,
    },
    fold: {
      handler(newVal) {
        this.$nextTick(() => {
          this.showContent = !this.fold
        })
      },
      immediate: true,

    }
  },
  computed: {
    roles() {
      return this.$store.getters.roles
    }
  },
  mounted() {
    this.titleFixed = false;
    this.$nextTick(() => {
      document.addEventListener('scroll', this.handleScroll, true);
    })
  },
  async created() {
    await this.dateRanges({type: 'fixed', date: 0})
    await this.querySysList()
    this.queryArea()
    this.queryForm.planId = this.checkedNode.id
    this.getFilter()
    this.getTreeData()
    this.getCollectionTreeData()
  },
  beforeDestroy() {
    // 当组件即将被销毁时停止间隔
    this.stopInterval();
    document.removeEventListener('scroll', this.handleScroll, true);
  },
  methods: {
    showPopover(name) {
      const map = {
        weibo: {
          condition: this.weiboValue === '全部',
          target: 'contentForm',
          source: this.contentList
        },
        account: {
          condition: this.accountValue === '全部',
          target: 'accountLevel',
          source: this.accountList
        },
        forward: {
          condition: this.forwardValue === '全部',
          target: 'forward',
          source: this.forwardList
        },
      };

      const config = map[name];
      if (config && config.condition) {
      this.queryForm[config.target] = config.source.map(item => item.dictValue);
      }
    },
    getLabelValue(newVal, list) {
      const allValues = list.map(item => item.dictValue);
        if (newVal.length === 0 || newVal.length === allValues.length) {
          return '全部';
        } else {
          const labels = newVal.map(value => {
          const item = list.find(item => item.dictValue === value);
          return item ? item.dictLabel : null;
          }).filter(label => label !== null);
          return labels.length > 0 ? labels.join('、') : '无';
        }
      },
      // 账号
      getAccountValue(newVal, list) {
        const allValues = list.map(item => item.dictValue);
        if (newVal.length === allValues.length) {
            const totalNumber = list.reduce((sum, item) => sum + item.number, 0);
            return [
                {
                    dictLabel: "全部",
                    number: totalNumber
                }
            ];

        } else {
          const labels = newVal.map(value => {
            const item = list.find(item => item.dictValue === value);
            return item ? {
                dictLabel: item.dictLabel,
                number: item.number
            } : null;
          }).filter(label => label!== null);
            return labels.length > 0 ? labels : [
                {
                    dictLabel: "无",
                    number: 0
                }
            ];
        }
      },
    // 获取素材树
    async getTreeData() {
      let resauto = await getFolderList()
      this.treeDataauto = resauto.data
    },
    // 批量导入至素材
    importToMaterials(item) {
      let req = []
      if (this.multipleSelection.selectedRows.length > 0) { //勾选导出
        req = this.multipleSelection.selectedRows.map(itemrow => {
          const { id, ...rest } = itemrow;
          return { ...rest, contentId: id, folderId: item.id };
        })
      } else {
        this.$message.warning('请选择导入项')
        return
      }

      this.importLoading = true
      this.$confirm('是否确认导入至素材?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return addBatchFolder(req);
      }).then(response => {
        this.importLoading = false
        this.$message.success('导入成功')
      }).catch(() => {
        this.importLoading = false
      })
    },
    async subFolder(item, row) {
      const newitem = JSON.parse(JSON.stringify(row))
      delete newitem.id
      newitem.contentId = row.id
      const params = {...newitem, folderId: item.id}
      const res = await addFolder(params)
      if (res.code == 200) {
        this.$message.success('添加成功')
      }
    },
    changeSort(val) {
      this.queryForm.sort = '10'
    },
    //滚动监听，头部固定
    handleScroll(val) {
      // if (this.tableData.length > 0) {
      //     const tabFixed =  this.$refs.pride_tab_fixed
      //     let offsetTop = tabFixed.getBoundingClientRect().top;
      //     this.titleFixed = offsetTop < 0;
      //     if (this.titleFixed) {
      //         this.$nextTick(() => {
      //             setTimeout(() => {
      //                 const dom  =  document.querySelector('.isFixed')
      //                 const tableDom = document.querySelector('.el-table__header-wrapper')
      //                 if (dom) {
      //                     if (tabFixed.offsetWidth - dom.offsetWidth != 20||val) {
      //                         dom.style.width = `${tabFixed.offsetWidth - 40}px`
      //                         tableDom.style.width = `${tabFixed.offsetWidth - 40}px`
      //                     }
      //                 }
      //             }, 200);
      //         })

      //     }
      // }else{
      //     this.titleFixed = false
      // }


      if (this.tableData.length > 0) {
        const tabFixed = this.$refs.pride_tab_fixed
        let offsetTop = tabFixed.getBoundingClientRect().top;

        this.titleFixed = offsetTop < document.querySelector('.fixed-header').offsetHeight;
        // this.titleFixed = offsetTop < document.querySelector('.fixed-header').offsetHeight;

        const marginDom = document.querySelector('.dataTable')

        if (this.titleFixed) {
          // this.$nextTick(() => {
          const dom = document.querySelector('.isFixed')
          const tableDom = document.querySelector('.el-table__header-wrapper')
          const dataTotDom = document.querySelector('.dataTot')
          setTimeout(() => {
            if (dom) {
              if (tabFixed.offsetWidth - dom.offsetWidth != 20 || val) {
                dom.style.width = `${tabFixed.offsetWidth - 40}px`
                tableDom.style.width = `${tabFixed.offsetWidth - 40}px`
              }
            }
          }, 10);
          if (marginDom && dataTotDom) {
            marginDom.style.marginTop = `${tableDom.offsetHeight + dataTotDom.offsetHeight}px`
          }
          // })
        } else {
          marginDom.style.marginTop = 0
        }
      } else {
        this.titleFixed = false
      }
    },
    // 初始化
    async init() {
      this.queryForm.planId = this.checkedNode.id
      await this.getFilter()
      this.submitSearch()
    },
    //时间段完整性校验
    checkTime() {
      let flag = true
      if (this.timeType != 'fixed') {
        if (!this.queryForm.startTime || !this.queryForm.endTime) {
          flag = false
        }
      }
      return flag
    },
    sortChange(sort) {
      this.queryForm.sort = sort
      this.submitSearch()
    },
    changeOriginal(val) {
      if (val == false && this.queryForm.sort == 7) {
        this.queryForm.sort = this.sortList[0].dictValue
      }
      if (this.$refs['dataRangeRef'].date == '999') {
        this.timeType = 'fixed'
        this.queryForm.timeIndex = 0
        this.$nextTick(() => {
          setTimeout(() => {
            this.dateRanges({type: 'fixed', date: 0})
            this.$refs['dataRangeRef'].setParam(0)
          }, 100);
        })
      }
    },
    goRight() {
      if (this.totalPage > this.queryForm.pageNum && this.totalPage > 0) {
        this.queryForm.pageNum++
        this.submitSearch()
      }
    },
    goLeft() {
      if (this.totalPage >= this.queryForm.pageNum && this.queryForm.pageNum > 1) {
        this.queryForm.pageNum--
        this.submitSearch()
      }
    },
    async enrichArrayWithDetails(arrayA) {
      // 使用map创建一个Promise数组
      const promises = arrayA.map(async item => {
        if (this.queryForm.isOriginal != true && this.queryForm.sort != 7) {
          // const detail = await similarCount({ md5: item.md5 });
          return {...item, count: 1};
        } else {
          return {...item, count: item.similarCount};
        }
      });
      const enrichedArray = await Promise.all(promises);
      return enrichedArray;
    },

    // 获取搜索字段列表
    async querySysList() {
      // 获取排序方式
      try {
        let res = await this.getDicts('sys_search_sort')
        this.sortList = res.data
        // this.queryForm.sort = this.sortList[0].dictValue
      } catch (error) {
        this.$message.error(error)
      }
      // 获取媒体类型
      try {
        let res = await this.getDicts('sys_media_type')

        let rseult = JSON.parse(JSON.stringify(res.data))
        this.mediaList = rseult.map((item) => {
          item.number = 0
          return item
        })
         if(this.queryForm.type.length == 0){
            this.queryForm.type = this.mediaList.map((item) => item.dictValue)
          }
        // let choseList = this.mediaList.map((item) => item.dictValue)
        // this.queryForm.type = choseList
      } catch (error) {
        this.$message.error(error)
      }
      // 微博类型
      this.forwardList = [{dictLabel: '原创微博', dictValue: 1}, {dictLabel: '转发微博', dictValue: 2}]
      // let choseListforward = this.forwardList.map((item) => item.dictValue)
      // this.queryForm.forward = choseListforward
      // 获取微博内容
      try {
        let res = await this.getDicts('sys_search_content_form')
        this.contentList = res.data
        // 初始化 watch 逻辑
        this.$watch('queryForm.contentForm', {
          handler: (newVal) => {
            this.weiboValue = this.getLabelValue(newVal, this.contentList);
          },
          immediate: true
        });
        this.$watch('queryForm.forward', {
          handler: (newVal) => {
            this.forwardValue = this.getLabelValue(newVal, this.forwardList);
          },
          immediate: true
        });
        // let choseList = this.contentList.map((item) => item.dictValue)
        // this.queryForm.contentForm = choseList
      } catch (error) {
        this.$message.error(error)
      }
      // 获取账号类型
      try {
        let res = await this.getDicts('sys_search_account_level')
        let rseult = JSON.parse(JSON.stringify(res.data))
        this.accountList = rseult.map((item) => {
          item.number = 0
          return item
        })
        // 初始化 watch 逻辑
         this.$watch('queryForm.accountLevel', {
          handler: (newVal) => {
            this.accountValue = this.getLabelValue(newVal, this.accountList);
          },
          immediate: true
        });
        // let choseList = this.accountList.map((item) => item.dictValue)
        // this.queryForm.accountLevel = choseList
      } catch (error) {
        this.$message.error(error)
      }
      // 获取短视频
      try {
        let res = await this.getDicts('sys_search_short_video')
        this.videoList = res.data
        
        if(this.queryForm.videoHost.length == 0){
          this.queryForm.videoHost = this.videoList.map((item) => item.dictValue)
        }
        // let choseList = this.videoList.map((item) => item.dictValue)
        // this.queryForm.videoHost = choseList
      } catch (error) {
        this.$message.error(error)
      }
    },
    // 获取微博地域
    queryArea() {
      areaTree('0', 2).then((res) => {
        this.sourceAreaData = res.data
        this.sourceAreaData.push({name: '其他', id: '999999'})
      })
    },


    // 获取实时数据刷新
    async queryTimeInfo() {
      let params = JSON.parse(JSON.stringify(this.queryForm))
      // params.accountLevel = params.accountLevel.join(',')
      params.contentForm = params.contentForm.join(',')
      params.forward = params.forward.join(',')
      params.type = params.type.join(',')
      params.videoHost = params.videoHost.join(',')
      if (params.sort == 10) {
        params.sort = this.sortRadio
      }
      this.totalLoading = true
      let res = await realTimeInfoCount(params)
      this.allTotal = res.data
      if (this.allTotal > this.total) {
        this.showTotal = this.allTotal - this.total
      } else {
        this.showTotal = 0
      }
      this.totalLoading = false
    },
    startInterval() {
      // 清除之前的间隔（如果有的话）
      if (this.intervalId) {
        clearInterval(this.intervalId);
      }
      this.queryTimeInfo()
      // 每隔一分钟（60000毫秒）调用queryTimeInfo方法
      this.intervalId = setInterval(() => {
        this.queryTimeInfo()
      }, 60000)
    },
    stopInterval() {
      // 清除间隔
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },
    // 切换敏感类型
    async changeSensitive(val, row) {
      let res = await updateEmotion({md5: row.md5, emotionFlag: val})
      this.$set(row, 'emotionFlag', val);
      if (res.code == 200) {
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'emotionFlag', row.originFlag);
        this.$message.error(res.msg)
      }
    },
    // 切换风险等级
    async changeRiskGrade(val, row) {
      let res = await updateRiskApi({md5: row.md5, riskGrade: val})
      this.$set(row, 'riskGrade', val);
      if (res.code == 200) {
        this.$message.success('操作成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 信息属性全选
    checkAll(item) {
      switch (item) {
        case 1:
          this.resetTag(this.emotionData);
          break;
      }
    },

    // 切换时间
    async dateRanges(param) {
      this.timeType = param.type
      switch (param.type) {
        case 'fixed':
          this.queryForm.timeIndex = param.date
          this.queryForm.startTime = undefined
          this.queryForm.endTime = undefined
          break
        case 'dymatic':
          this.queryForm.timeIndex = param.date
          this.queryForm.startTime = undefined
          this.queryForm.endTime = undefined
          break
        case 'start':
          this.queryForm.startTime = param.date
          break
        case 'end':
          this.queryForm.endTime = param.date
          break
        default:
          break
      }
    },
    async batchRead(){
       const req = {}
     if(this.multipleSelection.selectedRows.length > 0){
        const ids = this.multipleSelection.selectedRows.map(item => item.id)
        req.ids = ids
     } else {
        this.$message.warning('请先选择数据')
        return
      }
       this.readLoading = true
        this.$confirm('是否确认已读所有选中数据项?', "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function () {
         return readManyApi(req)
        }).then(response => {
          this.readLoading = false
            req.ids.forEach(id => {
                const matchedObj = this.tableData.find(obj => obj.id == id);
                if (matchedObj) {
                  this.$set(matchedObj, 'isRead', 1);   
                }
            });
            
          this.$message.success('操作成功')
        }).catch(() => {
          this.readLoading = false
        })

    
    },
    //导出条数变动后的table选中项改变
    exportNumChange(val) {
      this.$refs.tableRef.clearSelection()
      if (val) {
        this.$refs.tableRef.toggleAllSelection()
      }
    },
    // 列表导出
    exportExcel(id) {
      let params = JSON.parse(JSON.stringify(this.queryForm))
      // params.accountLevel = params.accountLevel.join(',')
      params.contentForm = params.contentForm.join(',')
      params.forward = params.forward.join(',')
      params.type = params.type.join(',')
      params.videoHost = params.videoHost.join(',')
      if (params.sort == 10) {
        params.sort = this.sortRadio
      }
      const req = {...params}
      if (this.exportNum) { //选项导出
        if (this.exportNum !== '0') {
          req.pageNum = 1
          req.pageSize = parseInt(this.exportNum)
        }
      } else if (this.multipleSelection.selectedRows.length > 0) { //勾选导出
        const ids = this.multipleSelection.selectedRows.map(item => item.id)
        req.ids = ids
      } else {
        this.$message.warning('请选择导出条数或导出项')
        return
      }
      this.downloadLoading = true
      this.$confirm('是否确认导出所有类型数据项?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return searchExport(req);
      }).then(response => {
        this.downloadLoading = false
        this.$message.success('导出成功')
        this.download(response.msg);
      }).catch(() => {
        this.downloadLoading = false
      })
    },
    // 查询列表
    async submitSearch(type) {
      this.searchNum++
      if (!this.checkedNode.id) {
        return
      }
      if (type == 'search') {
        this.queryForm.pageNum = 1
      }

      // type- search-搜索 query-空：查询 空-翻页、刷新
      if (this.timeType != 'fixed') {
        if (!this.queryForm.startTime || !this.queryForm.endTime) {
          this.$message.error('请输入完整的时间范围')
          return
        }
      }
      this.exportNum = ''//翻页时将导出页数选择器重置
      try {
        this.tableLoading = true
        if (type == 'search') { //点击翻页触发该方法
          this.searchLoading = true
        } else if (type == 'query') {
          this.queryLoading = true
        }
        if(this.queryForm.type.length == 0){
          this.queryForm.type = this.mediaList.map(item=>item.dictValue)
        }
        if(this.queryForm.videoHost.length == 0){
          this.queryForm.videoHost = this.videoList.map(item=>item.dictValue)
        }
        let params = JSON.parse(JSON.stringify(this.queryForm))
        // params.accountLevel = params.accountLevel.join(',')
        params.contentForm = params.contentForm.join(',')
        params.forward = params.forward.join(',')
        params.type = params.type.join(',')
        params.videoHost = params.videoHost.join(',')
        if (params.sort == 10) {
          params.sort = this.sortRadio
        }

        //获取媒体类型发文数量
        let choseList = this.mediaList.map((item) => item.dictValue)
        let mediaParams = {
          ...params,
          type: choseList.join(','),
        }
        this.showNum = false
        getMediaTypeCount(mediaParams).then(resNum => {
          if (resNum.code == 200) {
            this.mediaList = this.mediaList.map((item, index) => {
              item.number = Number(resNum.data[index]?.value || 0)
              return item
            })
            this.showNum = true
            this.total = 0;
            if (this.queryForm.type.length == 0) {
              this.mediaList.forEach(obj => {
                    this.total += obj.number;
                });
            } else {
              this.queryForm.type.forEach(value => {
                this.mediaList.forEach(obj => {
                  if (obj.dictValue === value) {
                    this.total += obj.number;
                  }
                });
              });
            }
            this.totalPage = Math.ceil(this.total / this.queryForm.pageSize)
          }
        })
        this.showAccount = false

        const newMediaParams = {...mediaParams};
        delete newMediaParams.accountLevel;
        accountLevelCount(newMediaParams).then(resNum => {
          if (resNum.code == 200) {
            this.accountList = this.accountList.map((item, index) => {
              item.number = Number(resNum.data[index]?.value || 0)
              return item
            })
            this.showAccount = true
          }
        })
        const res = await searchPlan(params)
        // this.tableData = res.rows.map(item => {
        //     item.originFlag = item.emotionFlag;
        //     // 初始化 `urlAccessStatus` 属性
        //     this.$set(item, 'urlAccessStatus', null);
        //     return item;
        // });
        let tableArray = JSON.parse(JSON.stringify(res.rows))
        tableArray.map((item) => {
          item.originFlag = item.emotionFlag
          item.urlAccessStatus = null
        })
        // this.total = Number(res.total)
        // this.totalPage = Math.ceil(this.total / this.queryForm.pageSize)
        this.searchLoading = false
        this.queryLoading = false
        this.tableData = await this.enrichArrayWithDetails(tableArray)
        this.tableLoading = false
        await this.checkUrlAlive(this.tableData, this.searchNum)
        // this.startInterval()
      } catch (error) {
        console.log(error)
        this.searchLoading = false
        this.queryLoading = false
        this.tableLoading = false
      }
    },
    //校验原文是否删除
    async checkUrlAlive(data, number) {
      const urls = data.map(item => {
        return item.url
      })
      if (urls.length == 0) {
        return
      }
      const newArray = data
      try {
        let res = await getUrlAccessStatusApi(urls)
        res.data.map((item, index) => {
          newArray[index].urlAccessStatus = item
        })
      } catch (err) {
        this.$message.error(err)
      }
      if (this.searchNum == number) {
        this.tableData = newArray;
      }
    },
    //保存筛选条件
    async saveFilter(message = true) {
      // type- search-搜索 query-空：查询 空-翻页、刷新
      if (this.timeType != 'fixed') {
        if (!this.queryForm.startTime || !this.queryForm.endTime) {
          this.$message.error('请输入完整的时间范围')
          return
        }
      }
      let params = JSON.parse(JSON.stringify(this.queryForm))
      params.pageNum = 1
      if (params.sort == 10) {
        params.sort = this.sortRadio
      }
      try {
        let res = await saveSearchCriteria(params)
        message && this.$message.success('保存成功')
      } catch (error) {
        this.$message.error(error)
      }
    },
    //获取筛选条件
    async getFilter() {
      if (!this.checkedNode.id) {
        return
      }
      if (this.filterNodeId == this.checkedNode.id) {
        return
      }
      this.filterNodeId = this.checkedNode.id
      let params = {planId: this.checkedNode.id}
      try {
        let res = await getSearchCriteria(params)
        if (Object.keys(res.data).length === 0) {//没有保存筛选条件

          this.queryForm.sort = this.sortList[0].dictValue

          this.queryForm.type = this.mediaList.map((item) => item.dictValue)

          this.queryForm.forward = this.forwardList.map((item) => item.dictValue)

          this.queryForm.contentForm = this.contentList.map((item) => item.dictValue)

          this.queryForm.accountLevel = this.accountList.map((item) => item.dictValue)

          this.queryForm.videoHost = this.videoList.map((item) => item.dictValue)

          //页面选择了时间段但未填写
          if (this.timeType != 'fixed' && (!this.queryForm.startTime || !this.queryForm.endTime)) {
            this.timeType = 'fixed'
            this.queryForm.timeIndex = 0
            this.$nextTick(() => {
              setTimeout(() => {
                this.$refs['dataRangeRef'].setParam(0)
              }, 100);
            })
          }
          return
        }
        this.queryForm = res.data
        let param = JSON.parse(JSON.stringify(res.data))
        if (this.queryForm.sort == 10 || this.queryForm.sort == 11 || this.queryForm.sort == 12 || this.queryForm.sort == 13) {
          this.queryForm.sort = '10'
          this.sortRadio = param.sort
        }
        if (res.data.timeIndex || res.data.timeIndex == 0) {
          this.timeType = 'fixed'
          this.$nextTick(() => {
            setTimeout(() => {
              this.$refs['dataRangeRef'].setParam(res.data.timeIndex)
            }, 100);
          })
        } else {
          this.timeType = 'end'
          this.$nextTick(() => {
            setTimeout(() => {
              this.$refs['dataRangeRef'].setParam('999', res.data.startTime, res.data.endTime)
            }, 100);
          })
        }
      } catch (error) {
        this.$message.error(error)
      }
    },
    // 分页查询
    pagination(page) {
      this.queryForm.pageNum = page.page
      this.queryForm.pageSize = page.limit
      this.submitSearch()
    },
    // 跳转详情页
    async goDetail(row) {
      const fullPath = this.$router.resolve({
        path: '/fullSearch/dataDetail',
        query: {id: row.id, planId: row.planId, keyWords: row.hitWords, time: row.publishTime, md5: row.md5}
      })
      window.open(fullPath.href, '_blank')
      if (row.isRead != 1) {
        this.updateIsRead(row.id)
        await searchRead({id: row.id})
      }
    },

    goSimilar(row) {
      if (row.count <= 1) {
        return
      }
      let query = {
        startTime: this.queryForm.startTime || '',
        endTime: this.queryForm.endTime || '',
        id: row.id,
        count: row.count,
        planId: this.checkedNode.id,
      }
      if (this.queryForm.timeIndex || this.queryForm.timeIndex == 0) {
        let timeRange = this.updateQueryTimeRange(this.queryForm.timeIndex)
        query.startTime = timeRange[0]
        query.endTime = timeRange[1]
        console.log('query', query)
      }

      const planRoute = this.$router.resolve({
        path: '/fullSearch/similarArticles',
        query
      })
      window.open(planRoute.href, '_blank')
    },
    updateQueryTimeRange(timeIndex) {
      if (timeIndex == 0) {
        return [moment(new Date()).format('YYYY-MM-DD 00:00:00'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')]
      } else {
        return [moment(new Date()).subtract(timeIndex, 'days').format('YYYY-MM-DD 00:00:00'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')]
      }
    },
    //更新阅读状态
    updateIsRead(id) {
      const foundItem = this.tableData.find(item => item.id === id);
      if (foundItem) {
        this.$set(foundItem, 'isRead', 1);
      }
    },
    //table选中项改变
    handleSelectionChange(val) {
      this.multipleSelection.selectedRows = val
    },
    // 复制文章
    copyAritical(row) {
      row.realTitle = row.title
      copyAritical(row)
    },
    // 复制
    copyText(content, tips) {
      copyText(content, tips)
    },
    // 查看原文
    goOrigin(url) {
      window.open(url, '_blank')
    },
    //过滤单条
    filterOne(row) {
      let param = {...row, indexId: row.id, planId: this.checkedNode.id}
      insertFilterInfoApi(param).then(res => {
        if (res.code == 200) {
          this.submitSearch()
          this.$message.success('操作成功')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getValue(row, isHost) {
        if (row.type == 0 && row.host == '百度贴吧') {
            return row.sector;
        } else {
            if (isHost) {
                return row.host;
            } else {
                return row.author;
            }
        }
    },
    //过滤站点
    async filterWeb(row) {
      let isHost = (row.type == 1 || row.type == 17 || row.type == 25 || row.type == 24)
      let params = [{
        name: this.getValue(row,isHost),
        settingHost: getDomain(row.url),
        plateId: this.checkedNode.id,
        sourceType: row.type,
        settingType: 2
      }]
      console.log(params)
      try {
        await addSourceSetting(params)
        this.$message.success('操作成功')
      } catch (error) {
        console.log(error)
      }
    },
    //处置||移除
    async markDisposition(row) {
      let val = row.deal ? 0 : 1;
      let res = await updateDeal({md5: row.md5, deal: val, indexId: row?.id, createTime: row?.publishTime})
      console.log(val, 'val');
      if (res.code == 200) {
        this.$set(row, 'deal', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'deal', row.deal);
        this.$message.error(res.msg)
      }
    },
    //重点关注||移除
    async markKeyFocus(row) {
      let val = row.follow ? 0 : 1;
      let res = await updateFollow({md5: row.md5, follow: val, indexId: row?.id, createTime: row?.publishTime})
      if (res.code == 200) {
        this.$set(row, 'follow', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'follow', row.follow);
        this.$message.error(res.msg)
      }
    },
    //标记噪音
    async markNoise(row) {
      let val = row.isSpam ? 1 : 2;
      let res = await updateTrash({md5: row.md5, noSpam: val})
      if (res.code == 200) {
        this.$set(row, 'isSpam', val == 1 ? false : true);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'isSpam', row.isSpam);
        this.$message.error(res.msg)
      }
    },
    //开启发送短信弹窗
    openSendMsg(row) {
      this.sendMsgDialog = true
      this.sendMsgRow = row
      this.sendMsgRow.planId = this.queryForm.planId
      this.sendMsgRow.keyWord1 = ''
    },
    // 系统报送
    openSendSystem(row) {
      // 管理者
      if (this.roles.includes('guanli')) {
        const fullPath = this.$router.resolve({
          path: '/send/addSend',
          query: {flag: true, sendId: row.id, time: row.publishTime}
        })
        window.open(fullPath.href, '_blank')
      }
      // 分析师
      if (this.roles.includes('fenxishi')) {
        const fullPath = this.$router.resolve({
          path: '/submit/addSubmit',
          query: {sendId: row.id, time: row.publishTime}
        })
        window.open(fullPath.href, '_blank')
      }
    },
    //同步sendMsgDialog值
    visibleChange(value) {
      this.sendMsgDialog = value
    },


    // 翻译时间选项
    transTimeRange() {
      let timeRange = {
        startTime: this.queryForm.startTime,
        endTime: this.queryForm.endTime
      }

      let timeIndex = this.queryForm.timeIndex

      if (timeIndex != null) {
        if (timeIndex == 0) {
          timeRange.startTime = moment(new Date()).format('YYYY-MM-DD 00:00:00')
        } else {
          timeRange.startTime = moment(new Date()).subtract(timeIndex, 'day').format('YYYY-MM-DD HH:mm:00')
        }
        timeRange.endTime = moment(new Date()).format('YYYY-MM-DD HH:mm:00')
      }

      if (timeRange.startTime && timeRange.endTime) {
        this.timeRange = timeRange
        this.newPlanName = this.checkedNode.name
        this.toHistoryVisible = true
      } else {
        this.$message.error('请输入完整的时间范围')
      }
    },
    async submitToHistory() {
      this.queryForm.timeIndex = undefined
      this.queryForm.startTime = this.timeRange.startTime
      this.queryForm.endTime = this.timeRange.endTime
      try {
        await this.saveFilter(false)
        this.$parent.$refs['taskTreeRef'].addToTime(this.checkedNode.id, '1', this.newPlanName)
        // this.toHistoryVisible = false
      } catch (error) {

      }
    },

    // 获取收藏树
    async getCollectionTreeData() {
      let resauto = await getCollectList()
      this.collectionTree = resauto.data
    },
    async subCollectionFolder(item, row) {
      const newitem = JSON.parse(JSON.stringify(row))
      delete newitem.id
      newitem.contentId = row.id
      const params = {...newitem, folderId: item.id}
      const res = await addCollectionFolder(params)
      if (res.code == 200) {
        this.$message.success('收藏成功')
      }
    },
  }

}
</script>
<style scoped lang="scss">
@import "./opinionData.scss";
.itemWrap{
  position: relative;
  display: inline-block;
}
.itemNum {
    position: absolute;
    top: 19px;
    width: 62%;

    div {
      width: fit-content;
      padding: 0px 7px;
      background: #E8F1FF;
      border-radius: 9px;
      font-size: 12px;
      color: #2D71FF;
      margin: 0 auto;
    }
  }
</style>
