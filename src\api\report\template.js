import request from '@/utils/request'

// 新增
export function createTemplate(data) {
  return request({
    url: `/report/insertTemplate`,
    method: 'post',
    data
  })
}

// 详情
export function detailTemplate(id) {
  return request({
    url: `/report/selectTemplateById/` + id,
    method: 'get',
  })
}

// 修改
export function updateTemplate(data) {
  return request({
    url: `/report/updateTemplateById`,
    method: 'post',
    data
  })
}

// 删除
export function deleteTemplate(id) {
  return request({
    url: `/report/deleteTemplateById/` + id,
    method: 'delete',
  })
}

// 是否默认模板
export function defaultTemplate(id) {
  return request({
    url: `/report/updateDefaultTemplate/` + id,
    method: 'get',
  })
}

// 系统、自定义列表
export function listTemplate() {
  return request({
    url: `/report/selectTemplate`,
    method: 'post',
  })
}

// 不系统、自定义列表
export function NolistTemplate() {
  return request({
    url: `/report/selectTemplateSortDefault`,
    method: 'post',
  })
}

// 创建报告
export function insertReport(data) {
  return request({
    url: `/report/insertReport`,
    method: 'post',
    data
  })
}
