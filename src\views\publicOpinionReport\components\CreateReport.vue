<template>
  <div class="add-wrap">
    <div class="title"><span>报告创建确认</span></div>
    <div class="contain">
      <div class="form-lf">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="报告模板：">
            <el-select v-model="form.tempId" placeholder="请选择报告模板" @change="changeReport">
              <el-option v-for="item in tempLists" :key="item.tempId" :label="item.name"
                         :value="item.tempId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报告标题：" prop="title">
            <el-input v-model="form.title" placeholder="请输入报告标题"></el-input>
          </el-form-item>
          <!-- <el-form-item label="报告刊号：" prop="issue">
            <el-input v-model="form.issue" placeholder="请输入报告刊号"></el-input>
          </el-form-item> -->
          <el-form-item label="报告标头：" prop="head">
            <el-input v-model="form.head" placeholder="请输入报告标头"></el-input>
          </el-form-item>
          <el-form-item label="选择素材库：" prop="materialId">
            <!-- <el-select v-model="form.materialId" placeholder="请选择素材库">
              <el-option v-for="item in folders" :label="item.folderName" :value="item.id"></el-option>
            </el-select> -->
            <el-cascader
              v-model="form.materialId"
              :options="folders"
              :props="{ label: 'folderName', value: 'id', children: 'children' }"
              @change="changeFold"
            ></el-cascader>
            <div><span style="color:red">*</span> 此处仅展示有素材的素材库</div>
          </el-form-item>
          <el-form-item label="报告分类：" prop="reportType">
            <el-select v-model="form.reportType" placeholder="请选择报告分类">
              <el-option v-for="item in typeLists" :key="item.value" :label="item.name"
                         :value="item.value"/>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="board-rg">
        <div class="board-title">{{ form.title }}</div>
        <div class="board-about">第（{{form.issue}}）期</div>
        <div class="board-time">{{ form.head }} {{currentTime}}</div>
        <div class="line"></div>
        <div class="board-desc">
          <div class="temp-compose">该模板由以下维度组成</div>
          <ul>
            <li v-for="(item,index) in newList">
              <img :src="item.icon" alt="">
              <span> {{item.name}}</span>
            </li>
          </ul>
          <div class="div-btn">
            <el-button type="primary" @click="submitForm('form')">生成报告</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {NolistTemplate, detailTemplate, insertReport} from '@/api/report/template.js'
import {getFolderList} from '@/api/report/material.js'
import {manageAllApi} from '@/api/publicOpinionWarning/index.js'
import dayjs from "dayjs";

export default {
  props: {
    tempId: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      dimension: [
        {name: '报告导读', params: 'reportIntro', icon: require('@/assets/images/report-read.png')},
        {name: '处置建议', params: 'suggest', icon: require('@/assets/images/deal-advise.png')},
        {name: '监测概述', params: 'overview', icon: require('@/assets/images/monitor-survey.png')},
        {name: '媒体来源统计', params: 'mediaStatistics', icon: require('@/assets/images/media-count.png')},
        {name: '信息情感分析', params: 'emotionAnalysis', icon: require('@/assets/images/emtion-analyse.png')},
        {name: '来源明细', params: 'mediaDetails', icon: require('@/assets/images/source-detail.png')},
        {name: '信息字符云', params: 'charCloud', icon: require('@/assets/images/word-cloud.png')},
        {name: '主要舆情', params: 'mainInfo', icon: require('@/assets/images/main-public.png')},
        {name: '舆情导读', params: 'infoIntro', icon: require('@/assets/images/public-read.png')},
        {name: '媒体信息走势图', params: 'mediaTrendChart', icon: require('@/assets/images/info-trend.png')},
      ],
      newList: [],
      tempLists: [],
      folders: [],
      form: {
        tempId: undefined,
        title: '',
        issue: '1',
        head: '网络舆情中心',
        materialId: [],
        reportType: ''
      },
      currentTime: `${dayjs().format("YYYY-MM-DD")}`,
      rules: {
        title: [
          {required: true, message: '请输入报告标题', trigger: 'blur'},
        ],
        issue: [
          {required: true, message: '请输入报告刊号', trigger: 'blur'}
        ],
        head: [
          {required: true, message: '请输入报告标头', trigger: 'blur'}
        ],
        materialId: [
          {required: true, message: '请选择素材库', trigger: 'change'}
        ],
        reportType: [
          {required: true, message: '请选择报告分类', trigger: 'change'}
        ]

      },
      typeLists: [{name: '日报', value: 1}, {name: '周报', value: 2}, {name: '月报', value: 3},
        {name: '季度报', value: 4}, {name: '年报', value: 5}, {name: '专报', value: 6}],

    }
  },
  // watch: {
  //   // 监听tempId的变化，并更新form中的tempId以及调用getDetails
  //   tempId(newVal) {
  //     if (newVal) {
  //     console.log('newVal :>> ', newVal);
  //       this.form.tempId = newVal;
  //       this.getDetails(this.form.tempId);
  //     } else{
  //     console.log('11 :>> ', 11);
  //        this.getDimension()
  //     }
  //   }
  // },
  //  beforeDestroy() {
  //   // 组件销毁前执行，可以清除或重置与tempId相关的状态
  //   // 注意：直接修改props是不可取的，但你可以清除组件内部的状态
  //   this.form.tempId = ''; // 清除form中的tempId
  //   // 如果需要，你可以在这里执行其他清理操作
  //   console.log('组件即将销毁，tempId相关的状态已被清理');
  // },
  created() {
    this.getDimension()
  },
  // mounted() {
  //   // 组件挂载后立即检查tempId
  //   if (!this.tempId) {
  //     this.getDimension();
  //   }
  // },
  methods: {
    changeFold(val) {
      console.log('val :>> ', val);
    },
    changeReport(value) {
      this.getDetails(value)
    },
    async getDimension() {
      const resp = await getFolderList()
      this.folders = resp.data
      // 首先，筛选出那些有至少一个 count > 0 的子文件的父文件
      let filteredParentFolders = this.folders.filter(parent => {
        return parent.children && parent.children.some(child => child.count > 0);
      });
      // 然后，对于每个筛选出的父文件，map 出一个新的对象，包含该父文件和其所有 count > 0 的子文件
      let result = filteredParentFolders.map(parent => ({
        ...parent,
        children: parent.children.filter(child => child.count > 0)
      }));
      this.folders = result

      console.log(' this.folders :>> ', result);

      const res = await NolistTemplate()
      this.tempLists = res.data
      this.form.tempId = res.data[0]?.tempId
      this.form.title = res.data[0]?.name

      if (this.form.tempId) {
        this.getDetails(this.form.tempId)
      }
      // if(this.tempId){
      //   this.form.tempId = this.tempId
      //   this.getDetails(this.tempId)
      // }
    },
    async getDetails(params) {
      const res = await detailTemplate(params)
      const inputComponents = res.data.inputComponents
      this.form.title = res.data.name
      const dimensionMap = this.dimension.reduce((acc, item) => {
        acc[item.params] = item;
        return acc;
      }, {});

      this.newList = inputComponents.map(param => dimensionMap[param]);
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const formParams = JSON.parse(JSON.stringify(this.form))
          formParams.materialId = formParams.materialId[1]
          if (!formParams.materialId) {
            this.$message.error('请先选择素材库')
          } else {
            insertReport(formParams).then(res => {
              if (res.code == 200) {
                this.$message.success('操作成功')
                this.$emit('changeIndex', 3)
              }
            })
          }

        } else {
          return false;
        }
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.add-wrap {
  background: #fff;
  min-height: calc(100vh - 120px);

  .title {
    height: 60px;
    line-height: 60px;
    border-bottom: 1px solid #DCDEE0;
    text-align: left;

    span {
      border-left: 6px solid #247CFF;
      padding-left: 28px;
      color: #333333;
      font-size: 18px;
      font-weight: bold;
    }

  }

  .contain {
    display: flex;
    padding-top: 20px;
    padding-bottom: 58px;

    .form-lf {
      padding-top: 30px;
      padding-right: 60px;
      width: 388px;
      border-right: 1px solid #DCDEE0;

      ::v-deep .el-form-item__label {
        color: #999999;
      }
    }

    .board-rg {
      height: 100%;
      flex: 1;
      text-align: center;
      padding: 0px 40px;

      .board-title {
        font-size: 24px;
        font-weight: bold;
        color: #FF0D0D;
        line-height: 33px;
        margin-top: 28px;
        margin-bottom: 10px;
      }

      .board-about {
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin: 10px 0px;
      }

      .board-time {
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin-bottom: 30px;
      }

      .line {
        width: 100%;
        height: 1px;
        border: 2px solid #FF0D0D;
      }

      .board-desc {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        margin-top: 68px;

        .temp-compose {
          font-size: 16px;
          color: #333333;
          line-height: 22px;
        }

        ul {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          margin: 0px;
          padding: 0px;
          margin-top: 42px;
          margin-bottom: 26px;
          margin-left: 50px;

          li {
            list-style: none;
            font-size: 16px;
            color: #333333;
            margin-bottom: 34px;
            width: 33%;
            display: flex;
            align-items: center;

            img {
              width: 22px;
              height: 22px;
              margin-right: 4px;
            }
          }
        }

        .div-btn {
          width: 100%;
          text-align: center;
        }
      }
    }
  }
}
</style>
