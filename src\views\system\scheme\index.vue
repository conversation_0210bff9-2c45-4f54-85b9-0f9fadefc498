<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="是否历史事件" prop="historyFlag">
        <el-select v-model="queryParams.historyFlag" placeholder="请选择" clearable size="small" @change="changeUser">
          <el-option label="否" :value="0"/>
          <el-option label="是" :value="1"/>
        </el-select>
      </el-form-item>
      <el-form-item label="方案名称" prop="planName">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入方案名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属组织" prop="deptId">
        <treeselect
          style="width:182px;"
          v-model="queryParams.deptId"
          :options="deptOptions"
          :normalizer="normalizer"
          placeholder="选择组织"
        />
      </el-form-item>
      <el-form-item label="所属用户" prop="userId">
        <el-select v-model="queryParams.userId" filterable placeholder="请选择方案分类" clearable size="small"
                   @change="changeUser">
          <el-option v-for="dict in userOptions" :key="dict.userId" :label="dict.userName"
                     :value="dict.userId"/>
        </el-select>
      </el-form-item>
      <el-form-item label="方案分类" prop="typeId">
        <el-select v-model="queryParams.typeId" filterable placeholder="请选择方案分类" clearable size="small">
          <el-option v-for="dict in classifyOptions" :key="dict.typeId" :label="dict.typeName"
                     :value="dict.typeId"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:post:remove']"
        >删除</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="方案名称" align="center" prop="planName"/>
      <el-table-column label="所属组织" align="center" prop="deptName"/>
      <el-table-column label="方案分类" align="center" prop="typeName"/>
      <el-table-column label="状态" align="center" prop="enableFlag">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enableFlag"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="是否历史事件" align="center" prop="historyFlag" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.historyFlag == 1 ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createBy"/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.optionFlag"
                     size="mini"
                     type="text"
                     icon="el-icon-edit"
                     @click="handleUpdate(scope.row)"
          >修改
          </el-button>
          <el-button v-if="scope.row.optionFlag"
                     size="mini"
                     type="text"
                     icon="el-icon-delete"
                     @click="handleDelete(scope.row)"
          >删除
          </el-button>
          <el-button size="mini" type="text"
                     icon="el-icon-view" v-if="!scope.row.optionFlag"
                     @click="handleView(scope.row)">查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加/修改/查看 方案 -->
    <el-dialog :visible.sync="addTaskDialog" :title="dialogTaskTitle" width="70%">
      <addProgram ref="addProgramRef"></addProgram>
      <template #footer>
        <div style="text-align: center;" v-if="dialogTaskTitle!='查看方案'">
                    <span class="dialog-footer">
                        <el-button type="primary" :loading="submitLoading" @click="submitForm">确定</el-button>
                        <el-button @click="closeAddTaskDialog">取消</el-button>
                    </span>
        </div>
        <div style="text-align: center;" v-if="dialogTaskTitle=='查看方案'">
                    <span class="dialog-footer">
                        <el-button type="primary" :loading="quoteLoading" @click="handleQuote">一键引用</el-button>
                        <el-button @click="closeAddTaskDialog">取消</el-button>
                    </span>
        </div>

      </template>
    </el-dialog>
    <FloatingButton/>
  </div>
</template>

<script>
import {copyPlan, treeselect} from "@/api/system/dept";
import {addPlanApi, updatePlanApi} from "@/api/publicOpinionMonitor/index.js";
import {listUser, planList, planTypeList} from "@/api/system/user";
import addProgram from '@/views/publicOpinionMonitor/components/addProgram/index.vue'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  components: {Treeselect, addProgram},
  name: "Post",
  data() {
    return {
      quoteLoading: false,
      addTaskDialog: false,
      dialogTaskTitle: '添加方案',
      submitLoading: false,
      deptOptions: [],
      classifyOptions: [],
      userOptions: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planName: undefined,
        deptId: undefined,
        userId: undefined,
        typeId: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        planName: [
          {required: true, message: "请输入分类名称,20字以内", trigger: "blur"},
          {min: 1, max: 20, message: '长度在20字以内', trigger: 'blur'}
        ]
      }
    };
  },
  watch: {
    'queryParams.deptId': {
      handler(val) {
        this.queryParams.userId = ''
        this.queryParams.typeId = ''
        this.classifyOptions = []
        this.queryUserOptions(val)
      },
      immediate: true
    }
  },
  created() {
    this.getList();
    this.getDeptList()
  },
  methods: {
    // 一键引用
    handleQuote() {
      let taskForm = JSON.parse(JSON.stringify(this.$refs['addProgramRef'].taskForm))
      this.$confirm('是否确认一键引用此方案吗？', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        console.log(taskForm, 'taskForm');
        return copyPlan({planId: taskForm.planId});
      }).then(() => {
        this.getList()
        this.closeAddTaskDialog();
        this.msgSuccess("操作成功");
      })
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.enableFlag === 1 ? "启用" : "停用";
      this.$confirm('确认要"' + text + '""' + row.planName + '"方案吗?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return updatePlanApi({planId: row.planId, enableFlag: row.enableFlag});
      }).then(() => {
        this.msgSuccess(text + "成功");
      }).catch(function () {
        row.enableFlag = row.enableFlag === 0 ? 1 : 0;
      });
    },
    // 切换用户
    changeUser(val) {
      if (val) {
        this.queryClassifyOptions(val)
      } else {
        this.classifyOptions = []
      }
      this.queryParams.typeId = ''
    },
    // 查询用户->>方案分类
    async queryClassifyOptions(val) {
      if (val) {
        let res = await planTypeList({pageNum: 1, pageSize: 0, userId: val})
        this.classifyOptions = res.rows
      } else {
        this.classifyOptions = []
      }
    },
    // 查询用户列表
    async queryUserOptions(val) {
      if (val) {
        let res = await listUser({pageNum: 1, pageSize: 0, deptId: val})
        this.userOptions = res.rows.filter((item) => item.userName != 'admin')
      } else {
        this.userOptions = []
      }
    },
    // 查询
    // 查询组织
    getDeptList() {
      treeselect().then((response) => {
        this.deptOptions = response.data
      });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      };
    },
    // 关闭添加任务弹窗
    closeAddTaskDialog() {
      this.addTaskDialog = false
    },
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      planList(this.queryParams).then(response => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 岗位状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postName: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.postId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    // 添加方案按钮
    handleAdd() {
      this.addTaskDialog = true
      this.dialogTaskTitle = '添加方案'
      this.$nextTick(() => {
        this.$refs['addProgramRef'].reset()
        this.$refs['addProgramRef'].taskForm.historyFlag = '0'
      })
    },
    // 添加/修改方案-提交表单
    submitForm() {
      this.$refs['addProgramRef'].$refs['form'].validate((valid) => {
        if (valid) {
          let taskForm = JSON.parse(JSON.stringify(this.$refs['addProgramRef'].taskForm))
          let searchMode = JSON.parse(JSON.stringify(this.$refs['addProgramRef'].searchMode))
          //高级模式-监测关键词去除符号后的长度
          let highMonitorWordLength = taskForm?.highMonitorWord?.replace(/[+|\(|\)|]/g, '')?.length || 0

          //至少一个关键词
          if ((searchMode == '0' && !taskForm.kw1 && !taskForm.kw2 && !taskForm.kw3) || (searchMode == '1' && !highMonitorWordLength)) {
            this.$message({
              message: '请输入至少一个监测关键词',
              type: 'warning'
            })
            return
          }
          let params = taskForm
          params.area = taskForm.area.join(',')
          params.highArea = taskForm.highArea?.join(',')
          params.searchMode = searchMode
          console.log('params', params)

          this.submitLoading = true
          //提交时同步转化快速模式和高级模式
          if (searchMode == '0') {
            params.highArea = params.area
            params.highExcludeWord = params.excludeWord || ''

            // 确保kw1、kw2、kw3存在且不为空字符串再进行处理
            const kw1Processed = params.kw1 ? `(${params.kw1.replace(/\s/g, '|')})` : '';
            const kw2Processed = params.kw2 ? `(${params.kw2.replace(/\s/g, '|')})` : '';
            const kw3Processed = params.kw3 ? `(${params.kw3.replace(/\s/g, '|')})` : '';
            // 根据处理后的变量拼接最终的highMonitorWord，这样可以避免多余的'+('或')'
            params.highMonitorWord = `${kw1Processed ? kw1Processed + '+' : ''}${kw2Processed ? kw2Processed + '+' : ''}${kw3Processed ? kw3Processed + '+' : ''}`.replace(/\+$/, ''); // 移除开头可能存在的多余 '+' 符号
          } else {
            params.area = params.highArea
            params.excludeWord = params.highExcludeWord || ''
            params.kw1 = ''
            params.kw2 = ''
            params.kw3 = ''
          }
          if (this.dialogTaskTitle == '添加方案') {
            // 添加
            addPlanApi(params).then((res) => {
              this.submitLoading = false
              this.$message({
                message: '提交成功',
                type: 'success'
              })
              this.closeAddTaskDialog()
              this.handleQuery()
            }).catch((err) => {
              this.submitLoading = false
            })
          } else {
            // 修改
            updatePlanApi(params).then((res) => {
              this.submitLoading = false
              this.$message({message: '修改成功', type: 'success'})
              this.closeAddTaskDialog()
              this.getList()
            }).catch((err) => {
              this.submitLoading = false
              console.log('err', err)
            })
          }
        } else {
          this.$message({
            message: '表单校验未通过',
            type: 'error'
          })
        }
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.addTaskDialog = true
      this.$nextTick(() => {
        let params = JSON.parse(JSON.stringify(row))
        params.area = params.area ? params.area.split(',') : []
        params.highArea = params.highArea ? params.highArea.split(',') : []
        params.historyFlag = '0'
        // this.$refs['addProgramRef'].configMode = '0'
        this.$refs['addProgramRef'].searchMode = String(params.searchMode)
        this.$refs['addProgramRef'].taskForm = params
        this.$refs['addProgramRef'].setTypeName()
        this.dialogTaskTitle = "修改方案";
      })
    },
    // 查看按钮
    handleView(row) {
      this.addTaskDialog = true
      this.$nextTick(() => {
        let params = JSON.parse(JSON.stringify(row))
        params.area = params.area ? params.area.split(',') : []
        params.highArea = params.highArea ? params.highArea.split(',') : []
        params.historyFlag = '1'
        this.$refs['addProgramRef'].searchMode = String(params.searchMode)
        // this.$refs['addProgramRef'].configMode = '0'
        this.$refs['addProgramRef'].taskForm = params
        this.$refs['addProgramRef'].setTypeName()
        this.disabled = true
        this.dialogTaskTitle = "查看方案";
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$confirm('是否确认删除方案名称为"' + row.planName + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return updatePlanApi({planId: row.planId, delFlag: 1})
      }).then(() => {
        this.handleQuery();
        this.msgSuccess("删除成功");
      })
    }
  }
};
</script>
