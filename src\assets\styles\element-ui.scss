// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

// 全文搜索
.head-wrap {
  .el-input-group__prepend {
    background: #fff;
    color: #666;
    padding: 0 0 0 20px;
  }

  .el-input--medium {
    font-size: 16px;

  }

  .el-input--medium .el-input__inner {
    height: 64px;
    line-height: 64px;
    border: none;
    font-size: 16px;
  }

  .el-input-group__append {
    background: #FF5E00;
    border: none;
    color: #fff;
  }
}

// 导航条
.navbar {
  .el-menu-item {
    margin-right: 20px;
    font-size: 16px;
    background: linear-gradient(180deg, #3485FF 0%, #0066FF 100%);
  }

  .el-submenu__title {
    font-size: 16px;
    background: linear-gradient(180deg, #3485FF 0%, #0066FF 100%);

  }
}

// 搜索结果
.result-wrap, .sentiment-wrap {
  .el-tabs__nav-wrap::after {
    height: 0;
  }

  .el-tabs__item {
    font-size: 18px;
    color: #666;
    line-height: 25px;
  }

  .el-tabs__item.is-active {
    color: #333;
  }

  .el-tabs__header {
    padding: 16px 0 0 16px;
    margin: 0 0 10px;
    background: #fff;
  }

  .el-tabs__active-bar {
    height: 6px;
    border-radius: 3px;
    background-color: #247CFF;
  }
}

.monit-data {
  .el-tabs__nav-wrap::after {
    height: 0;
  }

  .el-tabs__item {
    font-size: 18px;
    color: #666;
    line-height: 25px;
  }

  .el-tabs__item.is-active {
    color: #333;
  }

  .el-tabs__header {
    padding: 16px 0 0 16px;
    margin: 0 0 10px;
    background: #fff;
  }

  .el-tabs__active-bar {
    height: 6px;
    border-radius: 3px;
    background-color: #247CFF;
  }
}

.date-range, .name-list {
  .el-radio__inner {
    display: none;
  }

  .el-radio__label {
    padding: 7px 10px;
  }

  .el-radio {
    margin-right: 15px;
    color: #333;

    &:last-child {
      margin-right: 0;

    }
  }

  .el-radio__input.is-checked + .el-radio__label {
    color: #fff;
    background: #247CFF;
    border-radius: 2px;
  }
}

.checkbox-wrap {
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #333;
  }

  .el-checkbox__input.is-checked .el-checkbox__inner {
    background: none;
    border: 1px solid #247CFF;
  }

  .el-checkbox__inner::after {
    border: 1px solid #247CFF;
    border-left: 0;
    border-top: 0;
  }
}

.el-button--primary {
  background-color: #247CFF;
  border-color: #247CFF;
  font-size: 14px;
}

.dataTot {
  .el-input-group__append, .el-input-group__prepend {
    background: #fff;
    color: #333;
  }

  .el-button--small {
    padding: 7px 15px;
  }

  .el-input__inne {
    font-size: 14px;
    color: #333;
  }
}

.dataTable {
  .el-table--medium th, .el-table--medium td {
    padding: 0;
  }

  .el-table {
    color: #666;
  }

  .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
    color: #666;
    font-weight: 400;
    font-size: 14px;
  }

  .tableTitle {
    .el-input--mini .el-input__inner {
      height: 24px;
      line-height: 24px;
    }

    .el-input__suffix {
      right: 0;
      top: 2px;
    }

    .el-input--suffix .el-input__inner {
      padding-right: 0px;
      border-radius: 5px;
      border: none;
    }

    .el-input__inner {
      padding: 0 5px 0 10px;
    }

    .table-sense {
      .el-input .el-select__caret.is-reverse {
        margin-top: -4px;
      }

      .el-input.el-input--mini.el-input--suffix {
        width: 56px;
      }

      .el-icon-arrow-up:before {
        content: '\e78f';
      }

      .el-input__inner {
        background: #fcc3c2;
        color: #b52626;
      }

      .el-input .el-select__caret {
        color: #b52626;

      }
    }

    .table-nosense {
      .el-input.el-input--mini.el-input--suffix {
        width: 68px;
      }

      .el-input .el-select__caret.is-reverse {
        margin-top: -4px;
      }

      .el-icon-arrow-up:before {
        content: '\e78f';
      }

      .el-input__inner {
        background: #a8dfef;
        color: #006e8f;
      }

      .el-input .el-select__caret {
        color: #006e8f;
      }
    }

    .table-neutral {
      .el-input .el-select__caret.is-reverse {
        margin-top: -4px;
      }

      .el-input.el-input--mini.el-input--suffix {
        width: 56px;
      }

      .el-icon-arrow-up:before {
        content: '\e78f';
      }

      .el-input__inner {
        background: #ffdc70;
        color: #8e5d00;
      }

      .el-input .el-select__caret {
        color: #8e5d00;

      }
    }
    .dangerSelect {
        .el-input.el-input--mini.el-input--suffix {
          width: 50px;
        }
    }
  }
}

.jump-page {
  .el-input-number__increase {
    display: none;
  }

  .el-input-number__decrease {
    display: none;
  }

  .el-input-number--mini {
    width: 80px;

    .el-input__inner {
      padding: 0;
      font-size: 14px;
      color: #000;
    }
  }
}

.addProgram-tip-class {
  width: 30%;
}

.el-submenu__title i {
  color: #fff;
}

.el-tooltip__popper.is-light {
  border: none;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, .12), 0 6px 16px 0 rgba(0, 0, 0, .08), 0 9px 28px 8px rgba(0, 0, 0, .05);
  color: #333;

  .popper__arrow {
    border-top-color: #fff !important;
  }
}

.el-tooltip__popper {
  max-width: 500px;
  white-space: wrap;
}

.system-form .el-upload--picture-card {
  width: 300px;
}

.system-form .logo .el-upload--picture-card {
  width: 180px;
  height: 146px;
}

.self-dialog {
  .el-dialog__footer {
    display: flex;
    justify-content: center;
  }
}

.table-fiexd {
  .el-table__header-wrapper {
    position: fixed;
    // top: 205px;
    top: 169px;
    z-index: 300;
    background: #fff;
  }
}

.result-fiexd {
  .el-table__header-wrapper {
    position: fixed;
    // top: 185px;
    top: 149px;
    z-index: 300;
    background: #fff;
  }
}

/* 为整个页面或特定元素设置滚动条样式 */
.word-list {
  /* 确保元素可滚动 */
  overflow-y: auto;

  /* 滚动条样式 */
  /* 滚动条的轨道背景 */
  &::-webkit-scrollbar {
    width: 10px; /* 你可以根据需要调整这个值 */
  }

  /* 滚动条的滑块 */
  &::-webkit-scrollbar-thumb {
    background: transparent;
    // background: #888; /* 滑块颜色，你可以使用渐变色或图片 */
    border-radius: 8px; /* 圆角，使其看起来更圆润 */

    /* 添加阴影效果，使其看起来更像苹果系统的滚动条 */
    // box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  }

  /* 当滑块被点击或拖动时 */
  &::-webkit-scrollbar-thumb:active {
    background: #555; /* 滑块被点击或拖动时的颜色 */
  }

  /* 滚动条的轨道（背景） */
  &::-webkit-scrollbar-track {

    background: transparent;
    // background: #f1f1f1; /* 轨道颜色 */
    border-radius: 10px; /* 圆角 */
  }
}

.phone-tabs {
  .el-tabs__header {
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.14);
  }

  .el-tabs__nav {
    width: 100%;
  }

  .el-tabs__item {
    padding: 0;
    width: 33.3%;
    text-align: center;
    line-height: 55px;
    height: 55px;
  }

  .el-tabs__active-bar {
    display: none;
  }

  .el-tabs__item.is-active {
    color: #fff;
    background: #247CFF;
  }

  .el-tabs__nav-wrap::after {
    display: none;
  }
}

.content-detail {
  em {
    color: #FF2D26;
  }
}

.notice-title {
  em {
    color: #FF2D26;
  }
}

.account-content-detail {
  .detail-msg {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  em {
    font-style: normal !important;
  }
}

.spread-box {
  .el-timeline-item__wrapper {
    padding-left: 20px;
  }

  .el-card__body {
    padding: 12px 12px 12px 20px;
  }
}
.reportSetting{
  .el-form-item--medium .el-form-item__content{
    line-height: 40px;
  }
}
.data-container{
  .el-form-item--medium .el-form-item__content{
    line-height: 40px;
  }
}
