.rank-detail-wrap {
  background: #F4F7FB;
  // padding-bottom: 80px;
}

.rank-head {
  width: 100%;
  height: 400px;
  position: relative;
  border-radius: 8px;

  .head-img {
    width: 100%;
    height: 400px;
  }

  .head-wrap {
    width: 830px;
    text-align: center;
    position: absolute;
    top: 88px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 8;
    color: #fff;
    font-size: 16px;

    .append-head {
      display: flex;
      align-items: center;
    }

    .line {
      display: inline-block;
      margin-left: 26px;
      width: 1px;
      height: 35px;
      background: #EEEEEE;
    }
  }

  .head-input {
    margin-bottom: 7px;
    position: relative;
    display: flex;
    align-items: center;
  }

  .slide-box {
    position: absolute;
    top: 60px;
    left: 96px;
    right: 0;
    width: 610px;
    z-index: 100;
    margin-top: 10px;
    padding: 20px 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    text-align: left;
  }

  .slide-item {
    margin-right: 15px;
    padding: 4px 10px 6px 10px;
    border-radius: 5px;
    background: #1890ff;
    color: #fff;
    cursor: pointer;
    line-height: 24px;
  }

  .name-question {
    margin-left: 10px;
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  .head-title {
    margin-bottom: 45px;
    font-size: 48px;
    font-weight: 500;
    line-height: 67px;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .hot-words {
    width: 830px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    line-height: 22px;

    .hot-title {
      display: block;
      line-height: 33px;
      padding-top: 7px;
    }

    .word-list {
      padding-top: 7px;
      max-width: 750px;
      max-height: 120px;
      overflow-y: auto;
      margin-left: 14px;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;

      .el-icon-error {
        display: none;
        position: absolute;
        right: -6px;
        top: -8px;
        font-size: 20px;
        color: #FF0000;
        cursor: pointer;
      }

      .words-main {
        position: relative;
        display: flex;
        align-items: center;
        margin-right: 6px;
        margin-bottom: 13px;
        padding: 5px 6px;
        border-radius: 3px;
        background: #798af6;

        &:hover {
          .el-icon-error {
            display: block;
          }
        }

        img {
          display: inline-block;
          margin-right: 6px;
          width: 18px;
          height: 23px;
        }
      }
    }
  }
}

.rank-rank {
  // width: 86% !important;
  // margin: 0 auto;
  display: flex;
  padding: 0px 200px;
  // padding-left: 50px;
  justify-content: space-between;
  // justify-content: center;
  width: 100%;
  position: absolute;
  top: 50px;

  .sider-nav {
    width: 260px;
    padding: 12px;
    background: linear-gradient(180deg, #E4EEFF 0%, #FFFFFF 100%);
    box-shadow: 0px 2px 4px 0px #E5E5E5;
    border-radius: 4px;
    margin-right: 30px;


    .nav-list {
      width: 100%;

      .navbar {
        line-height: 25px;
        padding: 20px;
        text-align: left;
        font-size: 18px;
        color: #333333;
        cursor: pointer;

        img {
          width: 24px;
          height: 20px;
          margin-bottom: -3px;

          &.square {
            width: 20px;
          }
        }
      }

      .active {
        background: #D2E3FF;
        font-weight: bold;
        border-radius: 4px;
      }
    }
  }

  .hot-style {
    display: flex;
    flex: 1;
    min-width: 0;
  }

  .plat-style {
    flex: 1;
    min-width: 0;

    .nav-box {
      display: flex;
      justify-content: space-between;

      .rank-two {
        width: 50%;
      }

      .rank-three {
        // width: 33%;
      }

      .margin-two {
        margin-right: 0px;
      }
    }

    .nav-title {
      font-weight: bold;
      font-size: 20px;
      color: #333333;
      line-height: 28px;
      margin-bottom: 6px;
    }

    .comm-style {
      margin-top: 40px;
    }
  }

  .single-rank {
    // width: calc((100% - 60px) * 0.33);
    width: 100%;
    // margin-right: 30px;
    border-radius: 8px;
    background: linear-gradient(180deg, #E4EEFF 0%, #FFFFFF 100%);
    box-shadow: 0px 2px 4px 0px #E5E5E5;

    .single-title {
      display: flex;
      padding: 15px 0 16px 26px;
      border-radius: 8px 8px 0 0;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      color: #999;

      .title-box {
        margin-left: 10px;
      }

      img {
        width: 30px;
        height: 25px;
        margin-top: 4px;

        &.square {
          width: 25px;
        }
      }

      p {
        margin: 0;
        font-size: 24px;
        font-weight: bold;
        color: #333;
        line-height: 33px;
      }
    }

    .rank-list {
      width: calc(100% - 48px);
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin: 15px 24px 0 24px;
      padding-bottom: 14px;
      border-bottom: 1px dashed #DCDEE0;
      font-size: 14px;
      line-height: 20px;
      color: #999;
      cursor: pointer;

      &:last-child {
        border: none;
      }

      img {
        width: 17px;
        height: 21px;
        margin-right: 8px;
      }

      .num {
        display: inline-block;
        width: 16px;
        text-align: center;
      }

      .num-first {
        color: #FF0000;
      }

      .num-second {
        color: #FF7F2D;
      }

      .num-third {
        color: #FFB10E;
      }

      .detail {
        margin-left: 16px;
        color: #333;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: calc(100% - 70px);
      }
    }
  }

}

.rank-article {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 80%;
}

.rank-noimg {
  display: inline-block;
  width: 17px;
  margin-right: 8px;
}

.rank-box-wrap {
  max-height: 640px;
  overflow-y: scroll;
  width: 100%;
}

.keywords-content {
  cursor: pointer;
}
