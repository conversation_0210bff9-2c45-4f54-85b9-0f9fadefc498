import request from '@/utils/request'

// 收藏库
export function getCollectList(params) {
  return request({
    url: `/collection/folder/list`,
    method: 'get',
    params
  })
}

// 列表
export function getCollectPageList(data) {
  return request({
    url: `/collection/list`,
    method: 'post',
    data
  })
}

// 新增/修改收藏库
export function saveCollectionFolder(data) {
  return request({
    url: `/collection/folder/save`,
    method: 'post',
    data
  })
}

// 删除收藏库
export function deleteCollectFolder(id) {
  return request({
    url: `/collection/folder/delete/` + id,
    method: 'delete',
  })
}


// 删除收藏
export function deleteCollectList(ids) {
  return request({
    url: `/collection/delete/` + ids,
    method: 'delete',
  })
}

// 素材库
export function getCollectionFolderList(params) {
  return request({
    url: `/collection/folder/list`,
    method: 'get',
    params
  })
}

// 添加至素材
export function addCollectionFolder(data) {
  return request({
    url: `/collection/add`,
    method: 'post',
    data
  })
}
