<template>
  <!-- echarts -->
  <div class="echarts" ref="map">
    <div ref="DrillMap" style="width: 100; height: 100%"></div>
    <div class="mapChoose">
      <span v-for="(item, index) in parentInfo" :key="item.code">
        <span class="title" @click="chooseArea(item, index)">{{
          item.cityName == "全国" ? "中国" : item.cityName
        }}</span>
        <span v-show="index + 1 != parentInfo.length" class="icon">-</span>
      </span>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import mapJson from "../assets/china.json";
import {getAreaMap} from "@/api/search/index";

export default {
  props: {
    data: {
      type: Object,
      default: () => {
      },
    },
    params: {
      type: Object,
      default: () => {
      },
    },
    involves: {
      type: String,
      default: '0',
    },
    areaInfo: {
      type: Object,
      default: () => {
      },
    },
    showLoading: {
      type: Boolean,
      default: false
    },
    isToImg: {
      type: String,
      default: '',
    },
    isDown: {
      type: Boolean,
      default: false
    },
    isDataView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      myChart: null,
      onOff: true,
      areaBarData: {},
      parentInfo: [],
      geoJson: {
        features: []
      },
      // areaInfo: {
      //   adcode: '100000',
      //   adName: '全国'
      // }
    };
  },
  watch: {
    areaInfo() {
      this.$nextTick(() => {
        setTimeout(() => {
          this.initMap(this.areaInfo) //地图初始化区域
        }, 100)
      })
    },
  },
  // beforeDestroy() {
  //   if (!this.chart) {
  //     return;
  //   }
  //   this.chart.dispose();
  //   this.chart = null;
  // },
  mounted() {
    // if (mapJson !== "undefined") {
    //   echarts.registerMap("china", mapJson);
    // }

    // this.initChart();

    // this.initMap(this.areaInfo) // 地图初始化区域
  },
  methods: {
    initMap({adcode, adName}) {
      this.parentInfo = [{
        cityName: adName,
        code: adcode
      }]
      this.getGeoJson(adcode) //地图初始化区域，和parentInfo一致
    },
    getGeoJson(adcode) {
      this.onOff = false
      const that = this

      // eslint-disable-next-line no-undef
      AMapUI.loadUI(['geo/DistrictExplorer'], (DistrictExplorer) => {
        var districtExplorer = new DistrictExplorer()
        districtExplorer.loadAreaNode(adcode, function (error, areaNode) {
          if (error) {
            console.error(error)
            return
          }
          const Json = areaNode.getSubFeatures()

          if (Json.length > 0) {
            that.geoJson.features = Json
          } else if (Json.length === 0) {
            that.geoJson.features = that.geoJson.features.filter(
              (item) => item.properties.adcode == adcode
            )
            if (that.geoJson.features.length === 0) {
              that.geoJson.features = [areaNode.getParentFeature()]
            }
          }
          that.onOff = true;
          that.getMapData()
        })
      })
    },
    getMapData() {
      const contentAreaCode = this.parentInfo[this.parentInfo.length - 1].code
      getAreaMap({...this.params, involves: this.involves, contentAreaCode: contentAreaCode}).then(res => {
        if (res.code == 200) {
          const mapNull = res.data[0].xList.length
          const topData = res.data[0].zList.slice(0, 3)
          const zzData = res.data[0].zList
          const xxData = res.data[0].xList
          const yyData = res.data[0].yList
          const areaBarData = {name: xxData, value: yyData, code: zzData}
          this.areaBarData = {name: xxData, value: yyData}
          const mapData = areaBarData.name.map((name, index) => {
            return {
              name: name, // 直接使用原始name数组中的值
              value: areaBarData.value[index],// 使用对应的value数组中的值
              code: areaBarData.code[index].code
            };
          });
          const areaMapData = {mapData, topData, mapNull}
          this.initChart(areaMapData)
        } else {

        }

      })
    },
    initChart({mapData, topData, mapNull}) {
      this.$emit('getAreaData', this.areaBarData, mapNull, mapData)
      if (this.parentInfo.length === 1) {
        echarts.registerMap('Map', this.geoJson); //注册
      } else {
        echarts.registerMap('Map', this.geoJson); //注册
      }
      // this.chart = echarts.init(this.$refs.map);
      // echarts.registerMap('china', mapJSON); //注册地图
      this.myChart = echarts.init(this.$refs.DrillMap)

      // echarts.registerMap('Map', this.geoJson) //注册
      const img2 = `image://${require("@/assets/images/mapborder.png")}`;
      // let {mapData,topData} = this.data

      const myChart = echarts.init(this.$refs.DrillMap)
      const _this = this
      myChart.off('click')
      myChart.on('click', (params) => {

        if (!this.onOff) {
          return
        }
        if (this.parentInfo.length == 3) {
          // 2市 3县
          return
        }
        if (
          this.parentInfo[this.parentInfo.length - 1].code ==
          params.data.code
        ) {
          return
        }
        const data = params.data
        this.parentInfo.push({
          cityName: data.name,
          code: data.code
        })

        this.getGeoJson(data.code)
        this.$emit('getAreaData', this.areaBarData)
      })
      var max = mapData[0].value
      var min = mapData[mapData.length - 1].value

      const option = {
        tooltip: {
          show: true
        },
        toolbox: {
          right: "30",
          show: true,
          feature: {
            dataView: {  //设置数据视图
              show: this.isDataView,
              title: '数据视图',
              readOnly: false,
              lang: ['数据视图', '关闭', '刷新'],
            },
            restore: {  //设置数据重置
              show: this.isDataView,
              title: '还原',
            },
            saveAsImage: {show: true, name: "地域分布图"},
          },
        },
        grid: {
          left: "0px",
          right: "80px",
          top: "10px",
          bottom: "0px"
        },
        visualMap: {
          min: min,
          max: max,
          left: 'left',
          top: 'bottom',
          showLabel: true,
          text: ["高", "低"],
          show: true,
          calculable: true,
          seriesIndex: [0],//高亮指定的数据图形
          inRange: {
            color: ['#F4F4F4', '#40C3E3'] // 蓝绿
          }
        },
        geo: {
          map: "Map",
          animationDurationUpdate: 0,
          zoom: 1,
          // center: this.parentInfo.length === 1 ? ['118.83531246', '32.0267395887'] : false,
          roam: true, // 是否缩放
          // scaleLimit: {
          //     min: 1,
          //     max: 2
          // },
          label: {
            normal: {
              show: false,
            },
            emphasis: {
              show: false,
            }
          },
          itemStyle: {
            normal: {
              // shadowBlur: 50,
              // shadowColor: 'rgba(0, 0, 0, 0.2)',
              // borderColor: "#FFFFFF",
              show: false,
              // borderColor: "#DCDDDD",
            },
            emphasis: {
              areaColor: "#40C3E3",
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              borderWidth: 0
            }
          }
        },
        series: [
          {
            name: '地图',
            type: 'map',
            map: 'Map',
            selectedMode: 'none', // 禁用选中模式
            roam: true,//是否可缩放
            zoom: 1, //缩放比例
            data: mapData,
            geoIndex: 0,
            label: {show: false}
          },
          {
            name: 'Top 3',
            type: 'scatter',
            coordinateSystem: 'geo',
            tooltip: {show: false},
            data: topData,
            label: {
              normal: {
                show: true,
                formatter: function (params) {
                  let text = `{fline|${params.dataIndex + 1}} {tline|${params.data.data}}`;
                  return text;
                },
                // color: '#fff',
                rich: {
                  fline: {
                    padding: [0, 5],
                    color: '#F9843E',
                    textShadowColor: '#030615',
                    textShadowBlur: '0',
                    textShadowOffsetX: 1,
                    textShadowOffsetY: 1,
                    fontSize: 14,
                    fontWeight: 400,
                  },
                  tline: {
                    padding: [0, 5],
                    color: '#FFFFFF',
                    fontSize: 14,
                  },
                },
              },
            },
            symbol: img2,
            symbolSize: [80, 30],
            symbolOffset: [0, 0],
            z: 999,
          },
          //
        ]
      }

      this.myChart.setOption(option, true);
      if (this.myChart) {
        this.myChart.on('rendered', () => {
          if (this.isToImg) {
            const chartRef = this.isDown ?
              this.myChart.getDataURL({type: "png", pixelRatio: 2}) //拿到base64 地址，就好下载了。
              :
              this.myChart.getDataURL({type: "png", pixelRatio: 2, excludeComponents: ['toolbox']}) //拿到base64 地址，就好下载了。
            this.$emit('chartRef', this.isToImg, chartRef)
          }
        });
      }
    },
    //  选择切换市县
    chooseArea(val, index) {

      if (this.parentInfo.length === index + 1) {
        return
      }
      this.parentInfo.splice(index + 1)
      this.getGeoJson(this.parentInfo[this.parentInfo.length - 1].code)

    }
  },
};
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
  position: relative;
}

.mapChoose {
  position: absolute;
  left: 20px;
  top: 10px;
  color: #000;

  img {
    width: 12px;
    margin-bottom: 4px;
    vertical-align: middle;
  }

  .title {
    padding: 5px;
    cursor: pointer;
  }

  .icon {
    font-family: "simsun";
    font-size: 20px;
    // margin: 0 11px;
  }
}
</style>
<style scoped lang="scss">
::v-deep {
  textarea {
    line-height: 1.8em !important;
  }
}
</style>
