<template>
  <div class="info-style">
    <div class="head-label">
      <el-form :model="form" ref="form" :inline="true" label-width="100px" size="mini" label-position="right"
               @submit.native.prevent>
        <el-form-item label="类别名称" prop="typeName">
          <el-input v-model.trim="form.typeName" placeholder="请输入类别名称" clearable
                    @keyup.enter.native="handleQuery"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery" class="seStyle">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery" class="reStyle">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="contain-list">
      <div class="btns">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">添加</el-button>
        <el-button type="danger" icon="el-icon-delete" size="small" @click="handleDelete"
                   :disabled="multiple">删除
        </el-button>
      </div>
      <div style="padding:0px 20px">
        <el-table v-loading="loading" :data="infoLists" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center"/>
          <el-table-column type="index" label="序号" width="55" align="center" :index="getIndex"/>
          <el-table-column label="类别名称" prop="typeName" align="center"/>
          <el-table-column label="创建人" prop="createUserName" align="center"/>
          <el-table-column label="创建时间" prop="createTime" align="center"/>
          <el-table-column label="操作" align="center" width="140px">
            <template slot-scope="scope">
              <div style="display:flex;justify-content: center;">
                <el-button type="text" style="color:#1178FF" icon="el-icon-edit"
                           @click="handleUpdate(scope.row)">修改
                </el-button>
                <el-button type="text" style="color:#FF1212" @click="handleDelete(scope.row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <Pagination v-show="total > 0" :limit.sync="form.pageSize" @pagination="getList" :page.sync="form.pageNum"
                    :currentPage="form.pageNum" :total="total"></Pagination>
      </div>
    </div>
    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="formType" :model="formType" :rules="rules" label-width="80px">
        <el-form-item label="类别名称" prop="typeName">
          <el-input v-model.trim="formType.typeName" placeholder="请输入10个字符以内的类别名称"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {AddTypeApi, delTypeApi, getInfoTypeList} from '@/api/cate/index'
import {decryptByAES, encryptByAES} from '@/utils/jsencrypt'

export default {
  name: 'Cate',
  data() {
    return {
      form: {
        pageSize: 10,
        pageNum: 1,
        typeName: null,
        pageQuery: true
      },
      infoLists: [],
      total: 0,
      loading: false,
      formType: {
        typeName: '',
      },
      rules: {
        typeName: [
          {required: true, message: "类别名称不能为空", trigger: "blur"},
          {min: 1, max: 10, message: '长度在10个字符以内', trigger: 'blur'}
        ]
      },
      open: false,
      title: '',
      ids: [],
      multiple: true
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getIndex(index) {
      return (this.form.pageNum - 1) * this.form.pageSize + index + 1;
    },
    // 获取列表信息
    async getList() {
      this.loading = true
      try {
        // 加密
        if (this.$takeAES) {
          const formKeys = encryptByAES(JSON.stringify(this.form))
          let res = await getInfoTypeList({encryptJson: formKeys})
          if (res.code == 200) {
            this.loading = false
            this.total = res.total
            this.infoLists = JSON.parse(decryptByAES(res.encryptRows))
          } else {
            this.loading = false
          }
        } else {
          let res = await getInfoTypeList(this.form)
          if (res.code == 200) {
            this.loading = false
            this.total = res.total
            this.infoLists = res.rows
          } else {
            this.loading = false
          }
        }
      } catch {
        this.loading = false
      }
    },
    // 搜索按钮操作
    handleQuery() {
      this.form.pageNum = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.reset()
      this.handleQuery()
    },
    reset() {
      this.form = {
        pageSize: 10,
        pageNum: 1,
        typeName: null,
        pageQuery: true
      }
      this.resetForm('form')
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    // 新增
    handleAdd() {
      this.formType.typeName = ''
      this.open = true;
      this.title = "添加类别"
    },
    // 删除
    handleDelete(row) {
      const Ids = row.id || this.ids
      this.$confirm('是否确认删除该条数据?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        if (this.$takeAES) {
          const formKeys = encryptByAES(Ids)
          return delTypeApi(formKeys)
        } else {
          return delTypeApi(Ids)
        }
      }).then(() => {
        this.getList()
        this.msgSuccess("删除成功")
      })
    },
    // 修改
    handleUpdate(row) {
      this.title = "修改类别"
      this.open = true
      this.formType.typeName = row.typeName
      this.formType.id = row.id
    },
    // 提交按钮
    submitForm() {
      this.$refs["formType"].validate(valid => {
        if (valid) {
          if (this.title == '修改类别') {
            // 加密
            if (this.$takeAES) {
              const formKeys = encryptByAES(JSON.stringify(this.formType))
              AddTypeApi({encryptJson: formKeys}).then(() => {
                this.msgSuccess("修改成功")
                this.open = false
                this.getList()
              })
            } else {
              AddTypeApi(this.formType).then(() => {
                this.msgSuccess("修改成功")
                this.open = false
                this.getList()
              })
            }
          } else {
            delete this.formType.id
            // 加密
            if (this.$takeAES) {
              const formKeys = encryptByAES(JSON.stringify(this.formType))
              AddTypeApi({encryptJson: formKeys}).then(() => {
                this.msgSuccess("新增成功")
                this.open = false
                this.getList()
              })
            } else {
              AddTypeApi(this.formType).then(() => {
                this.msgSuccess("新增成功")
                this.open = false
                this.getList()
              })
            }
          }
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.formType.typeName = ''
    },
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/info.scss';

.info-style {
  .head-label {
    padding-top: 18px;
  }

  .contain-list {
    min-height: calc(100vh - 200px);
  }
}

.btns {
  padding-bottom: 20px;
}
</style>
