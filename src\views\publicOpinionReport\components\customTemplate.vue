<template>
  <div class="custom-template">
    <div class="header">
      <div><span>自定义模板{{title}}</span></div>
      <div>
        <el-button @click="goBack">返回上级</el-button>
        <el-button type="primary" @click="submitTemplate">保存模板</el-button>
      </div>
    </div>
    <div class="contain">
      <div class="side-menu">
        <div class="menu-title">维度</div>
        <div>
          <draggable
            class="components-draggable"
            :list="inputComponents"
            :group="{ name: 'componentsGroup', pull: 'clone'}"
            :clone="cloneComponent"
            @start="dragStart"
            @end="dragEnd"
            :animation="340"
            :sort="false"
          >
            <div v-for="(element, index) in inputComponents" :key="index" class="components-item">
              <div class="components-body">
                <img :src="element.icon" alt="">{{ element.name }}
              </div>
            </div>
          </draggable>

        </div>
      </div>
      <div class="rg">
        <div class="report-header">
          <el-input class="input-one" placeholder="" v-model="name" @blur="moreWord"></el-input>
          <div class="star-insert">第 ( -- ) 期</div>
          <div>
            <el-input class="input-two" :disabled="true" placeholder="" v-model="smallTitle"></el-input>
            <span>{{ currentTime }}</span>
          </div>
          <div class="line"></div>
        </div>
        <div class="center-scrollbar">
          <draggable
            class="drawing-board"
            :list="drawingList"
            :animation="340"
            :scroll="true"
            :move="onMove"
            :group="{ name: 'componentsGroup', pull: ''}">
            <div v-for="(element, index) in drawingList" :key="element.id" class="components-item">
              <div v-if="element.id == 1" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">报告导读</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <el-input type="textarea" :disabled="true" :rows="5" v-model="queryParmas.reportIntro"></el-input>
                </div>
              </div>
              <div v-if="element.id == 2" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">处置建议</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <el-input type="textarea" :disabled="true" :rows="5" v-model="queryParmas.suggest"></el-input>
                </div>
              </div>
              <div v-if="element.id == 3" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">监测概述</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <el-input type="textarea" :disabled="true" :rows="5" v-model="queryParmas.overview"></el-input>
                </div>
              </div>
              <div v-if="element.id == 4" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">媒体来源统计</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <img src="@/assets/report/media-chart.png" alt="">
                </div>
              </div>
              <div v-if="element.id == 5" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">信息情感分析</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <img src="@/assets/report/emtion-chart.png" alt="">
                </div>
              </div>
              <div v-if="element.id == 6" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">媒体来源明细</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <img src="@/assets/report/active-chart.png" alt="">
                </div>
              </div>
              <div v-if="element.id == 7" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">信息字符云</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <img src="@/assets/report/cloud-chart.png" alt="">
                </div>
              </div>
              <div v-if="element.id == 8" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">主要舆情</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <img src="@/assets/report/primary-table.png" alt="">
                </div>
              </div>
              <div v-if="element.id == 9" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">舆情导读</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <img src="@/assets/report/read-table.png" alt="">
                </div>
              </div>
              <div v-if="element.id == 10" class="div-box">
                <div class="nav-header">
                  <div class="nav-title">媒体信息走势图</div>
                  <div class="images">
                    <img src="@/assets/images/report-move.png" alt="" @click="drawingMove(index)">
                    <img src="@/assets/images/report-delete.png" alt="" @click="drawingDelete(index)">
                  </div>
                </div>
                <div class="content-textarea">
                  <img src="@/assets/report/line-chart.png" alt="">
                </div>
              </div>


            </div>
          </draggable>
          <div v-show="!drawingList.length" class="empty-info">
            <img src="@/assets/images/selected.png" alt="">
            从左侧栏选中维度，拖放到这里
          </div>
        </div>

      </div>
    </div>
    <!--  -->
    <div class="submitClass">
      <el-dialog width="35%" :visible.sync="dialogVisible" title="保存模板">
        <div v-if="isShow">
          <div class="ct-text">是否覆盖原模板?</div>
          <div style="text-align:center;">
            <el-button class="word-btn" @click="commonFn('update')" style="margin-right: 16px;">覆盖原模板</el-button>
            <el-button class="word-btn" type="primary" @click="commonFn('new')">另存为新模板</el-button>
          </div>
        </div>
        <div v-if="!isShow">
          <div class="ct-text">保存新模板成功</div>
          <div style="text-align:center;">
            <el-button class="word-btn" @click="commonFn('up')" style="margin-right: 16px;">返回首页</el-button>
            <el-button class="word-btn" type="primary" @click="commonFn('add')">创建报告</el-button>
          </div>
        </div>
      </el-dialog>
    </div>

  </div>
</template>

<script>
import draggable from 'vuedraggable'
import dayjs from "dayjs";
import {createTemplate, updateTemplate, detailTemplate} from '@/api/report/template.js'

export default {
  components: {
    draggable,
  },
  data() {
    return {
      timer: null, // 计时器 ID，用于清除计时器
      currentTime: `${dayjs().format("YYYY-MM-DD")}`,
      inputComponents: [
        {id: 1, params: 'reportIntro', name: '报告导读', icon: require('@/assets/images/report-read.png')},
        {id: 2, params: 'suggest', name: '处置建议', icon: require('@/assets/images/deal-advise.png')},
        {id: 3, params: 'overview', name: '监测概述', icon: require('@/assets/images/monitor-survey.png')},
        {id: 4, params: 'mediaStatistics', name: '媒体来源统计', icon: require('@/assets/images/media-count.png')},
        {id: 5, params: 'emotionAnalysis', name: '信息情感分析', icon: require('@/assets/images/emtion-analyse.png')},
        {id: 6, params: 'mediaDetails', name: '媒体来源明细', icon: require('@/assets/images/source-detail.png')},
        {id: 7, params: 'charCloud', name: '信息字符云', icon: require('@/assets/images/word-cloud.png')},
        {id: 8, params: 'mainInfo', name: '主要舆情', icon: require('@/assets/images/main-public.png')},
        {id: 9, params: 'infoIntro', name: '舆情导读', icon: require('@/assets/images/public-read.png')},
        {id: 10, params: 'mediaTrendChart', name: '媒体信息走势图', icon: require('@/assets/images/info-trend.png')},
      ],
      drawingList: [],
      draggedItem: [],
      name: '舆情简报',
      smallTitle: '网络舆情中心',
      queryParmas: {
        reportIntro: '   本报告就加入素材的文章进行分析，共有49篇相关内容，1篇相关评论。其中微博19篇，占比38%，客户端9篇，占比18%，微信9篇，占比18%，论坛4篇，占比8%，网站4篇，占比8%，新闻4篇，占比8%，外媒1篇，占比2%，而微博的比重最大，共有19篇，达到信息总量的38%。目前主要的报道集中在微博、客户端、微信、论坛、网站等几大站点。详细报告请继续浏览。',
        suggest: '   对于舆情信息中具有潜在危害的事件及情况应给予关注并积极处理，防止不良影响产生及扩散。此外，密切关注此前敏感预警事件的发展情况，及时制定有效应对措施。鉴于监测结果中负面舆情时有发生， 应吸取相关经验教训，做好预防和处理工作。',
        overview: '   监测主题相关信息内容48条，1篇相关评论。其中敏感5条，敏感占比10.2%，非敏感40条，非敏感占比81.6%，中性4条，中性占比8.1%。',
      },
      tempId: null,
      title: '创建',
      dialogVisible: false,
      isShow: false,
      paramsTemp: {}
    }
  },
  created() {
    this.tempId = this.$route.query?.id
    this.title = this.tempId ? '修改' : '创建'
    this.getDetail()
  },
  methods: {
    moreWord() {
      if (this.name.length > 20) {
        this.$message.error('长度不能超过20个字符')
        this.name = '网络舆情'
      }
    },
    commonFn(val) {
      if (val == 'update') {
        updateTemplate(this.paramsTemp).then(res => {
          if (res.code == 200) {
            this.isShow = false
            this.drawingList = []
          }
        })
      } else if (val == 'new') {
        delete this.paramsTemp.tempId
        createTemplate(this.paramsTemp).then(res => {
          if (res.code == 200) {
            this.drawingList = []
            this.isShow = false
          }
        })
      } else if (val == 'up') {
        this.goBack()
      } else {
        console.log('this.paramsTemp.tempId :>> ', this.paramsTemp.tempId);
        this.$router.push({path: '/PublicOpinionReport/report', query: {index: 0, tempId: this.paramsTemp.tempId}})
      }
    },
    // 删除
    drawingDelete(index) {
      this.drawingList.splice(index, 1)
    },
    // 向下移动
    drawingMove(index) {
      this.drawingList.splice(index, 1,
        ...this.drawingList.splice(index + 1, 1, this.drawingList[index]));
    },
    dragStart(event) {
    },
    dragEnd(end) {
    },
    cloneComponent(origin) {
      console.log('origin :>> ', origin);
      console.log('this.drawingList :>> ', this.drawingList);
      if (this.drawingList.length > 1) {
        if (!this.drawingList.find(item => item.id == origin.id)) {
          return JSON.parse(JSON.stringify(origin))
        } else {
          this.$message.error('该维度已存在，请勿重复添加！');
        }
      } else {
        return JSON.parse(JSON.stringify(origin))
      }

    },
    onMove(val) {
      console.log('val :>> ', val);
    },
    // 上级
    goBack() {
      this.$router.push({name: 'PublicOpinionReport/report', params: {msg: 2}})
    },
    // 保存
    submitTemplate() {
      console.log('submit:>> ', this.drawingList);
      const newArr = JSON.parse(JSON.stringify(this.drawingList))
      let paramsArray = newArr
        .filter(component => component.hasOwnProperty('params')) // 确保对象有 params 属性
        .map(component => component.params);
      console.log('paramsArray :>> ', paramsArray);
      const params = {
        name: this.name,
        tempId: this.tempId,
        inputComponents: paramsArray
      }
      console.log('params :>> ', params);
      this.paramsTemp = params
      if (paramsArray.length) {
        if (this.tempId) {
          this.dialogVisible = true
          this.isShow = true
          // updateTemplate(params).then(res=>{
          //     if(res.code == 200){
          //         this.$message.success('修改成功')
          //         this.drawingList = []
          //     }
          // })
        } else {
          delete params.tempId
          createTemplate(params).then(res => {
            if (res.code == 200) {
              // this.$message.success('保存成功')
              this.drawingList = []
              this.dialogVisible = true
              this.isShow = false
            }
          })
        }


      } else {
        this.$message.error('请至少选择一个维度')
      }
    },
    async getDetail() {
      if (this.tempId) {
        const res = await detailTemplate(this.tempId)
        this.name = res.data.name
        let items = res.data.inputComponents
        const dimensionMap = this.inputComponents.reduce((acc, item) => {
          acc[item.params] = item;
          return acc;
        }, {});

        this.drawingList = items.map(param => dimensionMap[param]);
        console.log('this.drawingList :>> ', this.drawingList);
      }

    },
  }
}
</script>
<style lang="scss" scoped>
.submitClass {
  .ct-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
  }

  .word-btn {
    font-size: 16px;
  }

  ::v-deep .el-dialog__footer {
    text-align: center;
  }

  ::v-deep .el-dialog__header {
    background: #247CFF;
    padding-bottom: 20px;

    .el-dialog__title {
      color: #fff;
    }

    .el-dialog__close {
      color: #fff;
    }
  }
}

.custom-template {
  background: #F4F7FB;
  height: calc(100vh - 80px);
  padding: 20px;

  .header {
    height: 54px;
    line-height: 54px;
    background: #FFFFFF;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    padding-right: 20px;

    span {
      border-left: 6px solid #247CFF;
      padding-left: 30px;
      font-size: 18px;
      color: #333333;
      font-weight: bold;
    }

  }

  .contain {
    display: flex;
    justify-content: space-between;
    height: calc(100vh - 184px);

    .side-menu {
      width: 300px;
      margin-right: 20px;
      background: #FFFFFF;
      padding: 16px;
      overflow-y: auto;

      .menu-title {
        font-size: 16px;
        color: #000000;
        font-weight: bold;
        text-align: center;
        border-bottom: 2px solid #f1eeee;
        padding-bottom: 10px;
        margin-bottom: 20px;
      }
    }

    .rg {
      flex: 1;
      background: #FFFFFF;
      padding: 28px 40px;
      overflow-y: auto;

      .report-header {
        text-align: center;
        font-weight: bold;

        .input-one {
          width: 416px;

          ::v-deep .el-input__inner {
            height: 50px;
            line-height: 50px;
            border: 1px solid #979797;
            padding: 0px;
            font-weight: bold;
            font-size: 24px;
            color: #FF0D0D;
            text-align: center;
          }
        }

        .star-insert {
          margin: 10px;
          font-size: 14px;
          line-height: 20px;
        }

        .line {
          height: 1px;
          border: 2px solid #FF0D0D;
          margin-top: 26px;
          margin-bottom: 35px;
        }

        .input-two {
          width: 106px;
          margin-right: 10px;

          ::v-deep .el-input__inner {
            height: 28px;
            line-height: 28px;
            border: 1px solid #979797;
            padding: 0px;
            font-size: 14px;
            font-weight: bold;
            color: #333333;
            text-align: center;
          }

        }
      }
    }

  }
}

.components-draggable {
  padding-bottom: 20px;
}

.components-item {
  display: inline-block;
  width: 100%;
  margin-bottom: 20px;
  transition: transform 0ms !important;

  .div-box {
    .nav-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 44px;
      background: #F0F5FF;
      padding-left: 30px;

      .nav-title {
        font-size: 18px;
        color: #000000;
        font-weight: bold;
      }

      .images {
        img {
          width: 20px;
          height: 20px;
          margin-right: 20px;
          cursor: pointer;
        }
      }
    }

    .content-textarea {
      text-align: center;
      margin-top: 20px;

      ::v-deep .el-textarea__inner {
        color: #333;
        font-weight: bold;
        font-size: 18px;
      }

      img {
        height: 100%;
        width: 100%;
      }
    }
  }
}

.center-scrollbar {
  height: calc(100vh - 426px);
  overflow-y: auto;
  box-sizing: border-box;
  border: 1px solid #CCCCCC;
  padding: 20px 40px;
  position: relative;
}

.components-body {
  padding: 12px 0px;
  padding-left: 44px;
  font-size: 16px;
  color: #333333;
  cursor: move;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;

  img {
    width: 22px;
    height: 22px;
    margin-right: 6px;
  }
}

.center-board-row {
  padding: 12px 12px 15px 12px;
  box-sizing: border-box;

  & > .el-form {
    // 69 = 12+15+42
    // height: calc(100vh - 69px);
    height: 100%;
    overflow-y: auto;
  }
}

.drawing-board {
  height: 100%;
}

.empty-info {
  font-size: 16px;
  line-height: 22px;
  color: #333333;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  img {
    width: 50px;
    height: 50px;
    margin-bottom: 28px;
  }
}
</style>
