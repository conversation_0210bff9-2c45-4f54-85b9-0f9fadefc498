<template>
  <div>
    <p style="margin: 0;">{{ days }}天 {{ hours }}:{{ minutes }}:{{ seconds }} </p>
  </div>
</template>
<script>
import moment from "moment"

export default {
  name: 'detailSubmit',
  data() {
    return {
      // targetDate: '2024-01-01 00:00:00',
      // timer: null,
      countdownInterval: null, // 更新间隔为1秒
      days: '',
      hours: '',
      minutes: '',
      seconds: '',
      files: []
    }
  },
  props: {
    targetDate: {
      type: String,
      default: '2024-07-19 17:30:00',
    },
    endTime: {
      type: Number,
      default: undefined,

    }
  },
  created() {
    this.countdownInterval = setInterval(this.startCountdown, 1000);
  },
  beforeDestroy() {
    // 在组件销毁前清除倒计时更新，防止内存泄漏
    clearInterval(this.countdownInterval);
  },
  methods: {
    startCountdown() {
      let today = new Date().getTime()
      let endTime = this.endTime ? this.endTime : today
      // 计算当前时间与目标日期的差值，并更新 data 中的时间差
      let dateObject = moment(this.targetDate, 'YYYY-MM-DD HH:mm:ss')
      let timeStamp = dateObject.valueOf() // 支持ie
      this.currentTimeDiff = timeStamp - endTime;
      // 每秒更新倒计时
      this.updateCountdown();
    },
    updateCountdown() {
      // 每秒更新倒计时各部分的值
      if (this.currentTimeDiff > 0) {
        const days = Math.floor(this.currentTimeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((this.currentTimeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((this.currentTimeDiff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((this.currentTimeDiff % (1000 * 60)) / 1000);
        this.days = days;
        this.hours = String(hours).length == 2 ? hours : '0' + hours;
        this.minutes = String(minutes).length == 2 ? minutes : '0' + minutes;
        this.seconds = String(seconds).length == 2 ? seconds : '0' + seconds;
      } else {
        // 如果时间差为零，重置倒计时各部分的值，并停止更新
        this.days = 0;
        this.hours = this.minutes = this.seconds = '00'
        clearInterval(this.countdownInterval)
        this.$emit('clearTime')
      }
    }
  }
}
</script>
