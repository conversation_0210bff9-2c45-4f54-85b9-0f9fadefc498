<template>
  <div class="dataScreen-container">
    <div class="dataScreen-content" ref="dataScreenRef">
      <div class="dataScreen-header">
        <div class="header-lf">
          <span class="header-screening">首页</span>
          <!-- <span class="header-screening" @click="router.push(HOME_URL)">首页</span> -->
        </div>
        <div class="header-ct">
          <div class="header-ct-title">
            <span>智慧旅游可视化大数据展示平台</span>
            <div class="header-ct-warning">平台高峰预警信息（2条）</div>
          </div>
        </div>
        <div class="header-ri">
          <span class="header-download">统计报告</span>
          <span class="header-time">当前时间：{{ currentTime }}</span>
        </div>
      </div>
      <div class="dataScreen-main">
        <div class="dataScreen-lf">
          <div class="dataScreen-top">
            <div class="dataScreen-main-title">
              <span>实时游客统计</span>
              <img src="./images/dataScreen-title.png" alt=""/>
            </div>
            <div class="dataScreen-main-chart">
              <vue-seamless-scroll
                :step="1"
                hover="true"
                :limitMoveNum="5"
                :data="listData"
                class="seamless-warp">
                <div class="item">
                  <div v-for="(item, index) in listData" :key="index">
                    <span>{{index + 1}}</span>
                    <span class="title">{{item.title}}</span>
                    <span>{{item.date}}</span>
                  </div>
                </div>
              </vue-seamless-scroll>
            </div>
          </div>
          <div class="dataScreen-center">
            <div class="dataScreen-main-title">
              <span>男女比例</span>
              <img src="./images/dataScreen-title.png" alt=""/>
            </div>
            <div class="dataScreen-main-chart">
              <BarChart style="height:250px;"/>
            </div>
          </div>
          <div class="dataScreen-bottom">
            <div class="dataScreen-main-title">
              <span>年龄比例</span>
              <img src="./images/dataScreen-title.png" alt=""/>
            </div>
            <div class="dataScreen-main-chart">
              <PieChart style="height:220px;"/>
            </div>
          </div>
        </div>
        <div class="dataScreen-ct">
          <div class="dataScreen-map">
            <div class="dataScreen-map-title">景区实时客流量</div>
            <ChinaMapChart style="height:640px"/>
          </div>
          <div class="dataScreen-cb">
            <div class="dataScreen-main-title">
              <span>未来30天游客量趋势图</span>
              <img src="./images/dataScreen-title.png" alt=""/>
            </div>
            <div class="dataScreen-main-chart">
              <LineChart style="height:200px;width:100%"/>
            </div>
          </div>
        </div>
        <div class="dataScreen-rg">
          <div class="dataScreen-top">
            <div class="dataScreen-main-title">
              <span>热门景区排行</span>
              <img src="./images/dataScreen-title.png" alt=""/>
            </div>
            <div class="dataScreen-main-chart">
              <div class="echarts-header">
                <span>排名</span>
                <span>景区</span>
                <span>预约数量</span>
              </div>
              <HotPlateChart style="height:260px"/>
            </div>
          </div>
          <div class="dataScreen-center">
            <div class="dataScreen-main-title">
              <span>年度游客量对比</span>
              <img src="./images/dataScreen-title.png" alt=""/>
            </div>
            <div class="dataScreen-main-chart">
              <AnnualUseChart style="height:250px"/>
            </div>
          </div>
          <div class="dataScreen-bottom">
            <div class="dataScreen-main-title">
              <span>预约渠道数据统计</span>
              <img src="./images/dataScreen-title.png" alt=""/>
            </div>
            <div class="dataScreen-main-chart">
              <CloudChart style="height:200px"/>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PieChart from "./components/PieChart.vue";
import AnnualUseChart from "./components/AnnualUseChart.vue";
import ChinaMapChart from "./components/ChinaMapChart.vue";
import HotPlateChart from "./components/HotPlateChart.vue";
import BarChart from "./components/BarChart.vue";
import LineChart from "./components/LineChart.vue";
import CloudChart from "./components/CloudChart.vue";
import dayjs from "dayjs";

export default {
  name: "dataScreen",
  components: {
    BarChart, PieChart, LineChart, HotPlateChart, ChinaMapChart, AnnualUseChart, CloudChart
  },
  data() {
    return {
      dataScreenRef: null,
      currentTime: dayjs().format("YYYY年MM月DD HH:mm:ss"),
      // 计时器 ID，用于清除计时器
      timer: null,
      listData: [
        {
          title: '五一节假日大蜀山人流量超出1000人',
          date: '2024-05-01'
        },
        {
          title: '五一节假日大蜀山人流量超出1000人',
          date: '2024-05-01'
        },
        {
          title: '五一节假日大蜀山人流量超出1000人',
          date: '2024-05-01'
        },
        {
          title: '五一节假日大蜀山人流量超出1000人',
          date: '2024-05-01'
        },
        {
          title: '五一节假日大蜀山人流量超出1000人',
          date: '2024-05-01'
        },
      ]
    };
  },
  created() {

  },
  mounted() {
    if (this.$refs.dataScreenRef) {
      this.$refs.dataScreenRef.style.transform = `scale(${this.getScale().ww},${this.getScale().wh}) translate(-50%, -50%)`;
      this.$refs.dataScreenRef.style.width = '1920px';
      this.$refs.dataScreenRef.style.height = '1080px';
    }
    window.addEventListener('resize', this.resize); // 注意这里的 this.resize 需要正确处理 this 的指向

    // 在组件挂载后设置计时器
    this.timer = setInterval(() => {
      this.currentTime = dayjs().format("YYYY年MM月DD日 HH:mm:ss");
    }, 1000);
  },
  beforeDestroy() {
    // 在组件销毁前清除计时器和移除事件监听器（尽管这里没有添加任何事件监听器）
    clearInterval(this.timer);
    // 如果之前添加了窗口大小变化的事件监听器，这里也需要移除
    window.removeEventListener("resize", this.resize);
  },
  methods: {
    // 设置响应式
    resize() {
      // 窗口大小变化时的处理逻辑
      if (this.$refs.dataScreenRef) {
        this.$refs.dataScreenRef.style.transform = `scale(${this.getScale().ww},${this.getScale().wh}) translate(-50%, -50%)`;
      }
    },
    // 根据浏览器大小推断缩放比例
    getScale(width = 1920, height = 1080) {
      let ww = window.innerWidth / width;
      let wh = window.innerHeight / height;
      // return ww < wh ? ww : wh;
      return {ww, wh};
    },
  }
};


</script>
<style lang="scss" scoped>
@import "./index.scss";

.seamless-warp {
  // height: 100px;
  overflow: hidden;
  color: #fff;
  padding: 10px;
  height: 100%;

  .item {
    height: 100%;

    .title {
      padding: 6px;
      display: inline-block;
    }
  }
}

.echarts-header {
  box-sizing: border-box;
  display: flex;
  height: 36px;
  margin: 10px 10px 0;
  line-height: 36px;
  background: url("./images/rankingChart-bg.png") no-repeat;
  background-size: 100% 100%;

  span {
    width: 18%;
    margin-left: 4px;
    font-size: 14px;
    font-weight: bold;
    color: #fdbc52;
    text-align: center;

    &:nth-child(2) {
      margin-left: 4px;
    }

    &:last-child {
      width: 20%;
      margin-left: 60px;
    }
  }
}
</style>
