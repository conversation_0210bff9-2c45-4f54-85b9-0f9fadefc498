<template>
  <div class="multi">
    <span :class="actives==true?'actives':''" @click="checkAll">全部</span>
    <span :class="actives==false&&item.tag==true?'actives':''" v-for="(item) in tabs" :key="item.value"
          @click="checkItem(item)">{{item.name}}</span>
  </div>
</template>

<script>
export default {
  model: {
    prop: "activeName",
    event: "update",
  },
  data() {
    return {
      actives: true,
      listIndex: []
    }
  },
  props: {
    tabs: {
      type: Array,
      default: () => [],
    },
    activeName: {
      type: String,
      default: "",
    },
  },
  watch: {
    activeName: {
      immediate: true, // 确保在初始渲染时也能执行此函数
      handler(newValue) {
        // 根据newValue更新actives和listIndex
        // 示例逻辑，具体实现需根据你的业务逻辑调整
        this.actives = newValue === ''; // ''代表全选
        this.listIndex = newValue ? newValue.split(',') : [];
        // 更新tabs中每个item的tag状态
        this.tabs.forEach(item => {
          item.tag = this.listIndex.includes(item.value.toString());
        });
      },
    },
  },
  methods: {
    delCheckItems() {
      this.actives = true
      this.listIndex = []
    },
    checkAll() {
      this.actives = true
      this.listIndex = []
      this.$emit("update", ''); // 这里更新activeName
      this.$emit("checkAll");
    },
    checkItem(item) {
      debugger
      this.actives = false
      if (item.tag == 0) {
        this.listIndex.push(item.value)
      } else {
        this.listIndex.splice(this.listIndex.findIndex(items => items === item.value), 1)
      }
      item.tag = !item.tag
      let param = this.listIndex.join(',')
      this.$emit("update", param);
    }
  },
};
</script>
<style lang="scss" scoped>
.multi {
  span {
    padding: 4px 10px;
    display: inline-block;
    margin: 0 15px 0 0;
    font-size: 14px;
    color: #333;
    line-height: 20px;
    cursor: pointer;

    &.actives {
      color: #fff;
      background: #247CFF;
      border-radius: 2px;
    }
  }
}

</style>
