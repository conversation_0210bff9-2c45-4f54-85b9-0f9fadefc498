<template>
  <ul class="hotTopic">
    <li class="omit">
      <div class="essay_main">
        <span style="width:20%;">排名</span>
        <span style="width:50%;">话题</span>
        <span class="essay_main_center" style="width:30%;">文章数</span>
        <!-- <span class="essay_main_center" style="width:15%;">历史热搜榜</span>
        <span class="essay_main_center" style="width:15%;">操作</span> -->
      </div>
    </li>
    <li class="omit">
      <div class="essay_main">
        <span style="width:20%;">排名</span>
        <span style="width:50%;">话题</span>
        <span class="essay_main_center" style="width:30%;">文章数</span>
        <!-- <span class="essay_main_center" style="width:15%;">历史热搜榜</span>
        <span class="essay_main_center" style="width:15%;">操作</span> -->
      </div>
    </li>
    <div style="width:100%">
      <li class="omit" v-for="(item,index) in data" :key="index">
        <div class="essay_main">
                    <span style="width:20%;">
                        <div :class="`essay_num${index<3?' back_red':''}`">{{index+1}}</div>
                    </span>
          <span style="width:50%;">
                        <a :href="item.url"
                           target="_blank">{{item.title}}</a>
                    </span>
          <span class="essay_main_center" style="width:30%;">
                        <a style="color: #5093e1;" @click="goSimilar(item)">{{item.docNum}}</a>
                    </span>
          <!-- <span class="essay_main_center" style="width:15%;">
              /
          </span>
          <span class="essay_main_center" style="width:15%;">
              <el-button size="mini">
                  <i class="el-icon-delete"></i>
                  删除
              </el-button>
          </span> -->
        </div>
      </li>
    </div>
  </ul>
</template>

<script>
import {DeleteHotWord} from '@/api/home/<USER>'
import moment from 'moment'

export default {
  data() {
    return {}
  },
  props: {
    type: {
      default: 'first',
      type: String
    },
    data: {
      default: () => [],
      type: Array
    },
    hotParams: {
      type: Object,
      default: () => {
      }
    }
  },
  watch: {
    type() {

    }
  },
  methods: {
    updateQueryTimeRange(timeIndex) {
      if (timeIndex == 0) {
        return [moment(new Date()).format('YYYY-MM-DD 00:00:00'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')]
      } else {
        return [moment(new Date()).subtract(timeIndex, 'days').format('YYYY-MM-DD 00:00:00'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')]
      }
    },
    goSimilar(row) {
      console.log(this.hotParams, 'this.hotParams', row);

      if (row.docNum == 0) {
        return
      }
      let query = {
        startTime: this.hotParams.startTime || '',
        endTime: this.hotParams.endTime || '',
        id: row.docId,
        count: row.pageView,
        planId: this.hotParams.planId,
      }
      if (this.hotParams.timeIndex || this.hotParams.timeIndex == 0) {
        let timeRange = this.updateQueryTimeRange(this.hotParams.timeIndex)
        query.startTime = timeRange[0]
        query.endTime = timeRange[1]
        console.log('query', query)
      }
      const planRoute = this.$router.resolve({
        path: '/fullSearch/similarArticles',
        query
      })
      window.open(planRoute.href, '_blank')
      // window.open('https://www.baidu.com','_parent')
    },
    // 获取列表
    async queryHotTopicList() {
      try {
        let params = {
          page: 1,
          type
        }
        this.hotTopicLoading = true
        let res = await mediaTypeAnalyse(params)
        this.hotTopicList = JSON.parse(JSON.stringify(res.data))
      } finally {
        this.hotTopicLoading = false
      }
    },
  }
}
</script>

<style scoped lang="scss">
.hotTopic {
  width: 100%;
  padding-left: 0;

  > li {
    min-height: 0px !important;
    border-bottom: #9DA5BE dashed 1px;
  }

  .omit {
    white-space: nowrap;

    width: calc(50% - 20px);
    list-style: none;
    float: left;
    margin: 5px 10px 0 10px;

    min-height: 10px !important;
    border-top: none !important;


    .essay_main {
      margin-left: 0px !important;
      display: flex;
      justify-content: space-between;
      line-height: 25px;

      > span {
        display: flex;
        align-items: center;

        overflow: hidden;
        text-overflow: ellipsis;

        > a {
          overflow: hidden;
          text-overflow: ellipsis;
        }


        ::v-deep .el-button--mini {
          padding: 7px;
        }
      }

      .essay_main_center {
        justify-content: center;
      }


      .essay_num {
        height: 25px;
        width: 25px;
        text-align: center;
        line-height: 25px;
        color: #fff;
        background-color: #1B76FF;
        border-radius: 4px;
        font-weight: bold;
      }

      .back_red {
        background-color: #FF0000;
      }
    }
  }

}
</style>
