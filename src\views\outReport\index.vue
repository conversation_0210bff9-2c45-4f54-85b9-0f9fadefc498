<template>
  <div class="out-report">
    <div class="contain">
      <div class="head">
        <div class="head-title">{{ detailList.title }}</div>
        <div class="star-insert">第（ {{detailList.issue}} ）期</div>
        <div>{{detailList.head}} <span style="display:inline-block;width:10px;"></span> {{ detailList.createTime }}
        </div>
        <div class="line"></div>
      </div>
      <div class="content">
        <div class="div-box" v-for="item in newList">
          <h1 v-if="chartData[item.params]">{{ item.name }}</h1>
          <div class="text" v-if="item.id === 1 || item.id === 2 || item.id === 3">{{ chartData[item.params]}}</div>
          <div class="image">
            <pieChart
              ref="pieChartOneRef"
              v-if="item.params == 'mediaStatistics' && chartData[item.params] "
              style="width:100%;height:300px;"
              :show-loading="sourceLoading"
              :data="chartData[item.params] && chartData[item.params]"
              :radius="['30%', '50%']"/>
            <pieChart
              ref="pieChartTwoRef"
              v-if="item.params == 'emotionAnalysis' && chartData[item.params] "
              style="width:100%;height:300px;"
              :show-loading="sourceLoading"
              :data="chartData[item.params]"/>
            <barChart
              ref="barChartRef"
              v-if="item.params == 'mediaDetails' && chartData[item.params] "
              style="width:100%;height:300px;"
              :data="chartData[item.params]"
              :showLoading="sourceLoading"/>
            <cloudChart
              ref="cloudRef"
              v-if="item.params == 'charCloud' && chartData[item.params]"
              style="width:100%;height:300px;"
              :showLoading="sourceLoading"
              :data="chartData[item.params]"
            />
            <div v-if="item.params == 'mainInfo' && chartData[item.params]">
              <el-table
                ref="tableRef"
                border
                v-loading="sourceLoading"
                :data="chartData[item.params]"
                :header-cell-style="{background:'#F8FAFF'}">
                <el-table-column label="序号" type="index" align="center"></el-table-column>
                <el-table-column label="标题" prop="title" align="center">
                  <template slot-scope="scope">
                    <div v-html="scope.row.title"></div>
                  </template>
                </el-table-column>
                <el-table-column label="日期与来源" prop="sourceAndTime" align="center"></el-table-column>
                <el-table-column label="属性" prop="emotion" align="center"/>
              </el-table>
            </div>
            <div v-if="item.params == 'infoIntro' && infoIntro ">
              <el-descriptions class="margin-top" :column="2" border ref="descriptionsRef">
                <el-descriptions-item label="性质" label-class-name="my-label">{{ infoIntro.emotion}}
                </el-descriptions-item>
                <el-descriptions-item label="文章来源" label-class-name="my-label">{{ infoIntro.source}}
                </el-descriptions-item>
                <el-descriptions-item label="时间" label-class-name="my-label">{{ infoIntro.time}}
                </el-descriptions-item>
                <el-descriptions-item label="作者" label-class-name="my-label">{{ infoIntro.author}}
                </el-descriptions-item>
                <el-descriptions-item label="内容" label-class-name="my-label" :span="2">{{ infoIntro.text}}
                </el-descriptions-item>
                <el-descriptions-item label="原文" label-class-name="my-label" :span="2">
                  <a :href="infoIntro.url" target="_blank" style="color:#247CFF;">{{ infoIntro.url}}</a>
                </el-descriptions-item>
              </el-descriptions>

            </div>
            <lineChart
              v-if="item.params == 'mediaTrendChart' && chartData[item.params]"
              style="width:100%;height:300px;"
              :data="chartData[item.params]"
              :showLoading="sourceLoading"/>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import pieChart from '@/views/fullSearch/components/pieChart.vue'
import barChart from '@/views/fullSearch/components/barChart.vue'
import cloudChart from '@/views/fullSearch/components/cloudChart.vue'
import lineChart from '@/views/fullSearch/components/lineChart.vue'
import {detailReport} from '@/api/report/list'

export default {
  components: {pieChart, barChart, cloudChart, lineChart},
  data() {
    return {
      sourceLoading: false,
      detailList: {},
      newList: {},
      infoIntro: [],
      chartData: {},
      dimension: [
        {id: 1, name: '报告导读', params: 'reportIntro'},
        {id: 2, name: '处置建议', params: 'suggest'},
        {id: 3, name: '监测概述', params: 'overview'},
        {id: 4, name: '媒体来源统计', params: 'mediaStatistics'},
        {id: 5, name: '信息情感分析', params: 'emotionAnalysis'},
        {id: 6, name: '来源明细', params: 'mediaDetails'},
        {id: 7, name: '信息字符云', params: 'charCloud'},
        {id: 8, name: '主要舆情', params: 'mainInfo'},
        {id: 9, name: '舆情导读', params: 'infoIntro'},
        {id: 10, name: '媒体信息走势图', params: 'mediaTrendChart'},
      ],

    }
  },
  created() {
    this.getDetails()
  },
  methods: {
    // 获取详情
    async getDetails() {
      this.sourceLoading = true
      try {
        const res = await detailReport(this.$route.query.reportId)
        this.sourceLoading = false

        this.detailList = res.data
        this.detailList.createTime = res.data.createTime.slice(0, 10);

        this.chartData = res.data.data
        this.chartData.mediaStatistics = res.data.data?.mediaStatistics ? {data: res.data.data.mediaStatistics} : ''
        this.chartData.emotionAnalysis = res.data.data?.emotionAnalysis ? {data: res.data.data.emotionAnalysis} : ''

        const yyData = res.data.data?.mediaDetails?.xList
        const xxData = res.data.data?.mediaDetails?.yList
        this.chartData.mediaDetails = this.chartData.mediaDetails ? {xxData, yyData} : ''
        this.chartData.charCloud = res.data.data?.charCloud ? res.data.data?.charCloud : ''

        this.infoIntro = res.data.data?.infoIntro ? res.data.data?.infoIntro[0] : ''
        const inputComponents = res.data.data.inputComponents ? res.data.data.inputComponents : []
        const dimensionMap = this.dimension.reduce((acc, item) => {
          acc[item.params] = item;
          return acc;
        }, {});

        this.newList = inputComponents.map(param => dimensionMap[param]);
        this.$nextTick(() => {
          setTimeout(() => {
            if (this.chartData.charCloud.length > 0) {
              this.$refs?.cloudRef?.[0]?.initChart();
            }
          }, 1000)
        })

      } catch (error) {
        this.sourceLoading = false
        console.error('Failed to fetch report details:', error);
        // 可以在这里添加错误处理逻辑，如显示错误消息
      }
    },
  }

}
</script>

<style lang="scss" scoped>
.out-report {
  margin: 0 auto;
  width: 85%;
  padding: 30px 0px;

  .contain {
    // overflow-y: auto;
    .head {
      text-align: center;

      .head-title {
        font-weight: bold;
        font-size: 24px;
        color: #FF0D0D;
      }

      .star-insert {
        margin: 10px;
        font-size: 14px;
        line-height: 20px;
      }

      .line {
        height: 1px;
        border: 2px solid #FF0D0D;
        margin-top: 26px;
        margin-bottom: 35px;
      }
    }

    .content {
      .div-box {
        .text {
          font-size: 16px;
          line-height: 24px;
        }
      }
    }
  }
}

</style>
