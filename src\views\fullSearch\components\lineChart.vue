<template>
  <div ref="linechart" class="chart"/>
</template>

<script>
import * as echarts from "echarts";
import {colorList} from '@/utils/boryou';

export default {
  name: 'lineChart',
  props: {
    data: {
      type: Object,
      default: () => {
      },
    },
    legendData: {
      type: Array,
      default: () => [],
    },
    showLoading: {
      type: Boolean,
      default: true,
    },
    show: {
      type: Boolean,
      default: true,
    },
    toolName: {
      type: String,
      default: '折线图'
    },
    chartText: {
      type: String,
      default: ""
    },
    legendTop: {
      type: Number,
      default: 0
    },
    legendLeft: {
      type: String,
      default: '5%'
    },
    lineType: {
      type: String,
      default: ""
    },
    gridTop: {
      type: String,
      default: '15%'
    },
    titleTop: {
      type: String,
      default: "30"
    },
    colors: {
      type: Array,
      default: () => ['#F46263', '#3D9FFE', '#F9A968', '#1D80DA', '#02F4FF', '#E3BC2D', '#FF6632', '#A7FFB0', '#8A01E1']
    },
    isShow: {
      type: Boolean,
      default: false
    },
    isToImg: {
      type: String,
      default: '',
    },
    isDown: {
      type: Boolean,
      default: false
    },
    isDataView: {
      type: Boolean,
      default: false
    },
    chartStyle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    data() {
      this.$nextTick(() => {
        this.initChart();
      });
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    window.removeEventListener('resize', () => {
      if (this.chart) this.chart.resize();
    });
  },
  mounted() {
    this.initChart()
    if (this.chart) {
      this.chart.on('click', (params) => {
        const seriesName = params.seriesName
        const seriesValue = params.value
        this.$emit('goToExpendDetail', this.toolName, seriesName, seriesValue)
      })
    }
    window.addEventListener('resize', () => {
      if (this.chart) this.chart.resize();
    })
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.linechart);
      const data = this.data;
      const xxData = data.xs;
      this.chart.showLoading();
      var legendData = this.legendData
      var isShow = this.isShow
      const yyData = [];
      data.seriesList?.map((item) => yyData.push(item.data));
      const legend = data.seriesList?.map((item) => item.name);
      let serieslist = [];
      for (let i in yyData) {
        serieslist.push({
          // showSymbol: false,
          name: legend[i],
          type: "line",
          // stack: "总量",
          data: yyData[i],
          smooth: true,
          areaStyle: {//覆盖区域的渐变色
            color: {
              type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                {offset: 0, color: '#F6FBFC'},// 0% 处的颜色
                {offset: 1, color: '#F5F8F7'}// 100% 处的颜色
              ],
              global: false // 缺省为 false
            },
          },
          showAllSymbol: true,
          symbolSize: 1,
          label: {
            show: i === '0' && this.isShow ? true : false, // 假设 '0' 是数组的第一个索引（注意，这里可能是字符串'0'或数字0，取决于yyData的键类型）
            position: 'top',
            fontSize: 16,
            // fontWeight: 'bold'
            formatter: function (params) {
              // 固定显示20个数字，计算间隔
              const interval = Math.ceil(yyData[i].length / 20);
              // 取余为整的才显示值
              if (params.dataIndex % interval == 0) {
                return params.value
              } else {
                return ''
              }
            },
          },

          // areaStyle:{}
          // // 最大值
          // markPoint: {
          //   symbol: "path://M607.2557983398438,806.6500244140625l79.6815185546875,-160.9346923828125L928.7442016601562,645.71533203125a130.976744,130.976744,0,0,0,130.97674560546875,-130.97674560546875L1059.720947265625,214.32557678222656A130.976744,130.976744,0,0,0,928.7442016601562,83.34883880615234L285.7674560546875,83.34883880615234A130.976744,130.976744,0,0,0,154.7906951904297,214.32557678222656l0,300.4130096435547a130.976744,130.976744,0,0,0,130.9767608642578,130.97674560546875l241.806884765625,0l79.68145751953125,160.9346923828125Z",
          //   symbolOffset: ['0', '-50%'],
          //   symbolKeepAspect: true,// 如果 symbol 是 path:// 的形式，是否在缩放时保持该图形的长宽比。
          //   label:{
          //       position: "inside",
          //       color: "#fff",
          //       fontWeight: 'bold',
          //       offset:[0,-3],
          //   },
          //   data: [{
          //     // type: "max"
          //   }],
          // },

        });
      }
      let option = {
        color: this.colors,
        title: {
          text: this.chartText,
          textStyle: {
            color: "#707070",
            fontSize: 12
          },
          top: this.titleTop,
          left: "50"
        },
        toolbox: {
          right: '30',
          show: true,
          feature: {
            dataView: {  //设置数据视图
              show: this.isDataView,
              title: '数据视图',
              readOnly: false,
              lang: ['数据视图', '关闭', '刷新'],
            },
            restore: {  //设置数据重置
              show: this.isDataView,
              title: '还原',
            },
            saveAsImage: {show: true, name: this.toolName}
          }
        },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          // data: legend,
          type: 'plain',  // 默认就是 plain，表示图例按行排列
          orient: 'horizontal',  // 设置图例为横向布局
          width: '80%',  // 限制 legend 的宽度，如果超过会自动换行
          formatter: function (name) {
            let target = 0
            for (let i = 0; i < legendData.length; i++) {
              if (name == legendData[i].name) {
                target = legendData[i].value
              }
            }
            let str = `${name} ${target}`;
            if (!isShow) {
              return `${name}`;
            }
            return str;
          },
          left: this.legendLeft,
          top: this.legendTop,
          show: this.show,
        },
        grid: {
          top: this.gridTop,
          left: "24px",
          right: "50px",
          bottom: "60px",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: xxData,
        },
        yAxis: {
          type: "value",
          // scale:true
        },
        dataZoom: [{
          id: 'dataZoomX',
          type: 'slider',
          xAxisIndex: [0],
          filterMode: 'filter', // 设定为 'filter' 从而 X 的窗口变化会影响 Y 的范围。
          start: 0,
          end: 10000000
        }],
        series: serieslist,
      };
      if (!this.showLoading) {
        this.chart.hideLoading()
      }
      this.chart.setOption(option, true);
      if (this.chart) {
        this.chart.on('rendered', () => {
          if (this.isToImg) {
            const chartRef = this.isDown ?
              this.chart.getDataURL({type: "png", pixelRatio: 2}) //拿到base64 地址，就好下载了。
              :
              this.chart.getDataURL({type: "png", pixelRatio: 2, excludeComponents: ['toolbox']}) //拿到base64 地址，就好下载了。
            this.$emit('chartRef', this.isToImg, chartRef)
          }
        });
        // 监听工具栏中数据视图的修改事件。
        this.chart.on('dataViewChanged', (params) => {
          // params中包含了与数据视图修改相关的信息
          console.log('修改后的数据视图相关信息:', params);
          const newData = {seriesList: params.newOption.series, xs: params.newOption.xAxis[0].data}
          this.$emit('modifiedData', this.chartStyle, newData)
        });
        // 监听工具栏中restore 重置 option 事件
        this.chart.on('restore', (params) => {
          this.$emit('modifiedData', this.chartStyle, '')
        });

      }
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep {
  textarea {
    line-height: 1.8em !important;
  }
}
</style>
