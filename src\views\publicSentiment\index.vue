<template>
  <div class="app-container">
    <div class="navbar">
      <el-form :model="queryParams" ref="queryParams" :inline="true">
        <el-form-item label="时间范围" prop="startTime">
          <el-date-picker style="width:175px" ref="start" type="datetime" size="mini"
                          :picker-options="pickerOptionsStart" v-model="queryParams.startTime" :clearable="true"
                          format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                          placeholder="请输入开始时间"></el-date-picker>
          -
          <el-date-picker style="width:175px" ref="end" :picker-options="pickerOptionsEnd" v-model="queryParams.endTime"
                          size="mini" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss"
                          placeholder="请输入结束时间"></el-date-picker>
        </el-form-item>
        <el-form-item label="信息属性" prop="emotionFlag">
          <el-select v-model="queryParams.emotionFlag" placeholder="请选择信息属性" clearable size="small">
            <el-option
              v-for="(item,index) in emotionData"
              :key="index"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="来源类型" prop="type">
          <el-select v-model="queryParams.type" placeholder="请选择类型" clearable size="small">
            <el-option
              v-for="dict in mediaList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="信息浏览" prop="isRead">
          <el-select v-model="queryParams.isRead" placeholder="请选择浏览状态" clearable size="small">
            <el-option
              v-for="item in msgOptions"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处置状态" prop="processStatus">
          <el-select v-model="queryParams.processStatus" placeholder="请选择处置状态" clearable size="small">
            <el-option
              v-for="item in dealOptions"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="报送内容" prop="content">
          <el-input
            v-model.trim="queryParams.content"
            placeholder="请输入报送内容"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="sentiment-wrap">
      <el-tabs v-model="queryParams.specialType" @tab-click="handleClick">
        <el-tab-pane label="已报送" name="1"></el-tab-pane>
        <el-tab-pane label="已处置" name="2"></el-tab-pane>
        <el-tab-pane label="已重点关注" name="3"></el-tab-pane>
      </el-tabs>
    </div>
    <div class="dataTable">
      <div style="margin-bottom:10px">
           <span v-show="total>=0">
              <el-select size="small" v-model="exportNum" placeholder="选择导出条数" style="width: 125px" clearable
                         @change="exportNumChange">
              <el-option label="选择当前页" value="0"/>
              <el-option label="前500条" value="500"/>
              <el-option label="前1000条" value="1000"/>
              <el-option label="前5000条" value="5000"/>
              </el-select>

              <el-dropdown placement="bottom" ref="Dropdown" trigger="click">
                  <div class="footButonItem">
                    <el-button type="text" primary style="margin-left:10px">批量导入素材</el-button>
                  </div>
                  <el-dropdown-menu slot="dropdown" class="subClass">
                      <el-dropdown-item v-for="item in treeDataauto" :key="item.id" :command="item.id"
                                        @mouseenter="() => {$refs.Dropdown.show()}">
                            <div v-if="!item.children">{{ item.folderName }}</div>
                            <template v-else>
                              <el-dropdown trigger="hover" placement="right-start" :show-timeout="1">
                                  <!-- 手动控制hover显示，解决鼠标移入二级菜单时一级菜单消失问题 -->
                                    <span class="el-dropdown-link" style="color: #606266;">
                                          {{ item.folderName }}<i v-if="item.children.length != 0"
                                                                  class="el-icon-arrow-right el-icon--right"/>
                                  </span>
                                  <el-dropdown-menu slot="dropdown" class="menuClass">
                                      <el-dropdown-item v-for="subItem in item.children" :key="subItem.id"
                                                        :command="subItem.id" @click.native="subFolder(subItem)">
                                              {{ subItem.folderName }}
                                      </el-dropdown-item>
                                  </el-dropdown-menu>
                              </el-dropdown>
                          </template>

                      </el-dropdown-item>
                  </el-dropdown-menu>
              </el-dropdown>

              <el-button v-if="!downloadLoading" type="text" primary style="margin-left:10px" @click="exportExcel">批量导出excel</el-button>
              <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;" class="el-icon-loading"></i>
          </span>
      </div>
      <el-table ref="tableRef" v-loading="tableLoading" :data="tableData" border
                style="width: 100%" :header-cell-style="{background:'#fcfcfd'}" :class="titleFixed?'result-fiexd':''"
                @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center"></el-table-column>
        <el-table-column prop="title" label="标题" align="left" header-align="center">
          <template #default="scope">
            <div :class="scope.row.isRead==1?'tableItemTitle cover-column':'tableItemTitle'">
              <div class="tableTitle" @click="goDetail(scope.row)">
                <img class="tableItemImg" :src="transImage(scope.row.type,scope.row.host||'')" alt="无图片"/>
                <el-tooltip placement="top" effect="light" raw-content>
                  <div slot="content">
                    <div v-html="scope.row.title"></div>
                  </div>
                  <div class="tableTitleSpan">
                    <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}. </span>
                    <span v-html="scope.row.title"></span>
                  </div>
                </el-tooltip>
                <el-select v-model="scope.row.emotionFlag"
                           :class="scope.row.emotionFlag==2?'emotionSelect table-nosense':scope.row.emotionFlag==1?'emotionSelect table-sense':'emotionSelect table-neutral'"
                           size="mini" placeholder="请选择" @change="(val)=>{changeSensitive(val,scope.row)}">
                  <el-option :key="2" label="非敏感" :value="2"></el-option>
                  <el-option :key="1" label="敏感" :value="1"></el-option>
                  <el-option :key="0" label="中性" :value="0"></el-option>
                </el-select>
                <p class="article-type" style="background-color: #339593" v-show="scope.row.isOriginal">原创</p>
                <p class="article-type" v-show="!scope.row.isOriginal">转载</p>
                <p class="article-type" style="background-color: #F7B8B3;color: #FA2C1C;" v-show="scope.row.warned==1">
                  流转中</p>
                <p class="article-type" style="background-color: #B2E8F3;color: #00B4D8;" v-show="scope.row.deal==1">
                  已处置</p>
                <p class="article-type" style="background-color: #F9D0AC;color: #F87500;" v-show="scope.row.follow==1">
                  已重点关注</p>
                <p class="article-type" style="background-color: #D8D8D8;color: #999999;"
                   v-if="scope.row.urlAccessStatus==0">已删除</p>
                <p class="article-type" style="background-color: #D5F8D1;color: #3EC82F;" v-else>可访问</p>
                <p v-for="item in scope.row.contentMeta" class="article-type" style="background-color: #ECF5FF;color: #409EFF;" >
                  {{item}}
                </p>
              </div>
              <div class="tableMain" v-html="scope.row.text" @click="goDetail(scope.row)"></div>
              <div class="tableFoot">
                <div class="footButtonGroup">
                  <div>
                    <div class="footButonItem" v-show="scope.row.hitWords">
                      <el-tooltip effect="light" content="涉及词" placement="top">
                        <img src="@/assets/images/keyword.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <el-tooltip effect="light" :content="scope.row.hitWords" placement="top">
                        <span class="keyword">{{ scope.row.hitWords || '' }}</span>
                      </el-tooltip>
                    </div>
                    <div class="footButonItem" v-show="scope.row.hitCourtNames">
                      <el-tooltip effect="light" content="涉及法院" placement="top">
                        <img src="@/assets/images/court.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <el-tooltip effect="light" :content="scope.row.hitCourtNames" placement="top">
                        <span class="keyword" style="color: #247CFF;">{{ scope.row.hitCourtNames || '' }}</span>
                      </el-tooltip>
                    </div>
                    <div class="footButonItem" v-show="scope.row.contentAreaCodeName">
                      <el-tooltip effect="light" content="精准地域" placement="top">
                        <img src="@/assets/images/areaDetail.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <el-tooltip effect="light" :content="scope.row.contentAreaCodeName" placement="top">
                        <span class="keyword" style="color: #356391;">{{ scope.row.contentAreaCodeName || '' }}</span>
                      </el-tooltip>
                    </div>
                  </div>

                  <div style="white-space: nowrap;">

                  </div>
                </div>
                <div>
                  作者：{{scope.row.author || '暂无'}} <span style="margin:0px 20px;"> {{scope.row.host}}</span>
                  {{scope.row.url}}
                </div>
              </div>
            </div>
            <img class="read-img" v-if="scope.row.isRead==1" src="@/assets/images/read.png" alt="">
            <img class="follow-img" v-if="scope.row.follow==1" src="@/assets/images/follow.png" alt="">
          </template>
        </el-table-column>
        <el-table-column prop="userName" v-if="queryParams.specialType =='1'" label="报送人" align="center"
                         width="100px">
          <template #default="scope">
            <div>
              {{ scope.row.userName }}
            </div>
            <div>
              {{ scope.row.phone }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="processStatus" v-if="queryParams.specialType =='1'" label="处置状态" align="center"
                         width="100px">
          <template #default="scope">
            {{ scope.row.processStatus }}
            <div v-if="scope.row.processStatus=='已处置'">
              <el-link type="primary" :underline="false" @click="openDialog(scope.row)">查看处置详情</el-link>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="时间" align="center" width="230px">
          <template #default="scope">
            <div>原文时间：{{ scope.row.publishTime }}</div>
            <div v-if="scope.row.createTime">{{queryParams.specialType==3?'关注':'处置'}}时间：{{ scope.row.createTime
              }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div style="width:100%;min-height: 20px;display:flex;align-items:center;justify-content: space-between;">
        <div style="margin-top:10px;">
          <el-checkbox :disabled="total==0" v-model="checked" @change="allSelect">全选</el-checkbox>
          <el-button v-if="!alldownloadLoading" type="text" primary style="margin-left:10px" @click="exportExcel">
            批量导出excel
          </el-button>
          <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;" class="el-icon-loading"></i>
        </div>
        <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                    @pagination="pagination"/>

      </div>
      <!-- 处置详情 -->
      <el-dialog title="处置详情" :visible.sync="dialog" width="30%">
        <div class="deal-detail">
          {{ detailParams.processText }}
        </div>
        <template #footer>
          <div style="text-align: center;">
            <el-button type="primary" @click="dialog=false">确定</el-button>
            <el-button @click="dialog = false">取消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {selectSpecialPage, exportSpecialPage, getProcessInfo} from "@/api/sentiment/index.js";
import {transImage} from '@/utils/index';
import {mulFilterInfo, getPlanType} from '@/api/system/filterMsg.js'
import {getTypeLists} from "@/api/publicOpinionMonitor/index.js";
import {updateEmotion, searchRead} from "@/api/search/index";
import {getFolderList, addBatch} from '@/api/report/material.js'

export default {
  name: "FilterMessage",
  data() {
    return {
      dialog: false,
      pickerOptionsStart: {
        disabledDate: (time) => {
          let endDateVal = this.queryParams.endTime;
          if (endDateVal) {
            const endDate = new Date(endDateVal);
            return time.getTime() > endDate.getTime() && time.toDateString() !== endDate.toDateString();
          }
        },
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          let beginDateVal = this.queryParams.startTime;
          if (beginDateVal) {
            const beginDate = new Date(beginDateVal);
            return time.getTime() < beginDate.getTime() && time.toDateString() !== beginDate.toDateString();
          }
        },
      },
      emotionData: [
        {name: '中性', value: 0},
        {name: '敏感', value: 1},
        {name: '非敏感', value: 2}
      ],
      msgOptions: [
        {name: '已读', value: 1},
        {name: '未读', value: 0},
      ],
      dealOptions: [
        {name: '未处置', value: 0},
        {name: '已处置', value: 1},
        {name: '已过期', value: 2}
      ],
      typeOptions: [],
      mediaList: [],
      planList: [],
      moduleOptions: [{dictLabel: '数据概览热词云', dictValue: 0}, {dictLabel: '方案热词云', dictValue: 1}],

      // 查询参数
      queryParams: {
        startTime: '',
        endTime: '',
        emotionFlag: '',
        isRead: '',
        type: '',
        content: '',
        processStatus: '',
        specialType: '1',
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      tableLoading: false,
      tableData: [],
      titleFixed: false,
      multipleSelection: {selectedRows: []},
      transImage,
      downloadLoading: false,
      alldownloadLoading: false,
      exportNum: null,
      checked: false,
      treeDataauto: [],
      detailParams: {}
    };
  },
  created() {
    this.getList();
    this.getDict()
    this.getTreeData()
  },
  methods: {
    // 查看处置详情
    openDialog(item) {
      this.dialog = true
      getProcessInfo(item.id).then((res) => {
        this.detailParams = res.data
      })
    },
    // 获取素材树
    async getTreeData() {
      let resauto = await getFolderList()
      this.treeDataauto = resauto.data
    },
    // 添加素材
    async subFolder(fold) {
      if (this.multipleSelection.selectedRows.length == 0) {
        this.$message({
          type: 'error',
          message: '请选择要导入的素材',
          duration: 1000
        });
        return
      }
      console.log(this.multipleSelection.selectedRows, 'this.multipleSelection.selectedRows');
      const newitem = JSON.parse(JSON.stringify(this.multipleSelection.selectedRows))
      let newParams = []
      newitem.map((item) => {
        newParams.push({
          contentId: item.indexId,
          folderId: fold.id,
          type: item.type,
          typeName: item.typeName,
          title: item.title,
          text: item.text,
          url: item.url,
          host: item.host,
          author: item.author,
          emotionFlag: item.emotionFlag,
          siteAreaCodeName: item.siteAreaCodeName,
          originFlag: item.originFlag,
          hitWords: item.hitWords,
          publishTime: item.publishTime
        })
      })
      const res = await addBatch(newParams)
      if (res.code == 200) {
        this.$message({
          type: 'success',
          message: '添加成功',
          duration: 1000
        });
      }
    },
    // 切换数据
    handleClick(val) {
      this.queryParams.pageNum = 1
      this.getList()
    },
    //table选中项改变
    handleSelectionChange(val) {
      this.multipleSelection.selectedRows = val
    },
    // 跳转详情页
    async goDetail(row) {
      // beforeTime.momentDay beforeTime.oneDay beforeTime.twoDay beforeTime.threeDay beforeTime.sevenDay
      const fullPath = this.$router.resolve({
        path: '/fullSearch/dataDetail',
        query: {id: row.indexId, planId: row.planId, keyWords: row.hitWords, time: row.publishTime, md5: row.md5}
      })
      window.open(fullPath.href, '_blank')
      if (row.isRead != 1) {
        this.updateIsRead(row.id)
        await searchRead({id: row.indexId})
      }


    },
    //更新阅读状态
    updateIsRead(id) {
      const foundItem = this.tableData.find(item => item.id === id);
      if (foundItem) {
        this.$set(foundItem, 'isRead', 1);
      }
    },
    // 切换敏感类型
    async changeSensitive(val, row) {
      let res = await updateEmotion({md5: row.md5, emotionFlag: val, indexId: row.indexId})
      this.$set(row, 'emotionFlag', val);
      if (res.code == 200) {
        this.$message({
          type: 'success',
          message: '操作成功',
          duration: 1000
        });
      } else {
        this.$set(row, 'emotionFlag', row.originFlag);
        this.$message.error(res.msg)
      }
    },
    getDict() {
      // 来源类型
      this.getDicts('sys_media_type').then(res => {
        this.mediaList = res.data
      })
    },
    //导出条数变动后的table选中项改变
    exportNumChange(val) {
      this.$refs.tableRef.clearSelection()
      if (val) {
        this.$refs.tableRef.toggleAllSelection()
      }
    },
    // 全选
    allSelect(val) {
      this.$refs.tableRef.clearSelection()
      if (val) {
        this.$refs.tableRef.toggleAllSelection()
      }
    },
    // 分页查询
    pagination(page) {
      this.queryParams.pageNum = page.page
      this.queryParams.pageSize = page.limit
      this.getList()
    },
    /** 查询菜单列表 */
    getList() {
      this.tableLoading = true;
      this.exportNum = null
      this.checked = false
      selectSpecialPage(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = Number(res.total)
        this.tableLoading = false;
      }).catch(err => {
        this.tableLoading = false;
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryParams");
      this.queryParams.endTime = ''
      this.handleQuery();
    },

    // 列表导出
    exportExcel(id) {
      let params = JSON.parse(JSON.stringify(this.queryParams))
      // params.type = params.type.join(',')
      const req = {...params}
      if (this.exportNum) { //选项导出
        if (this.exportNum !== '0') {
          req.pageNum = 1
          req.pageSize = parseInt(this.exportNum)
        }
      } else if (this.multipleSelection.selectedRows.length > 0) { //勾选导出
        const ids = this.multipleSelection.selectedRows.map(item => item.id)
        req.ids = ids
      } else {
        this.$message({
          type: 'warning',
          message: '请选择导出条数或导出项',
          duration: 1000
        });
        return
      }
      this.downloadLoading = true
      this.$confirm('是否确认导出所有类型数据项?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return exportSpecialPage(req);
      }).then(response => {
        this.downloadLoading = false
        this.$message({
          type: 'success',
          message: '导出成功',
          duration: 1000
        });
        this.download(response.msg);
      }).catch(() => {
        this.downloadLoading = false
      })
    },
  }
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #F4F7FB;
  height: calc(100vh - 80px);

  .navbar {
    background: #fff;
    padding: 20px;
    margin-bottom: 20px;
  }
}

@import './index.scss';
</style>
