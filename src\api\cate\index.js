import request from '@/utils/request'

// 信息类别列表
export function getInfoTypeList(data) {
  return request({
    url: '/type/typeList',
    method: 'post',
    data: data
  })
}

// 信息列表-新增/修改
export function AddTypeApi(data) {
  return request({
    url: '/type/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 信息列表-删除
export function delTypeApi(ids) {
  return request({
    url: '/type/delete/' + ids,
    method: 'delete',
    //   params: ids
  })
}
