<template>
  <div ref="barChart" class="chart"/>
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    chartData: {
      type: Object,
      default: () => {
      }
    },
    switchTheme: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.initChart()
    if (this.chart) {
      this.chart.on('click', (params) => {
        this.$emit('goToExpendDetail', '活跃账号', params.name, params.value)
      })
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.barChart)
      const option = {
        // backgroundColor: "#0f375f",
        grid: {
          top: '5%',
          left: '1%',
          right: '12%',
          bottom: '0%',
          containLabel: true
        },
        // barWidth: 10,
        xAxis: {
          type: 'value',
          name: '发文量',
          show: true,
          splitLine: {
            show: false,
            lineStyle: {
              // color: "rgba(255,255,255,0.2)",
              color: this.switchTheme ? 'rgba(255,255,255,0.2)' : '#333333',
              type: 'dashed',
              fontSize: '0.12rem'
            }
          },
          axisTick: {
            show: true
          },
          axisLine: { //  改变y轴颜色
            show: true,
            lineStyle: {
              // color: '#02F4FF'
              color: this.switchTheme ? '#02F4FF' : '#333333',
            }
          },
          axisLabel: { //  改变x轴字体颜色和大小
            // color: "#02F4FF",
            color: this.switchTheme ? '#02F4FF' : '#333333',
            fontSize: '0.12rem'
          }
        },
        yAxis: {
          type: 'category',
          data: this.chartData?.yyData?.slice(0, 5),
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: { //  改变y轴颜色
            lineStyle: {
              // color: '#02F4FF'
              color: this.switchTheme ? '#02F4FF' : '#333333',
            }
          },
          inverse: true,
          axisLabel: { //  改变y轴字体颜色和大小
            //formatter: '{value} m³ ', //  给y轴添加单位
            // color: "#02F4FF",
            color: this.switchTheme ? '#02F4FF' : '#333333',
            fontSize: '0.12rem'
          },
        },
        series: [{
          type: 'bar',
          name: "产出",
          // barWidth : 14,
          label: {
            show: true, //开启显示
            position: "inside", //在上方显示
            color: "#FFFFFF",
            fontSize: '0.12rem',
          },
          barBorderRadius: 0,
          itemStyle: {
            color: function (params) {
              var colorList = ['#FF0000', '#FF7F2D', '#f4b441', '#50b0f9', '#bcd3bb',
                '#1D80DA', '#02F4FF', '#E3BC2D', '#FF6632', '#A7FFB0',
              ];
              return colorList[params.dataIndex]
            }
          },
          data: this.chartData.xxData
        }]
      };
      this.chart.setOption(option, true)
      // if(this.chart){
      //   this.chart.on(
      //     'click',
      //     (params) => {
      //     // this.chart.off('click'); // 移除之前可能存在的监听器
      //     this.$emit('goToExpendDetail','活跃账号', params.name, params.value)
      //     })
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>

