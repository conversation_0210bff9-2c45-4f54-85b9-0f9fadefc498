<template>
  <div id="warnList" class="listBox" v-infinite-scroll="load" :infinite-scroll-disabled="disabled"
       infinite-scroll-distance="20"
       style="overflow:auto">
    <div class="listBoxTotal">
      信息数量 共<span>{{ total }}</span>条
    </div>
    <div>
      <ul>
        <li v-for="(item, index) in this.listData" :key="index" @click="goDetail(item)">
          <!-- <h3>{{ item.title }}</h3>
          <p v-html="item.content" class="pOne emRed"></p>
          <p class="pTwo">建议词:{{ item.suggestWord }}</p>
          <dl>
              <dt style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
                  {{ item.siteName }}
              </dt>
              <dd style="white-space: nowrap">{{ item.time }}</dd>
          </dl> -->


          <div class="tableTitle">
            <img class="tableItemImg" :src="transImage(item.type,item.host||'')" alt="无图片"/>
            <el-tooltip placement="top" effect="light" raw-content>
              <div slot="content">
                <div v-html="item.title"></div>
              </div>
              <div class="tableTitleSpan">
                <span>{{index+1}}. </span>
                <span v-html="item.title"></span>
              </div>
            </el-tooltip>
            <p class="article-type"
               :style="item.emotionFlag==2?'background-color: #a8dfef;color: #006e8f;':item.emotionFlag==1?'background-color: #fcc3c2;color: #b52626;':'background-color: #ffdc70;color: #8e5d00;'">
              {{item.emotionFlag==2?"非敏感":item.emotionFlag==1?"敏感":"中性"}}</p>
            <p class="article-type" style="background-color: #339593" v-show="item.isOriginal">原创</p>
            <p class="article-type" v-show="!item.isOriginal">转载</p>
            <p class="article-type" style="background-color: #F7B8B3;color: #FA2C1C;"
               v-show="item.warned==1">流转中</p>
            <p class="article-type" style="background-color: #B2E8F3;color: #00B4D8;" v-show="item.deal==1">
              已处置</p>

          </div>
          <div class="tableMain" v-html="item.text"></div>
          <div class="tableFoot">
            <div class="footButtonGroup">
              <div>
                <div class="footButonItem" v-show="item.hitWords">
                  <el-tooltip effect="light" content="涉及词" placement="top">
                    <img src="@/assets/images/keyword.png" alt="" class="footIcon"/>
                  </el-tooltip>
                  <el-tooltip effect="light" :content="item.hitWords" placement="top">
                    <span class="keyword">{{ item.hitWords || '' }}</span>
                  </el-tooltip>
                </div>
                <div class="footButonItem" v-show="item.hitCourtNames">
                  <el-tooltip effect="light" content="涉及法院" placement="top">
                    <img src="@/assets/images/court.png" alt="" class="footIcon"/>
                  </el-tooltip>
                  <el-tooltip effect="light" :content="item.hitCourtNames" placement="top">
                                        <span class="keyword" style="color: #247CFF;">{{ item.hitCourtNames || ''
                                            }}</span>
                  </el-tooltip>
                </div>
                <div class="footButonItem" v-show="item.contentAreaCodeName">
                  <el-tooltip effect="light" content="精准地域" placement="top">
                    <img src="@/assets/images/areaDetail.png" alt="" class="footIcon"/>
                  </el-tooltip>
                  <el-tooltip effect="light" :content="item.contentAreaCodeName" placement="top">
                                        <span class="keyword" style="color: #356391;">{{ item.contentAreaCodeName
                                            || '' }}</span>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </li>
      </ul>
      <!-- <div class="noMore">——没有更多了——</div> -->
      <p class="noMore" v-show="loading">加载中...</p>
      <p class="noMore" v-show="noMore && !this.loading">——没有更多了——</p>
    </div>
  </div>
</template>

<script>
import {getWarnListApi} from "@/api/warnList";
import {transImage} from '@/utils/index';

export default {
  data() {
    return {
      transImage,
      listData: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,

      baseUrlLocal: "",
      loading: false,
    };
  },
  computed: {
    noMore() {
      return this.listData.length >= this.total
    },
    disabled() {
      return this.loading || this.noMore
    }
  },
  created() {
    this.getwarnList();
  },
  methods: {
    load() {
      this.pageNum = this.pageNum + 1;
      this.getwarnList();
    },
    // 获取列表数据
    getwarnList() {
      this.loading = true;
      let params = {
        pushString: this.$route.query.p,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };

      getWarnListApi(params).then((res) => {
        this.loading = false;
        this.listData = [...this.listData, ...res.rows];
        this.total = res.total;
      }).then(() => {
        if (sessionStorage.getItem("scrollTop")) {
          let scrollTop = sessionStorage.getItem("scrollTop");
          document.querySelector("#warnList").scrollTop = scrollTop;
          sessionStorage.removeItem("scrollTop");
        }
      });
    },
    goDetail(item) {
      //存储滚动条位置
      sessionStorage.setItem(
        "scrollTop",
        document.querySelector("#warnList").scrollTop
      );
      // url转换为短链
      let publishTimeStr = item.publishTime.replace(/-/g, '').replace(/[\s:]/g, '');
      this.$router.push({
        path: "/phoneDetail",
        query: {
          id: item.articleId,
          planId: item.planId,
          publishTime: publishTimeStr,
          keyWord1:item.hitWords
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.listBox {
  width: 100%;
  height: calc(100vh);
  overflow: scroll;
  padding: 0 20px 40px 20px;
  box-sizing: border-box;

  .listBoxTotal {
    width: 100%;
    overflow: hidden;
    margin: 10px 0px;
    border-radius: 25px;
    border: solid 1px #ccc;
    box-shadow: 0px 0px 8px #ccc;
    height: 48px;
    line-height: 48px;
    text-align: center;

    span {
      display: inline-block;
      color: red;
    }
  }

  ul {
    width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;

    li {
      list-style: none;
      padding: 10px;
      box-sizing: border-box;
      border: solid 1px #ccc;
      box-shadow: 0px 0px 4px #ccc;
      margin-bottom: 10px;
      border-radius: 10px;

      h3 {
        font-size: 14px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-bottom: 10px;
        font-weight: 800;
      }

      p {
        margin: 0px;
        padding: 0;
        font-size: 14px;
        margin-bottom: 10px;
      }

      .pOne {
        color: #666;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .pTwo {
        color: #67c23a;
      }

      dl {
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-size: 14px;
      }


      .tableTitle {
        display: flex;
        align-items: center;

        ::v-deep em {
          color: #f46263;
        }

        .tableItemImg {
          width: 29px;
        }

        .tableItemImg,
        .tableTitleSpan {
          font-size: 16px;
          color: #333333;
          line-height: 22px;
          vertical-align: middle;
          margin-right: 14px;
        }

        .tableTitleSpan {
          display: inline-block;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          max-width: calc(100% - 150px);
          font-size: 16px;
          line-height: 22px;
          color: #333;
          cursor: pointer;
          font-weight: 600;
        }

        .article-type {
          margin: 0 0 0 10px;
          display: inline-block;
          padding: 2px 6px;
          background: #247CFF;
          font-size: 12px;
          color: #FFFFFF;
          line-height: 17px;
          border-radius: 2px;
          // width: 36px !important;
          width: fit-content;
          white-space: nowrap;
        }
      }

      .tableMain {
        margin: 9px 0 7px 0;
        position: relative;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        /* 控制显示的行数 */
        -webkit-box-orient: vertical;
        max-height: calc(2em * 2);
        /* 控制显示的高度 */
        overflow: hidden;
        font-size: 14px;
        line-height: 20px;
        color: #666;
        cursor: pointer;

        ::v-deep em {
          color: #f46263;
          font-style: normal
        }
      }

      .tableFoot {
        font-size: 12px;
        color: #666;

        .footInfo {
          display: inline-block;
          margin-right: 40px;
          font-size: 14px;
          color: #999;

          img {
            width: 16px;
            margin-right: 6px;
          }
        }

        .footButtonGroup {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .keyword {
            font-size: 12px;
            color: #F46263;
            line-height: 17px;
            max-width: 12em;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }

          .footButonItem {
            font-size: 12px;
            display: inline-block;
            margin-right: 20px;
            cursor: pointer;
            white-space: nowrap;
          }
        }

        img {
          width: 14px;
          margin-right: 3px;
        }

        img,
        span {
          display: inline-block;
          vertical-align: middle;
        }
      }
    }
  }

  .noMore {
    width: 100%;
    overflow: hidden;
    text-align: center;
    margin: 10px 0;
  }
}
</style>
