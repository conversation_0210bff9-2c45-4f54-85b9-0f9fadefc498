<template>
  <div class="info-style">
    <div class="head-label">
      <el-form :model="form" ref="form" :inline="true" label-width="100px" size="mini" label-position="right">
        <el-form-item label="文章标题" prop="articleTitle">
          <el-input v-model.trim="form.articleTitle" placeholder="请输入文章标题" clearable
                    @keyup.enter.native="handleQuery"></el-input>
        </el-form-item>
        <el-form-item label="信息类别" prop="infoType">
          <el-select v-model="form.infoType" placeholder="请选择信息类别" clearable filterable>
            <el-option v-for="item in typeList" :label="item.typeName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="媒体类型" prop="mediaType">
          <el-select v-model="form.mediaType" placeholder="请选择媒体类型" clearable>
            <el-option v-for="item in mediaList" :label="item.dictLabel" :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="涉事地域" prop="articleArea">
          <treeselect ref="treeSelect" style="width:194px;" noOptionsText="没有数据" noResultsText="暂无结果"
                      v-model="form.articleArea" :options="deptOptions" :normalizer="normalizer"
                      placeholder="请选择地域"/>
        </el-form-item>
        <el-form-item label="信息来源" prop="submitType">
          <el-select v-model="form.submitType" placeholder="请选择信息来源" clearable>
            <el-option v-for="item in sourceList" :label="item.dictLabel" :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="倾向性" prop="tendency">
          <el-select v-model="form.tendency" placeholder="请选择倾向性" clearable>
            <el-option v-for="item in tendencyList" :label="item.dictLabel" :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分发人" prop="managerUser">
          <el-select v-model="form.managerUser" placeholder="请选择分发人" clearable>
            <el-option v-for="item in managerList" :label="item.nick_name" :value="item.user_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="原文时间" prop="selectStartTime">
          <el-date-picker
            type="datetime"
            v-model="form.selectStartTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="startOption"
            @change="changeTime('start')"
            placeholder="开始日期">
          </el-date-picker>
          -
          <el-date-picker
            type="datetime"
            v-model="form.selectEndTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="endOption"
            @change="changeTime('end')"
            placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery" class="seStyle">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery" class="reStyle">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="contain-list">
      <div class="btns">
        <el-button type="primary" icon="el-icon-download" size="small" @click="exportSubmission">导出</el-button>
      </div>
      <div class="task-status">
        <ul>
          <li
            v-for="(item,index) in taskList"
            :key="item.dictValue"
            :class="taskIndex == index ? 'active' :'' "
            @click="toggleTask(index,item)"
          >{{item.dictLabel}}
          </li>
        </ul>
      </div>
      <div style="padding: 20px;background: #fff;">
        <el-table v-loading="loading" :data="infoLists" @selection-change="handleSelectionChange"
                  @sort-change="sortChange">
          <el-table-column type="selection" width="55" align="center"/>
          <el-table-column label="文章标题" prop="articleTitle" align="center" show-overflow-tooltip width="200">
            <template slot-scope="scope">
              <a
                style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;color: #409eff;cursor:pointer;"
                :href="scope.row.articleLink" target="_blank">{{ scope.row.articleTitle }}</a>
            </template>
          </el-table-column>
          <el-table-column label="原文时间" sortable prop="originalTime" align="center" width="150"
                           show-overflow-tooltip/>
          <el-table-column label="信息类别" prop="infoTypeName" align="center" show-overflow-tooltip/>
          <el-table-column label="媒体类型" prop="mediaType" align="center" width="80" :formatter="mediaFormat"/>
          <el-table-column label="涉事地域" prop="articleAreaName" align="center" show-overflow-tooltip/>
          <el-table-column label="信息来源" prop="submitType" align="center">
            <template slot-scope="scope">
              {{scope.row.submitType == 0 ? '分析报送' : '主动下发' }}
            </template>
          </el-table-column>
          <el-table-column label="倾向性" prop="tendency" align="center" width="65" :formatter="tendFormat"/>
          <el-table-column label="分发人" prop="dispenser" align="center"/>
          <el-table-column label="状态" prop="createType" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              {{scope.row.czState == 1 ? '待处置' : scope.row.czState == 2 ? '已处置': scope.row.czState == 3 ? '已转派'
              : scope.row.czState == 4 ? '已过期' : '已完结' }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" sortable prop="createTime" align="center" width="150"
                           show-overflow-tooltip/>
          <el-table-column label="操作" align="center" width="200px" fixed="right">
            <template slot-scope="scope">
              <div style="display:flex;justify-content: center;">
                <el-button type="text" style="color:#1178FF" icon="el-icon-finished"
                           v-if="scope.row.czState == 1&&canOperate(user.userId,scope.row.assigneer)"
                           @click="dealTask(scope.row)">处置
                </el-button>
                <el-button type="text" style="color:#1178FF" icon="el-icon-sort"
                           v-if="scope.row.czState == 1&&canOperate(user.userId,scope.row.assigneer)"
                           @click="transferTask(scope.row)">转派
                </el-button>
                <el-button type="text" style="color:#1178FF" icon="el-icon-search" @click="handleDetail(scope.row)">
                  查看详情
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <Pagination v-show="total>0" :limit.sync="form.pageSize" @pagination="getList" :page.sync="form.pageNum"
                    :currentPage="form.pageNum" :total="total"></Pagination>
      </div>
    </div>
    <!-- 转派 -->
    <TransferDialog ref="trans" @success="getList" :infoId="infoId" :dialogTransVisible="dialogTransVisible"
                    @cancelTrans="dialogTransVisible=false"></TransferDialog>
    <!-- 处置 -->
    <DealDialog ref="deal" @success="getList" :infoId="infoId" processResult="7" :dialogDealVisible="dialogDealVisible"
                @cancelDeal="dialogDealVisible=false"></DealDialog>
  </div>
</template>

<script>
import {getInfoDataApi, getAreaTreeApi, getInfoTypeList, getReceiver, exportList} from '@/api/infoSubmit/index'
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import TransferDialog from "../infoSubmit/components/TransferDialog.vue"
import DealDialog from "../infoSubmit/components/DealDialog.vue"
import {decryptByAES, encryptByAES} from '@/utils/jsencrypt'
import {canOperate} from '@/utils/index'

export default {
  name: 'InfoDeal',
  components: {Treeselect, TransferDialog, DealDialog},
  data() {
    return {
      canOperate,
      infoId: '',
      dialogTransVisible: false,
      dialogDealVisible: false,
      mediaList: [],
      tendencyList: [],
      deptOptions: [],
      typeList: [],
      managerList: [],
      transferList: [],
      startOption: {
        disabledDate: this.startDisable
      },
      endOption: {
        disabledDate: this.endDisable
      },
      form: {
        pageSize: 10,
        pageNum: 1,
        articleTitle: null,
        infoType: null,
        mediaType: null,
        articleArea: null,
        articleAreaName: null,
        submitType: null,
        tendency: null,
        managerUser: null,
        czState: -1,
        selectStartTime: '',
        selectEndTime: '',
      },
      total: 0,
      sourceList: [
        {dictValue: 0, dictLabel: '分析报送'},
        {dictValue: 1, dictLabel: '主动下发'}
      ],
      taskIndex: 0,
      taskList: [
        {dictValue: -1, dictLabel: '全部'},
        {dictValue: 1, dictLabel: '待处置'},
        {dictValue: 3, dictLabel: '已转派'},
        {dictValue: 2, dictLabel: '已处置'},
        {dictValue: 5, dictLabel: '已完结'},
        {dictValue: 4, dictLabel: '已过期'}
      ],
      infoLists: [],
      loading: false,
      formType: {
        remark: '',
        transferStaff: undefined,
        annex: ''
      },
      rules: {
        remark: [
          {required: true, message: "请输入评论", trigger: "blur"},
        ],
        transferStaff: [
          {required: true, message: "请选择转派人员", trigger: "blur"},
        ]
      },
      open: false,
      title: '',
      ids: []
    }
  },
  computed: {
    user() {
      return this.$store.getters.user
    }
  },
  created() {
    this.getDict()
    if (this.$route.query?.name) {
      this.form.articleTitle = this.$route.query?.name
      this.handleQuery()
    } else {
      this.getList()
    }
  },
  methods: {
    // 获取字典
    getDict() {
      // 媒体类型
      this.getDicts("sys_media_type").then(response => {
        this.mediaList = response.data
      })
      // 倾向性
      this.getDicts("tendency_type").then(response => {
        this.tendencyList = response.data
      })
      // 获取地域
      getAreaTreeApi().then(response => {
        this.deptOptions = response.data
      });
      // 信息类别
      const queryType = {
        typeName: '',
        pageQuery: false
      }
      // 加密
      if (this.$takeAES) {
        const formKeys = encryptByAES(JSON.stringify(queryType))
        getInfoTypeList({encryptJson: formKeys}).then(res => {
          this.typeList = JSON.parse(decryptByAES(res.encryptRows))
        })
        // 接收人员
        getReceiver().then(res => {
          this.managerList = JSON.parse(decryptByAES(res.data))
        })
      } else {
        getInfoTypeList(queryType).then(res => {
          this.typeList = res.rows
        })
        getReceiver().then(res => {
          this.managerList = res.data
        })
      }
    },
    // 字典翻译
    mediaFormat(row) {
      return this.selectDictLabel(this.mediaList, row.mediaType)
    },
    tendFormat(row) {
      return this.selectDictLabel(this.tendencyList, row.tendency)
    },
    // 地域转换
    normalizer(node) {
      if (node.children == null || node.children == 'null') {
        delete node.children
      }
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    // 人员转换
    transferUser(node) {
      if (node.children == null || node.children == 'null') {
        delete node.children
      }
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children
      }
    },
    // 开始日期规则
    startDisable(time) {
      return time.getTime() > new Date(this.form.selectEndTime).getTime()
    },
    // 结束日期规则
    endDisable(time) {
      return time.getTime() < new Date(this.form.selectStartTime).getTime()
    },
    changeTime(text) {
      const startTime = new Date(this.form.selectStartTime).getTime();
      const endTime = new Date(this.form.selectEndTime).getTime();
      if ((!this.form.selectStartTime && text === 'start') || (!this.form.selectEndTime && text === 'end')) {
        return; // 如果相应的时间还未选择，则直接返回
      }
      if ((text === 'start' && startTime > endTime) || (text === 'end' && startTime > endTime)) {
        if (text === 'start') {
          this.form.selectStartTime = '';
        } else {
          this.form.selectEndTime = '';
        }
        this.$message.error('请选择正确的时间！');
      }
    },
    // 搜索按钮操作
    handleQuery() {
      this.form.pageNum = 1
      this.getList()
    },
    // 表单重置
    reset() {
      this.form = {
        pageSize: 10,
        pageNum: 1,
        articleTitle: null,
        infoType: null,
        mediaType: null,
        articleArea: null,
        articleAreaName: null,
        tendency: null,
        managerUser: null,
        czState: this.form.czState,
        selectStartTime: '',
        selectEndTime: '',
      }
      this.resetForm('form')
    },
    // 重置按钮操作
    resetQuery() {
      this.reset()
      this.handleQuery()
    },
    // 任务类型切换
    toggleTask(index, item) {
      this.taskIndex = index
      this.form.czState = item.dictValue
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
    },
    // 获取列表信息
    async getList() {
      if ((this.form.selectStartTime && !this.form.selectEndTime) ||
        (!this.form.selectStartTime && this.form.selectEndTime)) {
        this.$message.error('请输入正确的时间范围');
        return false;
      }
      this.loading = true
      if (this.form.articleArea) {
        this.form.articleAreaName = this.$refs.treeSelect.getNode(this.$refs.treeSelect.value).nestedSearchLabel.replace(/\s/g, '')
      } else {
        this.form.articleAreaName = null
      }
      try {
        let formKeys;
        let res;
        if (this.$takeAES) {
          formKeys = encryptByAES(JSON.stringify(this.form));
          res = await getInfoDataApi({encryptJson: formKeys});
        } else {
          formKeys = JSON.stringify(this.form);
          res = await getInfoDataApi(formKeys);
        }
        // 处理API响应
        if (res.code === 200) {
          this.loading = false;
          this.total = res.total;
          // 根据是否需要AES解密来决定如何处理返回的行数据
          if (this.$takeAES) {
            this.infoLists = JSON.parse(decryptByAES(res.encryptRows));
          } else {
            this.infoLists = res.rows;
          }
        } else {
          this.loading = false;
        }
      } catch (error) {
        this.loading = false;
      }
    },
    // 处置
    dealTask(row) {
      this.infoId = row.id
      this.dialogDealVisible = true
      this.$refs.deal.resetParams()
    },
    // 转派
    transferTask(row) {
      this.infoId = row.id
      this.dialogTransVisible = true
      this.$refs.trans.resetParams()
    },
    // 查看详情
    handleDetail(row) {
      let routeUrl = this.$router.resolve({path: '/detail/detailSubmit', query: {id: row.id}})
      window.open(routeUrl.href, '_blank')
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.formType = {
        remark: '',
        transferStaff: undefined,
        annex: ''
      }
    },
    // 导出
    exportSubmission() {
      const queryParams = {...this.form, ids: this.ids}
      this.$confirm("是否确认导出所选的数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // 加密
        if (this.$takeAES) {
          const formKeys = encryptByAES(JSON.stringify(queryParams))
          return exportList({encryptJson: formKeys})
        } else {
          return exportList(queryParams)
        }
      }).then(response => {
        this.download(response.msg)
      })
    },
    // 排序
    sortChange(column) {
      const sortColumnMap = {
        originalTime: 'original_time',
        createTime: 'create_time',
      };
      this.form.sortColumn = sortColumnMap[column.prop];
      this.form.sortOrder = column.order === 'descending' ? 'desc' : 'asc';
      this.getList()
    },
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/info.scss';

a:hover {
  border-bottom: 1px solid #409eff;
}
</style>
