import request from '@/utils/request'

// 查询列表
export function getWordList(query) {
  return request({
    url: '/word/selectWordPage',
    method: 'get',
    params: query
  })
}

// 查询词库类型
export function getWordLib(data) {
  return request({
    url: '/word/selectWordLib',
    method: 'post',
    data
  })
}

// 新增
export function addSelectWord(data) {
  return request({
    url: '/word/insertWord',
    method: 'post',
    data: data
  })
}

// 修改
export function updateSelectWord(data) {
  return request({
    url: '/word/updateWordById',
    method: 'post',
    data: data
  })
}

// 删除
export function delWord(data) {
  return request({
    url: '/word/deleteWord',
    method: 'post',
    data
  })
}
