<template>
  <div class="template-fxbg">
    <img src="@/assets/images/simulatedVS/fxbgTopIcon.png" alt="">
    <div class="title">分析报告</div>
    <div class="text">{{ content }}</div>
    <img style="position: relative;top: 0.3em;" src="@/assets/images/simulatedVS/fxbgBottomIcon.png" alt="">
  </div>
</template>

<script>
export default {
  name: 'AnalysisReportTemplate',
  props: {
    content: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
.template-fxbg {
  font-size: 12em;
  background-color: #FFFFFF;
  margin: 0 10%;
  text-align: center;
  color: #000;

  img {
    width: 100%;
  }

  .title {
    font-size: 1.6em;
    text-align: center;
    font-weight: 600;
  }

  .text {
    text-indent: 2ch;
    /* 首行缩进2个字符 */
    line-height: 1.5em;
    text-align: start;
    word-wrap: break-word;
    white-space: pre-wrap;
    min-height: 30em;
    padding: 1em;
    font-size: 1.2em;
  }
}
</style>
