.info-style {
  background: #F7F8FA;
  min-height: calc(100vh - 154px);
  padding-top: 1px;
  padding-bottom: 30px;

  .head-label {
    background: #FFFFFF;
    padding-top: 28px;
    margin: 10px 20px 10px 20px;
  }

  .contain-list {
    // background: #FFFFFF;
    padding-top: 24px;
    padding-bottom: 30px;
    margin: 0px 20px;
    min-height: calc(100vh - 257px);

    .btns {
      background: #fff;
      padding: 16px;
    }

    .task-status {
      background: #fff;
      display: flex;
      align-items: center;
      margin-top: 16px;
      margin-bottom: 20px;
      padding-left: 16px;
      padding-top: 16px;

      ul {
        display: flex;
        justify-content: space-between;
        margin: 0px;
        padding: 0px;
        list-style: none;

        li {
          height: 40px;
          margin-right: 40px;
          line-height: 25px;
          text-align: center;
          font-size: 18px;
          color: #666;
          cursor: pointer;
        }
      }

      .active {
        color: #333;

        &::after {
          margin-top: 10px;
          display: block;
          content: '';
          height: 6px;
          background-color: #247CFF;
          border-radius: 3px;
        }
      }
    }
  }

}

::v-deep .el-dialog__header {
  padding: 20px;
  padding-bottom: 10px;
  background: #F5F9FF;
}
