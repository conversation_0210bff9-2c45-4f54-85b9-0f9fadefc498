import request from '@/utils/request'

// 列表
export function getReportPageList(params) {
  return request({
    url: `/report/selectReportPage`,
    method: 'get',
    params
  })
}

// 删除报告
export function deleteReport(id) {
  return request({
    url: `/report/deleteById/` + id,
    method: 'delete',
  })
}

// 修改报告
export function updateReport(data) {
  return request({
    url: `/report/updateReport`,
    method: 'post',
    data
  })
}

// 上传报告

export function uploadReport(data) {
  return request({
    url: `/report/insertReport`,
    method: 'post',
    data
  })
}

// 下载报告
export function downReport(id) {
  return request({
    url: `/report/downloadReport/` + id,
    method: 'get',
  })
}

// 查看详情
export function detailReport(id) {
  return request({
    url: `/report/selectById/` + id,
    method: 'get',
    headers: {
      isToken: false
    },
  })
}
